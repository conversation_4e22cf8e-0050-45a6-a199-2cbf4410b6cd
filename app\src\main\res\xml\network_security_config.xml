<?xml version="1.0" encoding="utf-8"?>
<network-security-config>
    <!-- 允许所有明文HTTP流量 -->
    <base-config cleartextTrafficPermitted="true">
        <trust-anchors>
            <!-- 信任系统预装的CA证书 -->
            <certificates src="system" />
            <!-- 信任用户添加的CA证书 -->
            <certificates src="user" />
        </trust-anchors>
    </base-config>
    
    <!-- 为我们的API服务器添加特殊配置 -->
    <domain-config cleartextTrafficPermitted="true">
        <domain includeSubdomains="true">qcx.yuneyang.top</domain>
        <trust-anchors>
            <certificates src="system" />
            <certificates src="user" />
        </trust-anchors>
    </domain-config>
    
    <!-- 本地开发服务器配置 -->
    <domain-config cleartextTrafficPermitted="true">
        <domain includeSubdomains="true">localhost</domain>
        <domain includeSubdomains="true">********</domain>
        <domain includeSubdomains="true">127.0.0.1</domain>
        <trust-anchors>
            <certificates src="system" />
            <certificates src="user" />
        </trust-anchors>
    </domain-config>

    <!-- 模拟器专用配置 - 信任所有证书 -->
    <debug-overrides>
        <trust-anchors>
            <certificates src="system" />
            <certificates src="user" />
        </trust-anchors>
    </debug-overrides>
</network-security-config> 