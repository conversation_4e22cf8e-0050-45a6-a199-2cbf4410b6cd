<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.example.aihealthbutler">

    <!-- 相关应用权限 -->
    <!-- 网络 -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <!-- 存储 -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" 
                     android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>
    <!-- 相机 -->   
    <uses-permission android:name="android.permission.CAMERA"
        tools:ignore="PermissionImpliesUnsupportedChromeOsHardware" />
    <!-- 麦克风 -->
    <uses-permission android:name="android.permission.RECORD_AUDIO"/>
    <!-- OPPO设备特殊权限 -->
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS"/>
    <uses-permission android:name="android.permission.WAKE_LOCK"/>
    <!-- 位置 -->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION"/>
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION"/>
    
    <!-- 如果需要在Android 15上获取连接信息，需要添加此权限 -->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <application
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.AIHealthButler"
        android:networkSecurityConfig="@xml/network_security_config"
        android:usesCleartextTraffic="true"
        android:enableOnBackInvokedCallback="true"
        tools:targetApi="34">

        <!--
        allowBackup：允许应用程序参与数据备份和恢复；
        dataExtractionRules：XML文件，定义应用程序数据提取的规则；
        fullBackupContent：定义应用程序全量备份的内容规则；
        icon：设置应用程序的图标；
        label：应用程序的显示名称；
        roundIcon：设置应用程序的圆形图标；
        android:usesCleartextTraffic="true"：HTTP明文流量限制
        -->
                                                                                        
        <!-- 相关服务 -->
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.provider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

<!--登录界面为起始页-->
        <!-- 登录页面 -->
        <activity
            android:name=".SignInActivity"
            android:exported="true"
            android:label="@string/app_name"
            android:theme="@style/Theme.AIHealthButler">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>



        <!-- 对话界面 -->
        <activity
            android:name=".ConversationInterface"
            android:exported="true"
            android:label="@string/app_name"
            android:theme="@style/Theme.AIHealthButler">
        </activity>

<!--        &lt;!&ndash; 对话界面 &ndash;&gt;-->
<!--        <activity-->
<!--            android:name=".ConversationInterface"-->
<!--            android:exported="true"-->
<!--            android:theme="@style/Theme.AIHealthButler" />-->

<!--我的界面为起始页-->

    <!--登录页面 -->
<!--        <activity-->
<!--            android:name=".SignInActivity"-->
<!--            android:exported="true"-->
<!--            android:theme="@style/Theme.AIHealthButler" />-->

<!--        &lt;!&ndash; 我的页面 &ndash;&gt;-->
<!--        <activity-->
<!--            android:name=".MainActivity"-->
<!--            android:exported="true">-->
<!--            <intent-filter>-->
<!--                <action android:name="android.intent.action.MAIN" />-->
<!--                <category android:name="android.intent.category.LAUNCHER" />-->
<!--            </intent-filter>-->
<!--        </activity>-->


<!-- 用户协议界面为首界面 -->
        <!-- 用户协议界面 -->
<!--        <activity-->
<!--            android:name=".FirstUserPopupActivity"-->
<!--            android:exported="true">-->
<!--            <intent-filter>-->
<!--                <action android:name="android.intent.action.MAIN" />-->
<!--                <category android:name="android.intent.category.LAUNCHER" />-->
<!--            </intent-filter>-->
<!--        </activity>-->

<!--        &lt;!&ndash;登录页面 &ndash;&gt;-->
<!--        <activity-->
<!--            android:name=".SignInActivity"-->
<!--            android:exported="true"-->
<!--            android:theme="@style/Theme.AIHealthButler" />-->

<!--        &lt;!&ndash; 我的页面 &ndash;&gt;-->
<!--        <activity-->
<!--            android:name=".MainActivity"-->
<!--            android:exported="true"-->
<!--            android:theme="@style/Theme.AIHealthButler" />-->

        <!-- 我的页面 -->
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:theme="@style/Theme.AIHealthButler" />

        <!-- 用药计划页面 -->
        <activity
            android:name=".MedicationPlanActivity"
            android:exported="true"
            android:theme="@style/Theme.AIHealthButler" />

        <!-- 新增用药计划 -->
        <activity
            android:name=".AddnewmedicationplanActivity"
            android:exported="true"
            android:theme="@style/Theme.AIHealthButler" />

        <!-- 运动建议 -->
        <activity
            android:name=".ExerciseAdviceActivity"
            android:exported="true"
            android:theme="@style/Theme.AIHealthButler" />

        <!-- 饮食建议 -->
        <activity
            android:name=".DietaryAdviceActivity"
            android:exported="true"
            android:theme="@style/Theme.AIHealthButler" />

        <!-- 个人信息 -->
        <activity
            android:name=".PersonalInformationActivity"
            android:exported="true"
            android:theme="@style/Theme.AIHealthButler" />

        <!-- 新增家庭成员 -->
        <activity
            android:name=".AddNewFamilyMember"
            android:exported="true"
            android:theme="@style/Theme.AIHealthButler" />



        <!-- 资料夹主界面 -->
        <activity
            android:name=".DocumentFolderActivity"
            android:exported="true"
            android:theme="@style/Theme.AIHealthButler" />

        <!-- 上传资料 -->
        <activity
            android:name=".UploadMaterialsActivity"
            android:exported="true"
            android:theme="@style/Theme.AIHealthButler" />

        <!-- 健康史 -->
        <activity
            android:name=".HealthHistoryActivity"
            android:exported="false"
            android:screenOrientation="portrait" />

        <!-- 权限设置 -->
        <activity
            android:name=".PermissionsManagerActivity"
            android:exported="true"
            android:theme="@style/Theme.AIHealthButler" />

        <!-- 家庭成员切换 -->
        <activity
            android:name=".FamilyMemberSwitchActivity"
            android:exported="true"
            android:theme="@style/Theme.AIHealthButler" />

        <!-- 邀请二维码生成 -->
        <activity
            android:name=".InviteQRCodeActivity"
            android:exported="true"
            android:theme="@style/Theme.AIHealthButler" />

        <!-- 二维码扫描 -->
        <activity
            android:name=".QRScannerActivity"
            android:exported="true"
            android:theme="@style/Theme.AIHealthButler" />

        <!-- 添加成员确认 -->
        <activity
            android:name=".AddMemberConfirmActivity"
            android:exported="true"
            android:theme="@style/Theme.AIHealthButler" />

        <!-- 关系设置 -->
        <activity
            android:name=".RelationshipSettingActivity"
            android:exported="true"
            android:theme="@style/Theme.AIHealthButler" />

        <!-- 网络调试工具 -->
        <activity
            android:name=".debug.NetworkDebugActivity"
            android:exported="false"
            android:theme="@style/Theme.AIHealthButler" />

    </application>
</manifest>