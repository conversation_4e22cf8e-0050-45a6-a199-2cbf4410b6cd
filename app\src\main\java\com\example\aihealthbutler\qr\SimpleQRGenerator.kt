package com.example.aihealthbutler.qr

import android.graphics.Bitmap
import android.graphics.Color
import android.util.Log
import com.google.zxing.BarcodeFormat
import com.google.zxing.EncodeHintType
import com.google.zxing.qrcode.QRCodeWriter
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel

/**
 * 真正的二维码生成器（使用ZXing库）
 */
object SimpleQRGenerator {
    private const val TAG = "SimpleQRGenerator"
    
    /**
     * 生成真正的二维码
     */
    fun generateQRBitmap(
        content: String,
        size: Int = 512
    ): Bitmap {
        return try {
            Log.d(TAG, "生成真正的二维码，内容: $content")

            // 配置二维码参数
            val hints = hashMapOf<EncodeHintType, Any>().apply {
                put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.M)
                put(EncodeHintType.CHARACTER_SET, "UTF-8")
                put(EncodeHintType.MARGIN, 1)
            }

            // 使用ZXing生成二维码
            val writer = QRCodeWriter()
            val bitMatrix = writer.encode(content, BarcodeFormat.QR_CODE, size, size, hints)

            // 转换为Bitmap
            val bitmap = Bitmap.createBitmap(size, size, Bitmap.Config.RGB_565)
            for (x in 0 until size) {
                for (y in 0 until size) {
                    bitmap.setPixel(x, y, if (bitMatrix[x, y]) Color.BLACK else Color.WHITE)
                }
            }

            Log.d(TAG, "真正的二维码生成成功")
            bitmap
        } catch (e: Exception) {
            Log.e(TAG, "生成二维码失败", e)
            createErrorBitmap(size)
        }
    }

    /**
     * 创建错误位图
     */
    private fun createErrorBitmap(size: Int): Bitmap {
        val bitmap = Bitmap.createBitmap(size, size, Bitmap.Config.RGB_565)
        bitmap.eraseColor(Color.WHITE)
        return bitmap
    }
}
