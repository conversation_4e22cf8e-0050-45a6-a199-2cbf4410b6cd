package com.example.aihealthbutler.speech

import android.content.Context
import kotlinx.coroutines.flow.StateFlow

/**
 * 语音识别引擎接口
 * 支持多种语音识别服务的统一接口
 */
interface SpeechRecognitionEngine {
    
    /**
     * 初始化引擎
     */
    suspend fun initialize(): Boolean
    
    /**
     * 开始语音识别
     */
    fun startListening()
    
    /**
     * 停止语音识别
     */
    fun stopListening()
    
    /**
     * 取消语音识别
     */
    fun cancelListening()
    
    /**
     * 检查是否可用
     */
    fun isAvailable(): Boolean
    
    /**
     * 检查是否正在监听
     */
    fun isListening(): Boolean
    
    /**
     * 获取识别状态
     */
    val state: StateFlow<SpeechToTextState>
    
    /**
     * 获取识别结果
     */
    val result: StateFlow<SpeechToTextResult>
    
    /**
     * 获取错误信息
     */
    val errorMessage: StateFlow<String>
    
    /**
     * 释放资源
     */
    fun release()
    
    /**
     * 重置状态
     */
    fun reset()
    
    /**
     * 获取引擎名称
     */
    fun getEngineName(): String
    
    /**
     * 获取引擎描述
     */
    fun getEngineDescription(): String
}

/**
 * 语音识别引擎类型
 */
enum class SpeechEngineType {
    GOOGLE,      // Google语音服务
    XUNFEI,      // 讯飞语音
    BAIDU,       // 百度语音
    VOSK,        // Vosk离线引擎
    SYSTEM       // 系统默认
}

/**
 * 语音识别状态
 */
enum class SpeechToTextState {
    IDLE,           // 空闲
    INITIALIZING,   // 初始化中
    LISTENING,      // 监听中
    PROCESSING,     // 处理中
    SUCCESS,        // 成功
    ERROR           // 错误
}

/**
 * 语音识别结果
 */
data class SpeechToTextResult(
    val text: String = "",
    val confidence: Float = 0f,
    val isPartial: Boolean = false,
    val alternatives: List<String> = emptyList()
)

/**
 * 语音识别配置
 */
data class SpeechRecognitionConfig(
    val language: String = "zh-CN",
    val enablePartialResults: Boolean = true,
    val maxResults: Int = 5,
    val timeoutMs: Long = 30000,
    val enableOffline: Boolean = false,
    val enablePunctuation: Boolean = true
)
