package com.example.aihealthbutler

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Check
import androidx.compose.material.icons.filled.Person
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.lifecycle.viewmodel.compose.viewModel
import com.example.aihealthbutler.api.Repository
import com.example.aihealthbutler.api.SessionManager
import com.example.aihealthbutler.api.SetFamilyRelationshipRequest
import com.example.aihealthbutler.api.PersonInfoRequest
import com.example.aihealthbutler.ui.theme.AIHealthButlerTheme
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.launch

/**
 * 关系设置页面
 */
class RelationshipSettingActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        
        // 获取成员数据
        val memberData = intent.getSerializableExtra("member_data") as? AddedMemberData
        
        if (memberData == null) {
            finish()
            return
        }
        
        setContent {
            AIHealthButlerTheme {
                val sessionManager = remember { SessionManager(applicationContext) }
                val repository = remember { Repository() }
                val viewModel = remember { RelationshipSettingViewModel(repository, sessionManager) }
                
                RelationshipSettingScreen(
                    memberData = memberData,
                    viewModel = viewModel,
                    onBack = { finish() },
                    onComplete = {
                        // 检查是否来自二维码扫描
                        val fromQRScan = intent.getBooleanExtra("from_qr_scan", false)
                        if (fromQRScan) {
                            // 设置成功结果并返回
                            setResult(Activity.RESULT_OK)
                            finish()
                        } else {
                            // 原有逻辑：直接返回主页面
                            Toast.makeText(this@RelationshipSettingActivity, "添加家庭成员成功", Toast.LENGTH_SHORT).show()
                            val intent = Intent(this@RelationshipSettingActivity, MainActivity::class.java)
                            intent.flags = Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_NEW_TASK
                            startActivity(intent)
                            finish()
                        }
                    }
                )
            }
        }
    }
}

/**
 * 关系设置ViewModel
 */
class RelationshipSettingViewModel(
    private val repository: Repository,
    private val sessionManager: SessionManager
) : ViewModel() {
    private val TAG = "RelationshipSettingVM"
    
    private val _uiState = MutableStateFlow(RelationshipSettingUiState())
    val uiState: StateFlow<RelationshipSettingUiState> = _uiState
    
    // 可选的家庭关系
    val availableRelationships = listOf(
        "父亲", "母亲", "儿子", "女儿", 
        "丈夫", "妻子", "兄弟", "姐妹",
        "爷爷", "奶奶", "外公", "外婆",
        "孙子", "孙女", "其他"
    )
    
    /**
     * 设置家庭关系和个人信息
     */
    fun setRelationship(memberData: AddedMemberData, relationship: String) {
        viewModelScope.launch {
            try {
                Log.d(TAG, "开始设置成员信息和关系")
                _uiState.value = _uiState.value.copy(isLoading = true, error = null)

                val token = sessionManager.token.firstOrNull() ?: throw Exception("未登录")
                val currentDefaultId = sessionManager.defaultId.firstOrNull() ?: throw Exception("获取当前用户ID失败")

                // 1. 首先设置个人信息（使用扫码用户的信息）
                Log.d(TAG, "设置个人信息: ${memberData.name}")
                val personInfoRequest = PersonInfoRequest(
                    name = memberData.name,
                    sex = 1, // 默认设置为男性，实际应用中可以从扫码数据获取
                    birthday = "1990-01-01", // 默认生日，实际应用中可以从扫码数据获取
                    height = 170, // 默认身高
                    weight = 65.0f // 默认体重
                )

                val updatePersonResult = repository.updatePersonInfo(token, memberData.defaultId, personInfoRequest)
                if (updatePersonResult.isFailure) {
                    throw Exception("设置个人信息失败: ${updatePersonResult.exceptionOrNull()?.message}")
                }

                Log.d(TAG, "个人信息设置成功，开始设置家庭关系")

                // 2. 设置家庭关系
                val relationshipRequest = SetFamilyRelationshipRequest(
                    fromMemberId = memberData.defaultId,
                    toMemberId = currentDefaultId,
                    relationship = relationship
                )

                Log.d(TAG, "设置关系: ${memberData.defaultId} 是 $currentDefaultId 的 $relationship")

                val relationshipResult = repository.setFamilyRelationship(token, relationshipRequest)

                relationshipResult.fold(
                    onSuccess = { response ->
                        Log.d(TAG, "家庭关系设置成功")
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            isCompleted = true,
                            error = null
                        )
                    },
                    onFailure = { e ->
                        Log.e(TAG, "设置家庭关系失败", e)
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            error = "设置关系失败: ${e.message}"
                        )
                    }
                )
            } catch (e: Exception) {
                Log.e(TAG, "设置成员信息失败", e)
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "设置失败: ${e.message}"
                )
            }
        }
    }
}

/**
 * UI状态
 */
data class RelationshipSettingUiState(
    val isLoading: Boolean = false,
    val isCompleted: Boolean = false,
    val error: String? = null
)

/**
 * 关系设置界面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun RelationshipSettingScreen(
    memberData: AddedMemberData,
    viewModel: RelationshipSettingViewModel,
    onBack: () -> Unit,
    onComplete: () -> Unit
) {
    val uiState by viewModel.uiState.collectAsState()
    var selectedRelationship by remember { mutableStateOf<String?>(null) }
    
    Scaffold(
        topBar = {
            CenterAlignedTopAppBar(
                title = {
                    Text(
                        "设置家庭关系",
                        fontSize = 20.sp,
                        fontWeight = FontWeight.Bold
                    )
                },
                navigationIcon = {
                    IconButton(onClick = onBack) {
                        Icon(
                            painter = painterResource(id = R.drawable.left_arrow),
                            contentDescription = "返回",
                            modifier = Modifier.size(24.dp)
                        )
                    }
                },
                colors = TopAppBarDefaults.centerAlignedTopAppBarColors(
                    containerColor = Color.White,
                    titleContentColor = Color.Black,
                    navigationIconContentColor = Color.Black
                )
            )
        },
        containerColor = Color.White
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .background(Color.White)
                .padding(paddingValues)
                .verticalScroll(rememberScrollState())
                .padding(16.dp)
        ) {
            // 成员信息
            MemberInfoCard(memberData = memberData)
            
            Spacer(modifier = Modifier.height(24.dp))
            
            // 关系选择提示
            Text(
                text = "请选择该成员与您的关系：",
                fontSize = 16.sp,
                fontWeight = FontWeight.Bold,
                color = Color.Black
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 关系选择列表
            RelationshipGrid(
                relationships = viewModel.availableRelationships,
                selectedRelationship = selectedRelationship,
                onRelationshipSelected = { selectedRelationship = it }
            )
            
            Spacer(modifier = Modifier.height(32.dp))
            
            // 确认按钮
            Button(
                onClick = {
                    selectedRelationship?.let { relationship ->
                        viewModel.setRelationship(memberData, relationship)
                    }
                },
                modifier = Modifier.fillMaxWidth(),
                enabled = selectedRelationship != null && !uiState.isLoading,
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color(0xFF22C1C3)
                )
            ) {
                if (uiState.isLoading) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(16.dp),
                        color = Color.White,
                        strokeWidth = 2.dp
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                }
                Text("确认设置")
            }
            
            // 错误提示
            uiState.error?.let { error ->
                Spacer(modifier = Modifier.height(16.dp))
                Card(
                    colors = CardDefaults.cardColors(containerColor = Color(0xFFFFEBEE)),
                    shape = RoundedCornerShape(8.dp)
                ) {
                    Text(
                        text = error,
                        color = Color.Red,
                        modifier = Modifier.padding(16.dp)
                    )
                }
            }
        }
    }
    
    // 处理完成
    LaunchedEffect(uiState.isCompleted) {
        if (uiState.isCompleted) {
            onComplete()
        }
    }
}

/**
 * 成员信息卡片
 */
@Composable
private fun MemberInfoCard(memberData: AddedMemberData) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = Color(0xFFF8F9FA)),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        shape = RoundedCornerShape(12.dp)
    ) {
        Row(
            modifier = Modifier.padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 头像
            Card(
                modifier = Modifier.size(60.dp),
                colors = CardDefaults.cardColors(containerColor = Color(0xFF22C1C3)),
                shape = RoundedCornerShape(30.dp)
            ) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = Icons.Default.Person,
                        contentDescription = "用户头像",
                        tint = Color.White,
                        modifier = Modifier.size(30.dp)
                    )
                }
            }

            Spacer(modifier = Modifier.width(16.dp))

            // 用户信息
            Column {
                Text(
                    text = memberData.name,
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.Black
                )
                Spacer(modifier = Modifier.height(4.dp))
                Text(
                    text = "账号: ${memberData.account}",
                    fontSize = 14.sp,
                    color = Color.Gray
                )
            }
        }
    }
}

/**
 * 关系选择网格
 */
@Composable
private fun RelationshipGrid(
    relationships: List<String>,
    selectedRelationship: String?,
    onRelationshipSelected: (String) -> Unit
) {
    // 使用网格布局，每行3个
    val chunkedRelationships = relationships.chunked(3)

    Column {
        chunkedRelationships.forEach { rowRelationships ->
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                rowRelationships.forEach { relationship ->
                    RelationshipItem(
                        relationship = relationship,
                        isSelected = relationship == selectedRelationship,
                        onSelected = { onRelationshipSelected(relationship) },
                        modifier = Modifier.weight(1f)
                    )
                }

                // 填充空白位置
                repeat(3 - rowRelationships.size) {
                    Spacer(modifier = Modifier.weight(1f))
                }
            }

            Spacer(modifier = Modifier.height(12.dp))
        }
    }
}

/**
 * 关系选择项
 */
@Composable
private fun RelationshipItem(
    relationship: String,
    isSelected: Boolean,
    onSelected: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .clickable { onSelected() },
        colors = CardDefaults.cardColors(
            containerColor = if (isSelected) Color(0xFF22C1C3) else Color.White
        ),
        border = if (!isSelected) {
            androidx.compose.foundation.BorderStroke(1.dp, Color(0xFFE0E0E0))
        } else null,
        elevation = CardDefaults.cardElevation(
            defaultElevation = if (isSelected) 4.dp else 1.dp
        ),
        shape = RoundedCornerShape(8.dp)
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            contentAlignment = Alignment.Center
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                if (isSelected) {
                    Icon(
                        imageVector = Icons.Default.Check,
                        contentDescription = "已选择",
                        tint = Color.White,
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                }
                Text(
                    text = relationship,
                    fontSize = 14.sp,
                    color = if (isSelected) Color.White else Color.Black,
                    fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Normal
                )
            }
        }
    }
}
