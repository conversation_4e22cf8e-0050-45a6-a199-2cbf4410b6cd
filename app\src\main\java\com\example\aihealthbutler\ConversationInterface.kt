package com.example.aihealthbutler

import android.Manifest
import android.app.Activity
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Bundle
import android.speech.RecognizerIntent
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import kotlinx.coroutines.delay
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalClipboardManager
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.content.ContextCompat
import com.example.aihealthbutler.ui.theme.AIHealthButlerTheme
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.*
import android.util.Log
import androidx.compose.foundation.Image
import androidx.compose.foundation.border
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.input.TextFieldValue
import java.util.concurrent.TimeUnit
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.ui.input.pointer.pointerInput
import coil.compose.AsyncImage
import coil.request.ImageRequest
import androidx.lifecycle.lifecycleScope
import androidx.compose.runtime.mutableStateOf
import com.example.aihealthbutler.dify.*
import com.example.aihealthbutler.utils.NetworkUtils
import com.example.aihealthbutler.debug.NetworkDebugActivity
import com.example.aihealthbutler.speech.SpeechToTextState
import com.example.aihealthbutler.speech.SpeechToTextResult
import com.example.aihealthbutler.utils.OppoPermissionHelper
import java.io.IOException

class ConversationInterface : ComponentActivity() {
    // 语音录制管理器
    private lateinit var audioRecorderManager: AudioRecorderManager

    // 语音转文字管理器
    private lateinit var speechToTextManager: SpeechToTextManager

    // Dify API客户端
    private lateinit var difyApiClient: DifyApiClient
    private lateinit var difyFileManager: DifyFileManager
    private lateinit var difyStreamHandler: DifyStreamHandler

    // 用于请求录音权限
    private val requestPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        if (isGranted) {
            // 权限已授予，启动语音识别
            startSpeechRecognition()
        } else {
            Toast.makeText(this, "需要麦克风权限才能使用语音输入", Toast.LENGTH_SHORT).show()
        }
    }
    
    // 图片选择结果
    private val pickImageLauncher = registerForActivityResult(
        ActivityResultContracts.GetContent()
    ) { uri: Uri? ->
        uri?.let {
            // 存储选择的图片URI
            selectedImageUri = it
            Toast.makeText(this, "已选择图片，可以添加文字描述后发送", Toast.LENGTH_SHORT).show()
        }
    }
    
    // 存储选中的图片URI
    private var selectedImageUri: Uri? = null
    
    // 启动语音识别
    private fun startSpeechRecognition() {
        val intent = Intent(RecognizerIntent.ACTION_RECOGNIZE_SPEECH).apply {
            putExtra(RecognizerIntent.EXTRA_LANGUAGE_MODEL, RecognizerIntent.LANGUAGE_MODEL_FREE_FORM)
            putExtra(RecognizerIntent.EXTRA_LANGUAGE, Locale.getDefault())
            putExtra(RecognizerIntent.EXTRA_PROMPT, "请说话...")
        }
        
        try {
            startActivityForResult(intent, SPEECH_REQUEST_CODE)
        } catch (e: Exception) {
            Toast.makeText(this, "您的设备不支持语音识别", Toast.LENGTH_SHORT).show()
        }
    }
    
    // 处理语音识别结果
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        
        if (requestCode == SPEECH_REQUEST_CODE && resultCode == Activity.RESULT_OK) {
            val results = data?.getStringArrayListExtra(RecognizerIntent.EXTRA_RESULTS)
            val spokenText = results?.get(0) ?: ""
            
            // 将识别到的文本设置到输入框
            recognizedText = spokenText
        }
    }
    
    // 存储识别的文本
    private var recognizedText: String = ""
        set(value) {
            field = value
            // 当语音识别文本更新时，更新UI的状态
            speechTextState.value = value
        }
    
    // 用于更新UI的状态
    private val speechTextState = mutableStateOf("")

    // 语音转文字状态
    private val speechToTextState = mutableStateOf(SpeechToTextState.IDLE)
    private val speechToTextResult = mutableStateOf(SpeechToTextResult())
    private val speechToTextError = mutableStateOf("")

    // Dify相关状态
    private val currentConversationId = mutableStateOf<String?>(null)
    private val isStreamingResponse = mutableStateOf(false)
    
    // 检查和请求麦克风权限（OPPO设备特殊处理）
    private fun checkAndRequestMicPermission() {
        val permissionStatus = OppoPermissionHelper.checkRecordAudioPermission(this)
        when (permissionStatus) {
            OppoPermissionHelper.PermissionStatus.GRANTED -> {
                // 已有权限，直接启动语音识别
                startSpeechRecognition()
            }
            OppoPermissionHelper.PermissionStatus.OPPO_SPECIAL_HANDLING_NEEDED -> {
                // OPPO设备特殊处理
                Toast.makeText(this, "检测到OPPO设备，需要手动设置权限", Toast.LENGTH_LONG).show()
                OppoPermissionHelper.requestRecordAudioPermission(this)
            }
            OppoPermissionHelper.PermissionStatus.DENIED -> {
                // 请求权限
                requestPermissionLauncher.launch(Manifest.permission.RECORD_AUDIO)
            }
        }
    }

    // 重试语音转文字功能
    private fun retrySpeechToText() {
        lifecycleScope.launch {
            try {
                // 重新初始化语音识别器
                speechToTextManager.reinitialize()

                // 等待初始化完成
                delay(1000)

                // 检查是否可用
                if (speechToTextManager.isAvailable()) {
                    speechToTextManager.startSpeechToText()
                } else {
                    speechToTextManager.setError("重试失败，请检查设备设置")
                }
            } catch (e: Exception) {
                speechToTextManager.setError("重试时发生错误: ${e.message}")
            }
        }
    }
    
    // 发送消息（包含文本和/或图片）
    private fun sendMessage(text: String, imageUri: Uri?) {
        // 创建消息对象
        val messageType = when {
            text.isNotEmpty() && imageUri != null -> MessageType.TEXT_WITH_IMAGE
            imageUri != null -> MessageType.IMAGE
            else -> MessageType.TEXT
        }

        val message = Message(
            content = text,
            isFromUser = true,
            imageUri = imageUri,
            messageType = messageType
        )

        // 添加到消息列表
        messagesList.add(message)

        // 清空当前选中的图片
        selectedImageUri = null

        // 发送文本到Dify API并获取回复
        if (text.isNotEmpty()) {
            lifecycleScope.launch {
                sendDifyChatMessage(text, imageUri)
            }
        } else if (imageUri != null) {
            // 如果只有图片没有文本，发送图片分析请求
            lifecycleScope.launch {
                sendDifyChatMessage("请分析这张图片", imageUri)
            }
        }
    }

    // 发送语音消息
    private fun sendVoiceMessage(audioFile: java.io.File, duration: Long) {
        val message = Message(
            content = "",
            isFromUser = true,
            audioFile = audioFile,
            audioDuration = duration,
            messageType = MessageType.AUDIO
        )

        // 添加到消息列表
        messagesList.add(message)

        // 可以添加AI回复
        messagesList.add(Message("我已收到您的语音消息", false))
    }

    // 发送语音转文字消息
    private fun sendSpeechToTextMessage(text: String) {
        if (text.isNotEmpty()) {
            sendMessage(text, null)
            // 清空语音识别状态
            speechToTextManager.reset()
        }
    }

    // 发送Dify聊天消息
    private suspend fun sendDifyChatMessage(text: String, imageUri: Uri? = null) {
        try {
            isStreamingResponse.value = true

            // 检查网络状态
            if (!NetworkUtils.isNetworkAvailable(this@ConversationInterface)) {
                val diagnostics = NetworkUtils.getNetworkDiagnostics(this@ConversationInterface)
                messagesList.add(Message("网络连接不可用，请检查网络设置后重试。\n\n$diagnostics", false))
                isStreamingResponse.value = false
                return
            }

            // 测试Dify服务器连接
            if (!NetworkUtils.checkDifyServerConnection(DifyConfig.BASE_URL)) {
                messagesList.add(Message("无法连接到AI服务器，请稍后重试。", false))
                isStreamingResponse.value = false
                return
            }

            // 准备文件信息（如果有图片）
            val files = mutableListOf<FileInfo>()
            imageUri?.let { uri ->
                val fileInfo = difyFileManager.uploadFile(uri, getCurrentUserId())
                fileInfo?.let { files.add(it) }
            }

            // 重置流处理器
            difyStreamHandler.reset()

            // 发送流式请求
            val stream = difyApiClient.sendChatMessageStream(
                query = text,
                user = getCurrentUserId(),
                conversationId = currentConversationId.value,
                files = files.ifEmpty { null }
            )

            // 处理流式响应
            val handledStream = difyStreamHandler.handleStreamResponse(stream)

            // 用于累积消息内容
            var accumulatedMessage = ""

            handledStream.collect { event ->
                difyStreamHandler.handleStreamEvent(event)

                when (event.event) {
                    StreamEventType.MESSAGE -> {
                        // 累积消息内容
                        event.answer?.let { answer ->
                            accumulatedMessage += answer
                            // 更新最后一条AI消息（如果存在）
                            updateLastAIMessage(accumulatedMessage)
                        }

                        // 更新会话ID
                        event.conversationId?.let {
                            currentConversationId.value = it
                        }
                    }

                    StreamEventType.MESSAGE_END -> {
                        // 消息结束，确保最终消息已添加
                        if (accumulatedMessage.isNotEmpty()) {
                            ensureAIMessageExists(accumulatedMessage)
                        }
                        isStreamingResponse.value = false
                    }

                    StreamEventType.ERROR -> {
                        // 处理错误
                        val errorMessage = event.message ?: "发生未知错误"
                        messagesList.add(Message("抱歉，发生了错误：$errorMessage", false))
                        isStreamingResponse.value = false
                    }
                }
            }

        } catch (e: Exception) {
            Log.e("DifyChat", "Error sending Dify chat message", e)

            val errorMessage = when (e) {
                is java.net.SocketException -> "网络连接被重置，请检查网络连接后重试。"
                is java.net.ConnectException -> "无法连接到服务器，请检查网络设置。"
                is java.net.UnknownHostException -> "无法解析服务器地址，请检查网络连接。"
                is java.net.SocketTimeoutException -> "请求超时，请稍后重试。"
                is IOException -> "网络请求失败：${e.message}"
                else -> "发生未知错误，请稍后再试。"
            }

            messagesList.add(Message("抱歉，$errorMessage", false))
            isStreamingResponse.value = false
        }
    }

    // 更新最后一条AI消息
    private fun updateLastAIMessage(content: String) {
        val lastIndex = messagesList.size - 1
        if (lastIndex >= 0 && !messagesList[lastIndex].isFromUser) {
            // 更新现有的AI消息
            messagesList[lastIndex] = messagesList[lastIndex].copy(content = content)
        } else {
            // 添加新的AI消息
            messagesList.add(Message(content, false))
        }
    }

    // 确保AI消息存在
    private fun ensureAIMessageExists(content: String) {
        val lastIndex = messagesList.size - 1
        if (lastIndex < 0 || messagesList[lastIndex].isFromUser) {
            // 添加新的AI消息
            messagesList.add(Message(content, false))
        }
    }

    // 获取当前用户ID
    private fun getCurrentUserId(): String {
        return "android-user-${android.provider.Settings.Secure.getString(
            contentResolver,
            android.provider.Settings.Secure.ANDROID_ID
        )}"
    }

    // 消息列表状态
    private val messagesList = mutableStateListOf<Message>()
    
    companion object {
        private const val SPEECH_REQUEST_CODE = 1000
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()

        // 初始化语音录制管理器
        audioRecorderManager = AudioRecorderManager(this)

        // 初始化语音转文字管理器
        speechToTextManager = SpeechToTextManager(this)

        // 启用多引擎模式
        speechToTextManager?.setMultiEngineMode(true)

        // 初始化Dify API客户端
        difyApiClient = DifyApiClient(
            apiKey = DifyConfig.API_KEY,
            baseUrl = DifyConfig.BASE_URL
        )
        difyFileManager = DifyFileManager(this, difyApiClient)
        difyStreamHandler = DifyStreamHandler()

        // 监听语音转文字状态变化
        lifecycleScope.launch {
            speechToTextManager.state.collect { state ->
                speechToTextState.value = state
            }
        }

        lifecycleScope.launch {
            speechToTextManager.result.collect { result ->
                speechToTextResult.value = result
                // 如果转换完成且有文本，自动填入输入框
                if (result.text.isNotEmpty() && !result.isPartial) {
                    speechTextState.value = result.text
                }
            }
        }

        lifecycleScope.launch {
            speechToTextManager.errorMessage.collect { error ->
                speechToTextError.value = error
            }
        }

        // 添加初始欢迎消息
        if (messagesList.isEmpty()) {
            messagesList.add(
                Message(
                    content = "哈喽~ 👋\n  我是你的AI健康智能管家，我会回答你提出的任何问题，解答各种疑惑~",
                    isFromUser = false
                )
            )
        }
        
        setContent {
            AIHealthButlerTheme {
                ChatScreen(
                    messages = messagesList,
                    onSpeechInput = { checkAndRequestMicPermission() },
                    onImagePickerClick = { pickImageLauncher.launch("image/*") },
                    speechText = speechTextState.value,
                    selectedImageUri = selectedImageUri,
                    onSendMessage = { text ->
                        sendMessage(text, selectedImageUri)
                    },
                    onClearSelectedImage = {
                        selectedImageUri = null
                    },
                    audioRecorderManager = audioRecorderManager,
                    onSendVoiceMessage = { audioFile, duration ->
                        sendVoiceMessage(audioFile, duration)
                    },
                    speechToTextManager = speechToTextManager,
                    speechToTextState = speechToTextState.value,
                    speechToTextResult = speechToTextResult.value,
                    speechToTextError = speechToTextError.value,
                    onSendSpeechMessage = { text ->
                        sendSpeechToTextMessage(text)
                    },
                    onOpenNetworkDebug = {
                        startActivity(Intent(this@ConversationInterface, NetworkDebugActivity::class.java))
                    }
                )
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        // 释放语音录制管理器资源
        audioRecorderManager.release()
        // 释放语音转文字管理器资源
        speechToTextManager.release()
        // 释放Dify API客户端资源
        difyApiClient.release()
        // 清理临时文件
        difyFileManager.cleanupTempFiles()
    }
}

data class Message(
    val content: String,
    val isFromUser: Boolean,
    val timestamp: Long = System.currentTimeMillis(),
    val formattedTime: String = formatTimestamp(timestamp),
    val imageUri: Uri? = null, // 添加图片URI字段
    val audioFile: java.io.File? = null, // 添加音频文件字段
    val audioDuration: Long = 0L, // 音频时长（毫秒）
    val messageType: MessageType = MessageType.TEXT // 消息类型
)

enum class MessageType {
    TEXT, IMAGE, AUDIO, TEXT_WITH_IMAGE
}

// 根据时间戳格式化时间显示
fun formatTimestamp(timestamp: Long): String {
    val calendar = Calendar.getInstance()
    val currentCalendar = Calendar.getInstance()
    calendar.timeInMillis = timestamp
    
    // 判断是否为今天
    return if (isSameDay(calendar, currentCalendar)) {
        // 今天，显示时:分
        SimpleDateFormat("HH:mm", Locale.getDefault()).format(calendar.time)
    } else if (isYesterday(calendar, currentCalendar)) {
        // 昨天，显示"昨天 时:分"
        "昨天 " + SimpleDateFormat("HH:mm", Locale.getDefault()).format(calendar.time)
    } else if (isWithinOneWeek(calendar, currentCalendar)) {
        // 一周内，显示"周几 时:分"
        val weekDays = arrayOf("周日", "周一", "周二", "周三", "周四", "周五", "周六")
        weekDays[calendar.get(Calendar.DAY_OF_WEEK) - 1] + " " + 
                SimpleDateFormat("HH:mm", Locale.getDefault()).format(calendar.time)
    } else {
        // 超过一周，显示"月-日 时:分"
        SimpleDateFormat("MM月dd日 HH:mm", Locale.getDefault()).format(calendar.time)
    }
}

// 判断两个日期是否是同一天
fun isSameDay(cal1: Calendar, cal2: Calendar): Boolean {
    return cal1.get(Calendar.YEAR) == cal2.get(Calendar.YEAR) &&
           cal1.get(Calendar.DAY_OF_YEAR) == cal2.get(Calendar.DAY_OF_YEAR)
}

// 判断是否是昨天
fun isYesterday(cal1: Calendar, currentCal: Calendar): Boolean {
    val yesterday = Calendar.getInstance()
    yesterday.add(Calendar.DAY_OF_YEAR, -1)
    return cal1.get(Calendar.YEAR) == yesterday.get(Calendar.YEAR) &&
           cal1.get(Calendar.DAY_OF_YEAR) == yesterday.get(Calendar.DAY_OF_YEAR)
}

// 判断是否在一周内
fun isWithinOneWeek(cal1: Calendar, currentCal: Calendar): Boolean {
    val oneWeekAgo = Calendar.getInstance()
    oneWeekAgo.add(Calendar.DAY_OF_YEAR, -7)
    return cal1.timeInMillis >= oneWeekAgo.timeInMillis
}

// 判断两条消息之间的时间间隔是否超过5分钟
fun shouldShowTimestamp(current: Message, previous: Message?): Boolean {
    if (previous == null) return true // 第一条消息总是显示时间
    
    // 计算时间差（毫秒）
    val timeDifference = current.timestamp - previous.timestamp
    // 转换为分钟
    val minutesDifference = TimeUnit.MILLISECONDS.toMinutes(timeDifference)
    
    // 如果时间差超过5分钟，则显示时间戳
    return minutesDifference >= 5
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ChatScreen(
    messages: List<Message>,
    onSpeechInput: () -> Unit = {},
    onImagePickerClick: () -> Unit = {},
    speechText: String = "",
    selectedImageUri: Uri? = null,
    onSendMessage: (String) -> Unit = {},
    onClearSelectedImage: () -> Unit = {},
    audioRecorderManager: AudioRecorderManager? = null,
    onSendVoiceMessage: (java.io.File, Long) -> Unit = { _, _ -> },
    speechToTextManager: SpeechToTextManager? = null,
    speechToTextState: SpeechToTextState = SpeechToTextState.IDLE,
    speechToTextResult: SpeechToTextResult = SpeechToTextResult(),
    speechToTextError: String = "",
    onSendSpeechMessage: (String) -> Unit = {},
    onOpenNetworkDebug: () -> Unit = {}
) {
    val context = LocalContext.current
    val messageInput = remember { mutableStateOf(TextFieldValue(speechText)) }
    val listState = rememberLazyListState()
    val coroutineScope = rememberCoroutineScope()
    val clipboardManager = LocalClipboardManager.current

    // 语音录制相关状态
    var isRecording by remember { mutableStateOf(false) }
    var recordingDuration by remember { mutableStateOf(0L) }
    var volumeLevel by remember { mutableStateOf(0f) }
    var isCancelMode by remember { mutableStateOf(false) }
    var isConvertToTextMode by remember { mutableStateOf(false) }
    var currentPlayingAudio by remember { mutableStateOf<java.io.File?>(null) }
    var showRecordingOverlay by remember { mutableStateOf(false) }

    // 输入模式状态：true为文本模式，false为语音模式
    var isTextInputMode by remember { mutableStateOf(true) }

    // 监听录制状态
    audioRecorderManager?.let { recorder ->
        val recordingState by recorder.recordingState.collectAsState()
        val duration by recorder.recordingDuration.collectAsState()
        val volume by recorder.volumeLevel.collectAsState()
        val playingState by recorder.playingState.collectAsState()

        LaunchedEffect(recordingState) {
            isRecording = recordingState == AudioRecorderManager.RecordingState.RECORDING
            showRecordingOverlay = isRecording
        }

        LaunchedEffect(duration) {
            recordingDuration = duration
        }

        LaunchedEffect(volume) {
            volumeLevel = volume
        }

        // 监听播放状态，播放完成时自动停止
        LaunchedEffect(playingState) {
            when (playingState) {
                AudioRecorderManager.PlayingState.COMPLETED,
                AudioRecorderManager.PlayingState.IDLE -> {
                    currentPlayingAudio = null
                }
                else -> {}
            }
        }
    }
    
    // 监听语音识别结果的变化
    LaunchedEffect(speechText) {
        if (speechText.isNotEmpty()) {
            messageInput.value = TextFieldValue(speechText)
        }
    }
    
    // 当前选中的标签
    var selectedTab by remember { mutableStateOf("对话") }
    
    // 添加长按操作的菜单显示状态
    var showContextMenu by remember { mutableStateOf(false) }
    var contextMenuPosition by remember { mutableStateOf(ClickOffset(0f, 0f)) }
    
    // 自动滚动到最后一条消息
    LaunchedEffect(messages.size) {
        if (messages.isNotEmpty()) {
            listState.animateScrollToItem(messages.size - 1)
        }
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.Center
                    ) {
                        Text(
                            text = "对话",
                            fontSize = if (selectedTab == "对话") 20.sp else 16.sp,
                            fontWeight = if (selectedTab == "对话") FontWeight.Bold else FontWeight.Normal,
                            color = if (selectedTab == "对话") Color.Black else Color.Gray,
                            modifier = Modifier.clickable { selectedTab = "对话" }
                        )
                        Spacer(modifier = Modifier.width(24.dp))
                        Text(
                            text = "我的",
                            fontSize = if (selectedTab == "我的") 20.sp else 16.sp,
                            fontWeight = if (selectedTab == "我的") FontWeight.Bold else FontWeight.Normal,
                            color = if (selectedTab == "我的") Color.Black else Color.Gray,
                            modifier = Modifier.clickable {
                                selectedTab = "我的"
                                val intent = Intent(context, MainActivity::class.java)
                                context.startActivity(intent)
                            }
                        )
                    }
                },
                actions = {
                    // 调试按钮（网络诊断工具）
                    IconButton(onClick = onOpenNetworkDebug) {
                        Text(
                            text = "🔧",
                            fontSize = 16.sp
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = Color(0xFFF4F6F8)
                )
            )
        },
        containerColor = Color.Transparent
    ) { innerPadding ->
        Box(
            modifier = Modifier
                .fillMaxSize()
        ) {
            // 添加背景图片在最底层
            Image(
                painter = painterResource(id = R.drawable.conversation_back),
                contentDescription = null,
                modifier = Modifier.fillMaxSize(),
                contentScale = ContentScale.FillBounds
            )
            
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(innerPadding)
            ) {
                // 聊天消息列表
                LazyColumn(
                    modifier = Modifier
                        .weight(1f)
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp),
                    state = listState,
                    reverseLayout = false
                ) {
                    itemsIndexed(messages) { index, message ->
                        val previousMessage = if (index > 0) messages[index - 1] else null
                        ChatMessageItem(
                            message = message,
                            showTimestamp = shouldShowTimestamp(message, previousMessage),
                            onLongClick = { offset ->
                                // 显示上下文菜单
                                showContextMenu = true
                                contextMenuPosition = offset
                            },
                            onCopyText = {
                                // 复制文本到剪贴板
                                clipboardManager.setText(AnnotatedString(message.content))
                                Toast.makeText(context, "已复制到剪贴板", Toast.LENGTH_SHORT).show()
                            },
                            audioRecorderManager = audioRecorderManager,
                            currentPlayingAudio = currentPlayingAudio,
                            onPlayingAudioChange = { newPlayingAudio ->
                                currentPlayingAudio = newPlayingAudio
                            }
                        )
                    }
                }
                
                // 显示选择的图片预览（如果有）
                if (selectedImageUri != null) {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(100.dp)
                            .padding(horizontal = 16.dp, vertical = 4.dp)
                            .background(Color.LightGray.copy(alpha = 0.2f), RoundedCornerShape(8.dp))
                    ) {
                        Row(
                            modifier = Modifier
                                .fillMaxSize()
                                .padding(8.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            // 图片预览
                            AsyncImage(
                                model = ImageRequest.Builder(LocalContext.current)
                                    .data(selectedImageUri)
                                    .crossfade(true)
                                    .build(),
                                contentDescription = "已选图片",
                                modifier = Modifier
                                    .size(80.dp)
                                    .clip(RoundedCornerShape(4.dp)),
                                contentScale = ContentScale.Crop
                            )
                            
                            Spacer(modifier = Modifier.width(8.dp))
                            
                            Text(
                                text = "已选择图片，将与文字一起发送",
                                fontSize = 12.sp,
                                color = Color.DarkGray,
                                modifier = Modifier.weight(1f)
                            )
                            
                            // 删除按钮
                            IconButton(
                                onClick = onClearSelectedImage,
                                modifier = Modifier.size(30.dp)
                            ) {
                                Icon(
                                    painterResource(id = R.drawable.ic_close),
                                    contentDescription = "移除图片",
                                    tint = Color.Gray
                                )
                            }
                        }
                    }
                }

                // 输入区域
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 5.dp, horizontal = 1.dp),//vertical：纵向的；horizontal：横向的
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.SpaceEvenly
                ) {
                    // 键盘/语音切换按钮
                    IconButton(
                        onClick = {
                            isTextInputMode = !isTextInputMode
                        },
                        modifier = Modifier.size(50.dp)
                    ) {
                        Icon(
                            painter = painterResource(
                                id = if (isTextInputMode) R.drawable.ic_mic else R.drawable.ic_keyboard
                            ),
                            contentDescription = if (isTextInputMode) "切换到语音输入" else "切换到文本输入",
                            tint = Color(0xFF333333),
                            modifier = Modifier.size(24.dp)
                        )
                    }

                    Spacer(modifier = Modifier.width(1.dp))

                    // 根据输入模式显示不同的中间区域
                    if (isTextInputMode) {
                        // 文本输入模式 - 多行文本输入框
                        TextField(
                            value = messageInput.value,
                            onValueChange = { messageInput.value = it },
                            modifier = Modifier
                                .weight(1f)
                                .padding(horizontal = 1.dp)
                                .heightIn(min = 50.dp, max = 150.dp), // 允许高度在一定范围内变化
                            placeholder = {
                                Text(
                                    "有什么问题需要我解答吗？",
                                    style = TextStyle(
                                        color = Color.Gray,
                                        fontSize = 12.sp
                                    )
                                )
                            },
                            colors = TextFieldDefaults.colors(
                                focusedContainerColor = Color.White,
                                unfocusedContainerColor = Color.White,
                                cursorColor = Color(0xFF2EB0AC),
                                focusedIndicatorColor = Color.Transparent,
                                unfocusedIndicatorColor = Color.Transparent
                            ),
                            shape = RoundedCornerShape(10.dp),
                            singleLine = false, // 允许多行
                            maxLines = 10 // 最多显示10行
                        )
                    } else {
                        // 语音输入模式 - 按住说话按钮
                        Box(
                            modifier = Modifier
                                .weight(1f)
                                .height(50.dp)
                                .padding(horizontal = 1.dp)
                                .clip(RoundedCornerShape(10.dp))
                                .background(
                                    when {
                                        isRecording && isConvertToTextMode -> Color(0xFF5C93D1) // 转文字模式（右滑）
                                        isRecording && isCancelMode -> Color(0xFFC75D5D) // 取消模式（左滑）
                                        isRecording -> Color(0xFF74B5AF) // 发送模式（上滑发送）
                                        else -> Color.White
                                    }
                                )
                                .pointerInput(Unit) {
                                    detectTapGestures(
                                        onTap = {
                                            // 短按时显示提示
                                            if (!isRecording) {
                                                Toast.makeText(context, "长按录音", Toast.LENGTH_SHORT).show()
                                            }
                                        },
                                        onLongPress = { offset ->
                                            // 长按开始录音
                                            audioRecorderManager?.let { recorder ->
                                                if (ContextCompat.checkSelfPermission(
                                                        context,
                                                        Manifest.permission.RECORD_AUDIO
                                                    ) == PackageManager.PERMISSION_GRANTED
                                                ) {
                                                    if (recorder.startRecording()) {
                                                        isRecording = true
                                                        showRecordingOverlay = true
                                                        isCancelMode = false

                                                        // 启动时长计时器
                                                        coroutineScope.launch {
                                                            while (isRecording) {
                                                                recordingDuration = recorder.getRecordingDuration()
                                                                volumeLevel = recorder.getCurrentVolumeLevel()
                                                                kotlinx.coroutines.delay(100)
                                                            }
                                                        }
                                                    }
                                                } else {
                                                    onSpeechInput() // 请求权限
                                                }
                                            }
                                        }
                                    )
                                }
                                .pointerInput(Unit) {
                                    // 用于跟踪拖拽的累计距离
                                    var totalDragY = 0f
                                    var totalDragX = 0f
                                    var localIsConvertToTextMode = false
                                    var localIsCancelMode = false
                                    var localIsSendMode = false

                                    // 检测拖拽手势用于取消录音、发送语音和语音转文字
                                    detectDragGestures(
                                        onDragStart = { offset ->
                                            // 重置拖拽距离和模式
                                            totalDragY = 0f
                                            totalDragX = 0f
                                            localIsConvertToTextMode = false
                                            localIsCancelMode = false
                                            localIsSendMode = false
                                        },
                                        onDrag = { change, dragAmount ->
                                            if (isRecording) {
                                                // 累计拖拽距离
                                                totalDragY += dragAmount.y
                                                totalDragX += dragAmount.x

                                                // 重置所有模式
                                                localIsConvertToTextMode = false
                                                localIsCancelMode = false
                                                localIsSendMode = false

                                                // 检测向左滑动距离，超过一定距离进入取消模式
                                                if (totalDragX < -100) { // 向左滑动超过100像素
                                                    localIsCancelMode = true
                                                    isCancelMode = true
                                                    isConvertToTextMode = false
                                                }
                                                // 检测向右滑动距离，超过一定距离进入语音转文字模式
                                                else if (totalDragX > 100) { // 向右滑动超过100像素
                                                    localIsConvertToTextMode = true
                                                    isConvertToTextMode = true
                                                    isCancelMode = false
                                                }
                                                // 检测向上滑动距离，超过一定距离进入发送模式
                                                else if (totalDragY < -80) { // 向上滑动超过80像素
                                                    localIsSendMode = true
                                                    isCancelMode = false
                                                    isConvertToTextMode = false
                                                } else {
                                                    // 没有明显滑动，保持正常录音模式
                                                    isCancelMode = false
                                                    isConvertToTextMode = false
                                                }
                                            }
                                        },
                                        onDragEnd = {
                                            // 拖拽结束，根据滑动方向执行不同操作
                                            audioRecorderManager?.let { recorder ->
                                                if (isRecording) {
                                                    if (localIsCancelMode) {
                                                        // 向右滑动：取消录音
                                                        recorder.cancelRecording()
                                                        speechToTextManager?.cancelListening()
                                                        isRecording = false
                                                        showRecordingOverlay = false
                                                        isCancelMode = false
                                                        recordingDuration = 0L
                                                        volumeLevel = 0f
                                                    } else if (localIsConvertToTextMode) {
                                                        // 向右滑动：转换为文字模式
                                                        val audioFile = recorder.stopRecording()
                                                        if (audioFile != null) {
                                                            // 开始语音转文字转换
                                                            if (ContextCompat.checkSelfPermission(
                                                                    context,
                                                                    Manifest.permission.RECORD_AUDIO
                                                                ) == PackageManager.PERMISSION_GRANTED
                                                            ) {
                                                                speechToTextManager?.startSpeechToText()
                                                            } else {
                                                                // 权限不足时的处理
                                                                speechToTextManager?.setError("需要录音权限进行语音识别")
                                                            }
                                                        } else {
                                                            // 录音文件获取失败
                                                            speechToTextManager?.setError("录音失败，请重试")
                                                            showRecordingOverlay = false
                                                        }
                                                        isRecording = false
                                                        // 保持覆盖层显示，等待转换完成
                                                        isConvertToTextMode = false
                                                    } else if (localIsSendMode) {
                                                        // 向上滑动：发送语音
                                                        val audioFile = recorder.stopRecording()
                                                        if (audioFile != null) {
                                                            val duration = recorder.getAudioDuration(audioFile)
                                                            if (duration > 0) {
                                                                onSendVoiceMessage(audioFile, duration)
                                                            } else {
                                                                Toast.makeText(context, "录音时间太短", Toast.LENGTH_SHORT).show()
                                                            }
                                                        } else {
                                                            Toast.makeText(context, "录音失败，请重试", Toast.LENGTH_SHORT).show()
                                                        }
                                                        isRecording = false
                                                        showRecordingOverlay = false
                                                        isCancelMode = false
                                                        recordingDuration = 0L
                                                        volumeLevel = 0f
                                                    } else {
                                                        // 没有明显滑动，松开时默认发送语音
                                                        val audioFile = recorder.stopRecording()
                                                        if (audioFile != null) {
                                                            val duration = recorder.getAudioDuration(audioFile)
                                                            if (duration > 0) {
                                                                onSendVoiceMessage(audioFile, duration)
                                                            } else {
                                                                Toast.makeText(context, "录音时间太短", Toast.LENGTH_SHORT).show()
                                                            }
                                                        } else {
                                                            Toast.makeText(context, "录音失败，请重试", Toast.LENGTH_SHORT).show()
                                                        }
                                                        isRecording = false
                                                        showRecordingOverlay = false
                                                        isCancelMode = false
                                                        recordingDuration = 0L
                                                        volumeLevel = 0f
                                                    }
                                                }
                                            }
                                            // 重置拖拽距离和模式
                                            totalDragY = 0f
                                            totalDragX = 0f
                                            localIsConvertToTextMode = false
                                            localIsCancelMode = false
                                            localIsSendMode = false
                                        }
                                    )
                                },
                            contentAlignment = Alignment.Center
                        ) {
                            Text(
                                text = when {
                                    isRecording && isConvertToTextMode -> "右滑转文字"
                                    isRecording && isCancelMode -> "左滑取消"
                                    isRecording -> "上滑发送"
                                    else -> "按住说话"
                                },
                                color = when {
                                    isRecording && isConvertToTextMode -> Color.White
                                    isRecording && isCancelMode -> Color.White
                                    isRecording -> Color.White
                                    else -> Color(0xFF666666)
                                },
                                fontSize = 14.sp
                            )
                        }
                    }

                    Spacer(modifier = Modifier.width(1.dp))

                    // 只在文本输入模式下显示图片和发送按钮
                    if (isTextInputMode) {
                        // 图片按钮
                        IconButton(
                            onClick = onImagePickerClick
                        ) {
                            Icon(
                                painter = painterResource(id = R.drawable.ic_image),
                                contentDescription = "发送图片",
                                tint = Color.White,
                                modifier = Modifier
                                    .size(35.dp)
                                    .clip(CircleShape)
                                    .background(Color(0xFF2EB0AC))
                                    .padding(5.dp)
                            )
                        }

                        Spacer(modifier = Modifier.width(1.dp))

                        // 发送按钮
                        IconButton(
                            onClick = {
                                // 检查是否有内容可发送（文本或图片）
                                if (messageInput.value.text.isNotEmpty() || selectedImageUri != null) {
                                    // 调用发送消息接口
                                    onSendMessage(messageInput.value.text)

                                    // 清空输入框
                                    messageInput.value = TextFieldValue("")
                                }
                            },
                        ) {
                            Icon(
                                painter = painterResource(id = R.drawable.ic_send),
                                contentDescription = "发送",
                                tint = Color.White,
                                modifier = Modifier
                                    .size(35.dp)
                                    .clip(CircleShape)
                                    .background(Color(0xFF2EB0AC))
                                    .padding(5.dp)
                            )
                        }
                    }
                }
            }

            // 语音录制覆盖层（只在录音时显示）
            if (showRecordingOverlay) {
                VoiceRecordingOverlay(
                    isRecording = isRecording,
                    recordingDuration = recordingDuration,
                    volumeLevel = volumeLevel,
                    isCancelMode = isCancelMode,
                    isConvertToTextMode = isConvertToTextMode,
                    speechToTextState = speechToTextState,
                    speechToTextResult = speechToTextResult,
                    speechToTextError = speechToTextError,
                    speechToTextManager = speechToTextManager,
                    onCancel = {
                        audioRecorderManager?.cancelRecording()
                        speechToTextManager?.cancelListening()
                        isRecording = false
                        showRecordingOverlay = false
                        isCancelMode = false
                    },
                    onConvertToText = {
                        // 转换为文字功能
                        val audioFile = audioRecorderManager?.stopRecording()
                        if (audioFile != null) {
                            // 检查权限并开始语音转文字转换
                            when {
                                ContextCompat.checkSelfPermission(
                                    context,
                                    Manifest.permission.RECORD_AUDIO
                                ) == PackageManager.PERMISSION_GRANTED -> {
                                    // 权限已授予，检查语音识别器是否可用
                                    if (speechToTextManager?.isAvailable() == true) {
                                        speechToTextManager.startSpeechToText()
                                    } else {
                                        // 尝试重新初始化
                                        speechToTextManager?.reinitialize()
                                        // 延迟启动，给初始化时间
                                        android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                                            if (speechToTextManager?.isAvailable() == true) {
                                                speechToTextManager.startSpeechToText()
                                            } else {
                                                speechToTextManager?.setError("语音识别服务不可用，请重启应用或检查设备设置")
                                            }
                                        }, 500)
                                    }
                                }
                                else -> {
                                    speechToTextManager?.setError("需要录音权限进行语音识别")
                                }
                            }
                        } else {
                            speechToTextManager?.setError("录音失败，无法转换为文字")
                            showRecordingOverlay = false
                        }
                        isRecording = false
                        // 不立即关闭覆盖层，等待转换完成
                    },
                    onComplete = {
                        val audioFile = audioRecorderManager?.stopRecording()
                        if (audioFile != null) {
                            val duration = audioRecorderManager?.getAudioDuration(audioFile) ?: 0L
                            if (duration > 0) {
                                onSendVoiceMessage(audioFile, duration)
                            } else {
                                Toast.makeText(context, "录音时间太短", Toast.LENGTH_SHORT).show()
                            }
                        } else {
                            Toast.makeText(context, "录音失败，请重试", Toast.LENGTH_SHORT).show()
                        }
                        isRecording = false
                        showRecordingOverlay = false
                    },
                    onDismissSpeechResult = {
                        speechToTextManager?.reset()
                        showRecordingOverlay = false
                    },
                    onSendSpeechMessage = { text ->
                        onSendSpeechMessage(text)
                        showRecordingOverlay = false
                    }
                )
            }
        }
    }
}

// 记录点击位置的辅助类
data class ClickOffset(val x: Float, val y: Float)

@Composable
fun ChatMessageItem(
    message: Message,
    showTimestamp: Boolean = true,
    onLongClick: (ClickOffset) -> Unit = {},
    onCopyText: () -> Unit = {},
    audioRecorderManager: AudioRecorderManager? = null,
    currentPlayingAudio: java.io.File? = null,
    onPlayingAudioChange: (java.io.File?) -> Unit = {}
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp),
        horizontalAlignment = if (message.isFromUser) Alignment.End else Alignment.Start
    ) {
        // 消息时间 - 只在需要显示时间时显示
        if (!message.isFromUser && showTimestamp) {
            Text(
                text = message.formattedTime,
                color = Color.Gray,
                fontSize = 12.sp,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 4.dp),
                textAlign = TextAlign.Center
            )
        }

        // 消息气泡
        val isFirstMessage = message.content.contains("哈喽")
        val cornerShape = RoundedCornerShape(
            topStart = 16.dp,
            topEnd = 16.dp,
            bottomStart = if (message.isFromUser) 16.dp else 0.dp,
            bottomEnd = if (message.isFromUser) 0.dp else 16.dp
        )
        
        Column(
            modifier = Modifier,
            horizontalAlignment = if (message.isFromUser) Alignment.End else Alignment.Start
        ) {
            Box(
                modifier = Modifier
                    .clip(cornerShape)
                    .background(
                        brush = when {
                            isFirstMessage -> Brush.verticalGradient(
                                colors = listOf(
                                    Color(0xFFC0E1DC),
                                    Color(0xFFFCFDFD)
                                )
                            )
                            message.isFromUser -> Brush.verticalGradient(
                                colors = listOf(
                                    Color(0xFFBADED9),
                                    Color(0xFFBADED9)
                                )
                            )
                            else -> Brush.verticalGradient(
                                colors = listOf(
                                    Color(0xFFE4E7EB),
                                    Color(0xFFE4E7EB)
                                )
                            )
                        },
                        shape = cornerShape
                    )
                    .border(
                        width = if (isFirstMessage) 1.dp else 0.dp,
                        color = if (isFirstMessage) Color(0xFFABCFCA) else Color.Transparent,
                        shape = cornerShape
                    )
                    .padding(12.dp)
                    // 添加长按手势，用于复制文本
                    .pointerInput(Unit) {
                        detectTapGestures(
                            onLongPress = { offset ->
                                onLongClick(ClickOffset(offset.x, offset.y))
                                onCopyText()
                            }
                        )
                    }
            ) {
                Column {
                    // 显示语音消息（如果有）
                    if (message.messageType == MessageType.AUDIO && message.audioFile != null) {
                        var isPlaying by remember { mutableStateOf(false) }

                        // 监听播放状态变化
                        LaunchedEffect(currentPlayingAudio) {
                            isPlaying = currentPlayingAudio == message.audioFile
                        }

                        VoiceMessageBubble(
                            duration = message.audioDuration,
                            isPlaying = isPlaying,
                            isFromUser = message.isFromUser,
                            onPlayClick = {
                                if (isPlaying) {
                                    audioRecorderManager?.stopPlaying()
                                    onPlayingAudioChange(null)
                                } else {
                                    // 停止其他正在播放的音频
                                    audioRecorderManager?.stopPlaying()

                                    audioRecorderManager?.playAudio(message.audioFile)?.let { success ->
                                        if (success) {
                                            onPlayingAudioChange(message.audioFile)
                                        }
                                    }
                                }
                            },
                            modifier = Modifier.widthIn(max = 200.dp)
                        )
                    }

                    // 显示图片消息（如果有）
                    if (message.imageUri != null) {
                        AsyncImage(
                            model = ImageRequest.Builder(LocalContext.current)
                                .data(message.imageUri)
                                .crossfade(true)
                                .build(),
                            contentDescription = "图片消息",
                            modifier = Modifier
                                .widthIn(max = 220.dp)
                                .heightIn(max = 220.dp)
                                .clip(RoundedCornerShape(8.dp)),
                            contentScale = ContentScale.Fit
                        )

                        // 如果同时有图片和文字，添加一个间隔
                        if (message.content.isNotEmpty()) {
                            Spacer(modifier = Modifier.height(8.dp))
                        }
                    }

                    // 显示文本内容
                    if (message.content.isNotEmpty()) {
                        if (isFirstMessage) {
                            val welcomeText = message.content.split("\n")
                            Column {
                                Text(
                                    text = welcomeText.first(), // "哈喽~ 👋"
                                    color = Color(0xFF333333),
                                    fontSize = 18.sp,
                                    fontWeight = FontWeight.Medium
                                )
                                if (welcomeText.size > 1) {
                                    Text(
                                        text = welcomeText[1], // 第二行文字，已经包含缩进
                                        color = Color(0xFF333333),
                                        fontSize = 14.sp
                                    )
                                }
                            }
                        } else {
                            Text(
                                text = message.content,
                                color = Color(0xFF333333),
                                fontSize = 15.sp
                            )
                        }
                    }
                }
            }
        }
    }
}


