package com.example.aihealthbutler.dify

import android.content.Context
import android.net.Uri
import android.util.Log
import android.webkit.MimeTypeMap
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileOutputStream
import java.io.InputStream

/**
 * Dify 文件管理器
 */
class DifyFileManager(
    private val context: Context,
    private val apiClient: DifyApiClient
) {
    
    companion object {
        private const val TAG = "DifyFileManager"
        private const val TEMP_DIR = "dify_temp"
        
        // 支持的图片类型
        private val SUPPORTED_IMAGE_TYPES = setOf(
            "jpg", "jpeg", "png", "gif", "webp", "svg"
        )
        
        // 支持的文档类型
        private val SUPPORTED_DOCUMENT_TYPES = setOf(
            "txt", "md", "markdown", "pdf", "html", "xlsx", "xls", 
            "docx", "csv", "eml", "msg", "pptx", "ppt", "xml", "epub"
        )
        
        // 支持的音频类型
        private val SUPPORTED_AUDIO_TYPES = setOf(
            "mp3", "m4a", "wav", "webm", "amr"
        )
        
        // 支持的视频类型
        private val SUPPORTED_VIDEO_TYPES = setOf(
            "mp4", "mov", "mpeg", "mpga"
        )
    }
    
    /**
     * 上传文件并返回文件信息
     */
    suspend fun uploadFile(
        uri: Uri,
        user: String
    ): FileInfo? = withContext(Dispatchers.IO) {
        try {
            // 将 URI 转换为临时文件
            val tempFile = uriToTempFile(uri) ?: return@withContext null
            
            // 上传文件
            val uploadResponse = apiClient.uploadFile(tempFile, user)
            
            // 确定文件类型
            val fileType = determineFileType(uploadResponse.extension)
            
            // 清理临时文件
            tempFile.delete()
            
            // 返回文件信息
            FileInfo(
                type = fileType,
                transferMethod = TransferMethod.LOCAL_FILE,
                uploadFileId = uploadResponse.id
            )
        } catch (e: Exception) {
            Log.e(TAG, "Failed to upload file", e)
            null
        }
    }
    
    /**
     * 从远程URL创建文件信息
     */
    fun createFileInfoFromUrl(
        url: String,
        fileType: String = FileType.IMAGE
    ): FileInfo {
        return FileInfo(
            type = fileType,
            transferMethod = TransferMethod.REMOTE_URL,
            url = url
        )
    }
    
    /**
     * 将URI转换为临时文件
     */
    private suspend fun uriToTempFile(uri: Uri): File? = withContext(Dispatchers.IO) {
        try {
            val inputStream: InputStream = context.contentResolver.openInputStream(uri)
                ?: return@withContext null
            
            // 获取文件名和扩展名
            val fileName = getFileName(uri)
            val extension = getFileExtension(uri)
            
            // 创建临时目录
            val tempDir = File(context.cacheDir, TEMP_DIR)
            if (!tempDir.exists()) {
                tempDir.mkdirs()
            }
            
            // 创建临时文件
            val tempFile = File(tempDir, "${System.currentTimeMillis()}_$fileName")
            
            // 复制文件内容
            inputStream.use { input ->
                FileOutputStream(tempFile).use { output ->
                    input.copyTo(output)
                }
            }
            
            tempFile
        } catch (e: Exception) {
            Log.e(TAG, "Failed to convert URI to temp file", e)
            null
        }
    }
    
    /**
     * 获取文件名
     */
    private fun getFileName(uri: Uri): String {
        val cursor = context.contentResolver.query(uri, null, null, null, null)
        return cursor?.use {
            if (it.moveToFirst()) {
                val nameIndex = it.getColumnIndex(android.provider.OpenableColumns.DISPLAY_NAME)
                if (nameIndex >= 0) {
                    it.getString(nameIndex)
                } else {
                    "unknown_file"
                }
            } else {
                "unknown_file"
            }
        } ?: "unknown_file"
    }
    
    /**
     * 获取文件扩展名
     */
    private fun getFileExtension(uri: Uri): String {
        val mimeType = context.contentResolver.getType(uri)
        return MimeTypeMap.getSingleton().getExtensionFromMimeType(mimeType) ?: ""
    }
    
    /**
     * 根据扩展名确定文件类型
     */
    private fun determineFileType(extension: String): String {
        val lowerExtension = extension.lowercase()
        
        return when {
            lowerExtension in SUPPORTED_IMAGE_TYPES -> FileType.IMAGE
            lowerExtension in SUPPORTED_DOCUMENT_TYPES -> FileType.DOCUMENT
            lowerExtension in SUPPORTED_AUDIO_TYPES -> FileType.AUDIO
            lowerExtension in SUPPORTED_VIDEO_TYPES -> FileType.VIDEO
            else -> FileType.CUSTOM
        }
    }
    
    /**
     * 检查文件类型是否支持
     */
    fun isFileTypeSupported(uri: Uri): Boolean {
        val extension = getFileExtension(uri).lowercase()
        
        return extension in SUPPORTED_IMAGE_TYPES ||
                extension in SUPPORTED_DOCUMENT_TYPES ||
                extension in SUPPORTED_AUDIO_TYPES ||
                extension in SUPPORTED_VIDEO_TYPES
    }
    
    /**
     * 获取支持的文件类型描述
     */
    fun getSupportedFileTypesDescription(): String {
        return """
            支持的文件类型：
            
            图片: ${SUPPORTED_IMAGE_TYPES.joinToString(", ")}
            文档: ${SUPPORTED_DOCUMENT_TYPES.joinToString(", ")}
            音频: ${SUPPORTED_AUDIO_TYPES.joinToString(", ")}
            视频: ${SUPPORTED_VIDEO_TYPES.joinToString(", ")}
        """.trimIndent()
    }
    
    /**
     * 清理临时文件
     */
    fun cleanupTempFiles() {
        try {
            val tempDir = File(context.cacheDir, TEMP_DIR)
            if (tempDir.exists()) {
                tempDir.listFiles()?.forEach { file ->
                    if (file.isFile) {
                        file.delete()
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to cleanup temp files", e)
        }
    }
    
    /**
     * 获取文件大小（字节）
     */
    fun getFileSize(uri: Uri): Long {
        return try {
            val cursor = context.contentResolver.query(uri, null, null, null, null)
            cursor?.use {
                if (it.moveToFirst()) {
                    val sizeIndex = it.getColumnIndex(android.provider.OpenableColumns.SIZE)
                    if (sizeIndex >= 0) {
                        it.getLong(sizeIndex)
                    } else {
                        0L
                    }
                } else {
                    0L
                }
            } ?: 0L
        } catch (e: Exception) {
            Log.e(TAG, "Failed to get file size", e)
            0L
        }
    }
    
    /**
     * 格式化文件大小
     */
    fun formatFileSize(bytes: Long): String {
        val kb = 1024
        val mb = kb * 1024
        val gb = mb * 1024
        
        return when {
            bytes >= gb -> String.format("%.1f GB", bytes.toDouble() / gb)
            bytes >= mb -> String.format("%.1f MB", bytes.toDouble() / mb)
            bytes >= kb -> String.format("%.1f KB", bytes.toDouble() / kb)
            else -> "$bytes B"
        }
    }
    
    /**
     * 检查文件大小是否在限制范围内
     */
    fun isFileSizeValid(uri: Uri, maxSizeMB: Int = 50): Boolean {
        val fileSize = getFileSize(uri)
        val maxSizeBytes = maxSizeMB * 1024 * 1024
        return fileSize <= maxSizeBytes
    }
}
