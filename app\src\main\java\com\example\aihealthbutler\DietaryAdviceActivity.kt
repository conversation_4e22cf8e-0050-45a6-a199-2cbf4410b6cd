package com.example.aihealthbutler

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Clear
import androidx.compose.material.icons.filled.Search
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.aihealthbutler.ui.theme.AIHealthButlerTheme

class DietaryAdviceActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            AIHealthButlerTheme {
                DietaryAdviceScreen(onBack = { finish() })
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DietaryAdviceScreen(onBack: () -> Unit) {
    var searchText by remember { mutableStateOf("白粥") }
    
    Scaffold(
        modifier = Modifier.fillMaxSize(),
        topBar = {
            CenterAlignedTopAppBar(
                title = {
                    Text(
                        text = "饮食建议",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold
                    )
                },
                navigationIcon = {
                    IconButton(onClick = onBack) {
                        Icon(
                            painter = painterResource(R.drawable.left_arrow),
                            contentDescription = "返回",
                            modifier = Modifier.size(24.dp)
                        )
                    }
                },
                colors = TopAppBarDefaults.centerAlignedTopAppBarColors(
                    containerColor = Color(0xFFF5F6F7)
                )
            )
        },
        containerColor = Color(0xFFF6F8FA)
    ) { innerPadding ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(innerPadding)
                .verticalScroll(rememberScrollState())
                .padding(horizontal = 16.dp)
        ) {
            // 搜索框
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 8.dp),
                shape = RoundedCornerShape(50.dp),
                elevation = CardDefaults.cardElevation(defaultElevation = 0.dp),
                colors = CardDefaults.cardColors(containerColor = Color.White)
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp, vertical = 8.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.Search,
                        contentDescription = "搜索",
                        tint = Color.Gray,
                        modifier = Modifier.size(20.dp)
                    )
                    
                    Spacer(modifier = Modifier.width(8.dp))
                    
                    Text(
                        text = searchText,
                        color = Color.DarkGray,
                        modifier = Modifier.weight(1f)
                    )
                    
                    IconButton(
                        onClick = { searchText = "" },
                        modifier = Modifier.size(20.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Clear,
                            contentDescription = "清除",
                            tint = Color.LightGray
                        )
                    }
                    
                    Spacer(modifier = Modifier.width(8.dp))
                    
                    Divider(
                        modifier = Modifier
                            .height(24.dp)
                            .width(1.dp),
                        color = Color.LightGray
                    )
                    
                    IconButton(
                        onClick = { /* 筛选功能 */ },
                        modifier = Modifier.size(25.dp)
                    ) {
                        Icon(
                            painter = painterResource(R.drawable.ic_filter),
                            contentDescription = "筛选",
                            tint = Color(0xFF4285F4),
                        )
                    }
                }
            }
            
            // 推荐食物标题
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 12.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "推荐食物",
                    fontSize = 17.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF333333)
                )
                
                Spacer(modifier = Modifier.width(12.dp))
                
                Text(
                    text = "点击'换一批'，获取全新搭配方案",
                    fontSize = 13.sp,
                    color = Color.Gray
                )
            }
            
            // 早餐卡片
            FoodCard(
                imageRes = R.drawable.breakfast_porridge,
                title = "早餐-南瓜百合粥",
                calories = "热量: 约 200 千卡/份",
                description = "香甜的南瓜搭配淘洗的百合，熬成软糯的粥。富含维生素、膳食纤维等营养，口感清甜，易于消化，开启活力清晨。"
            )
            
            // 午餐卡片
            FoodCard(
                imageRes = R.drawable.lunch_beef_potato,
                title = "午餐-土豆炖牛腩",
                calories = "热量: 约 450 千卡/份",
                description = "牛腩炖煮得软烂入味，土豆吸收肉香变得绵密，蛋白质与碳水化合物完美搭配，营养丰富，饱腹感强。"
            )
            
            // 晚餐卡片
            FoodCard(
                imageRes = R.drawable.dinner_spinach_omelette,
                title = "晚餐-菠菜鸡蛋煎饼",
                calories = "热量: 约 300 千卡/份",
                description = "鲜嫩菠菜与鸡蛋融合在面饼中，富含维生素、优质蛋白，制作简单，口感鲜香，是清淡易消化的晚餐佳选。"
            )
            
            // 底部健康提示
            Text(
                text = "根据您的健康状态分析，目前您没有疾病隐患，\n日常饮食应注重清淡营养。",
                fontSize = 13.sp,
                color = Color(0xFF555555),
                textAlign = TextAlign.Center,
                lineHeight = 20.sp,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 24.dp)
            )
        }
    }
}

@Composable
fun FoodCard(
    imageRes: Int,
    title: String,
    calories: String,
    description: String
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 12.dp),
        shape = RoundedCornerShape(12.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
                .height(IntrinsicSize.Min)
        ) {
            // 食物图片
            Image(
                painter = painterResource(imageRes),
                contentDescription = title,
                modifier = Modifier
                    .size(120.dp)
                    .clip(RoundedCornerShape(10.dp)),
                contentScale = ContentScale.Crop
            )
            
            Spacer(modifier = Modifier.width(16.dp))
            
            // 食物信息
            Column(
                modifier = Modifier
                    .weight(1f)
                    .fillMaxHeight(),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Text(
                    text = title,
                    fontSize = 17.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF333333)
                )
                
                Text(
                    text = calories,
                    fontSize = 13.sp,
                    color = Color(0xFF33B3CC)
                )
                
                Text(
                    text = description,
                    fontSize = 13.sp,
                    color = Color(0xFF666666),
                    lineHeight = 18.sp
                )
            }
        }
    }
} 