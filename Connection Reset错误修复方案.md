# Connection Reset错误修复方案

## 🔍 问题分析

### 错误日志分析：
```
java.net.SocketException: Connection reset
at java.net.SocketInputStream.read(SocketInputStream.java:191)
at okhttp3.internal.http1.Http1ExchangeCodec.readResponseHeaders
```

### 根本原因：
1. **服务器主动断开连接** - 在流式请求过程中，服务器端主动关闭了连接
2. **网络不稳定** - 网络波动导致连接中断
3. **超时设置不当** - 读取超时时间过短，长时间的流式响应被中断
4. **连接池配置问题** - HTTP连接复用策略不当

## ✅ 修复方案

### 1. 增强OkHttpClient配置

#### 🔧 优化超时和连接池设置
```kotlin
private val client = OkHttpClient.Builder()
    .connectTimeout(30, TimeUnit.SECONDS)
    .readTimeout(120, TimeUnit.SECONDS)  // ✅ 增加读取超时
    .writeTimeout(60, TimeUnit.SECONDS)
    .retryOnConnectionFailure(true)
    .connectionPool(ConnectionPool(5, 5, TimeUnit.MINUTES))  // ✅ 连接池配置
    .build()
```

#### 🔧 添加Keep-Alive支持
```kotlin
.addNetworkInterceptor { chain ->
    val request = chain.request().newBuilder()
        .addHeader("Connection", "keep-alive")  // ✅ 保持连接
        .addHeader("Keep-Alive", "timeout=30, max=100")
        .build()
    
    try {
        chain.proceed(request)
    } catch (e: java.net.SocketException) {
        // ✅ 网络层重试
        chain.proceed(request)
    }
}
```

### 2. 创建专业的连接重置处理工具

#### 🔧 ConnectionResetHandler工具类
提供智能重试策略：

```kotlin
// 流式操作专用重试
suspend fun streamRetry(operation: String, block: suspend () -> T): T {
    return executeWithRetry(
        operation = operation,
        config = RetryConfig(
            maxRetries = 5,           // 最多5次重试
            baseDelayMs = 1000L,      // 基础延迟1秒
            maxDelayMs = 8000L,       // 最大延迟8秒
            backoffMultiplier = 1.8   // 指数退避
        ),
        block = block
    )
}
```

#### 🔧 智能异常判断
```kotlin
private fun isRetryableException(exception: Exception): Boolean {
    return when (exception) {
        is SocketException -> {
            val message = exception.message?.lowercase() ?: ""
            message.contains("connection reset") ||
            message.contains("broken pipe") ||
            message.contains("connection refused")
        }
        is IOException -> {
            val message = exception.message?.lowercase() ?: ""
            message.contains("connection reset") ||
            message.contains("timeout") ||
            message.contains("connection closed")
        }
        else -> false
    }
}
```

### 3. 改进流式请求处理

#### 🔧 分层错误处理
```kotlin
// 1. 请求执行层 - 处理连接建立
private fun executeStreamRequest(request: Request): Response {
    repeat(3) { attempt ->
        try {
            return client.newCall(request).execute()
        } catch (e: java.net.SocketException) {
            if (attempt < 2) {
                Thread.sleep(500L * (attempt + 1))  // 递增延迟
            }
        }
    }
}

// 2. 响应处理层 - 处理流式读取
private fun processStreamResponse(response: Response, onData: (StreamChatResponse) -> Unit) {
    response.body?.let { responseBody ->
        val reader = responseBody.charStream().buffered()
        var lineCount = 0
        
        reader.useLines { lines ->
            for (line in lines) {
                try {
                    // 处理每一行数据
                    if (line.startsWith("data: ")) {
                        val jsonData = line.substring(6).trim()
                        if (jsonData.isNotEmpty() && jsonData != "[DONE]") {
                            val streamResponse = gson.fromJson(jsonData, StreamChatResponse::class.java)
                            onData(streamResponse)
                        }
                    }
                } catch (e: JsonSyntaxException) {
                    // ✅ 单行解析失败不中断整个流
                    Log.w(TAG, "Failed to parse line: $line", e)
                }
            }
        }
    }
}
```

### 4. 集成ConnectionResetHandler

#### 🔧 在流式聊天中使用
```kotlin
fun sendChatMessageStream(...): Flow<StreamChatResponse> = flow {
    try {
        // ✅ 使用专业的重试处理工具
        ConnectionResetHandler.streamRetry("Stream Chat Request") {
            val response = executeStreamRequest(httpRequest)
            
            if (!response.isSuccessful) {
                throw IOException("Stream API request failed: ${response.code}")
            }
            
            processStreamResponse(response) { streamResponse ->
                emit(streamResponse)
            }
        }
    } catch (e: Exception) {
        // ✅ 详细的诊断信息
        val diagnosticInfo = ConnectionResetHandler.getDiagnosticInfo(e)
        Log.d(TAG, "Stream chat diagnostic:\n$diagnosticInfo")
        throw e
    }
}
```

## 🛠️ 技术改进

### 1. 连接稳定性增强
- ✅ **增加读取超时时间** - 从60秒增加到120秒
- ✅ **配置连接池** - 优化连接复用策略
- ✅ **Keep-Alive支持** - 保持长连接稳定性
- ✅ **网络层重试** - 在网络拦截器中处理Socket异常

### 2. 智能重试机制
- ✅ **指数退避算法** - 避免频繁重试加重服务器负担
- ✅ **异常类型判断** - 只对可重试的异常进行重试
- ✅ **分层重试策略** - 请求层和响应层分别处理
- ✅ **最大重试限制** - 防止无限重试

### 3. 错误处理优化
- ✅ **详细诊断信息** - 提供完整的错误分析
- ✅ **友好错误消息** - 用户可理解的错误提示
- ✅ **开发者调试信息** - 便于问题排查
- ✅ **异常包装** - 统一的异常处理格式

### 4. 流式处理改进
- ✅ **逐行容错处理** - 单行解析失败不影响整个流
- ✅ **连接中断恢复** - 自动重新建立连接
- ✅ **进度跟踪** - 记录处理进度便于调试
- ✅ **资源管理** - 确保连接和流正确关闭

## 📊 预期效果

### 连接稳定性提升
- 🎯 **减少Connection Reset错误** - 通过Keep-Alive和连接池优化
- 🎯 **提高重试成功率** - 智能重试策略和指数退避
- 🎯 **增强网络容错能力** - 多层错误处理和恢复机制

### 用户体验改善
- 🎯 **减少请求失败** - 自动重试机制
- 🎯 **更快的错误恢复** - 智能重试策略
- 🎯 **清晰的错误提示** - 友好的错误消息

### 开发调试便利
- 🎯 **详细的错误诊断** - 完整的异常分析报告
- 🎯 **分层错误追踪** - 精确定位问题层级
- 🎯 **性能监控** - 重试次数和延迟统计

## 🔧 使用指南

### 1. 自动重试
```kotlin
// 系统会自动处理Connection Reset错误
val response = difyApiClient.sendChatMessageStream(
    query = "Hello",
    user = "user123"
)
```

### 2. 手动重试
```kotlin
// 对于特殊场景，可以手动使用重试工具
val result = ConnectionResetHandler.streamRetry("Custom Operation") {
    // 你的操作代码
}
```

### 3. 错误诊断
```kotlin
// 获取详细的错误诊断信息
try {
    // 网络操作
} catch (e: Exception) {
    val diagnostic = ConnectionResetHandler.getDiagnosticInfo(e)
    Log.d(TAG, diagnostic)
}
```

## 📋 相关文件

- `app/src/main/java/com/example/aihealthbutler/dify/DifyApiClient.kt` - API客户端
- `app/src/main/java/com/example/aihealthbutler/utils/ConnectionResetHandler.kt` - 连接重置处理工具

通过这些改进，应用现在具备了强大的网络连接稳定性和智能的错误恢复能力，能够有效处理Connection Reset等网络异常。🎉
