package com.example.aihealthbutler.api

import android.util.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.withContext
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import java.util.concurrent.TimeUnit
import java.io.IOException
import org.json.JSONObject
import java.io.OutputStreamWriter
import java.net.HttpURLConnection
import java.net.URL
import java.io.BufferedReader
import java.io.InputStreamReader
import okhttp3.MultipartBody
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import com.google.gson.Gson
import com.google.gson.GsonBuilder

/**
 * 数据仓库，负责处理API请求和数据转换
 * 简化版本，不依赖Retrofit
 */
class Repository {
    private val TAG = "Repository"
    private val BASE_URL = "https://qcx.yuneyang.top/api/"
    
    // 添加HTTP日志拦截器
    private val loggingInterceptor = HttpLoggingInterceptor { message ->
        Log.d("HTTP", message)
    }.apply {
        level = HttpLoggingInterceptor.Level.BODY
    }

    private val okHttpClient = OkHttpClient.Builder()
        .addInterceptor(loggingInterceptor)
        .connectTimeout(30, TimeUnit.SECONDS)
        .readTimeout(30, TimeUnit.SECONDS)
        .writeTimeout(30, TimeUnit.SECONDS)
        .build()

    // 创建宽松的Gson实例
    private val gson = GsonBuilder()
        .setLenient()
        .create()

    private val retrofit = Retrofit.Builder()
        .baseUrl(BASE_URL)
        .client(okHttpClient)
        .addConverterFactory(GsonConverterFactory.create(gson))
        .build()

    private val apiService = retrofit.create(ApiService::class.java)
    
    // 注册
    suspend fun register(request: RegisterRequest): Result<RegisterResponse> = try {
        val response = apiService.register(request)
        Result.success(response)
    } catch (e: Exception) {
        Result.failure(e)
    }
    
    // 登录
    suspend fun login(request: LoginRequest): Result<LoginResponse> = try {
        val response = apiService.login(request)
        Result.success(response)
    } catch (e: Exception) {
        Result.failure(e)
    }

    // 添加账号下家庭成员 - 修正参数
    suspend fun addMember(token: String, request: AddMemberRequest): Result<AddMemberResponse> = try {
        val formattedToken = if (!token.startsWith("Bearer ")) "Bearer $token" else token
        Log.d("Repository", "调用添加成员API: $request")
        Log.d("Repository", "使用token: ${formattedToken.take(20)}...")
        val response = apiService.addMember(formattedToken, request)
        Log.d("Repository", "添加成员API响应: code=${response.code}, message=${response.message}")
        Result.success(response)
    } catch (e: Exception) {
        Log.e("Repository", "添加成员API调用异常", e)
        Result.failure(e)
    }

    // 切换家庭成员
    suspend fun switchMember(token: String, request: SwitchMemberRequest): Result<SwitchMemberResponse> = try {
        val formattedToken = if (!token.startsWith("Bearer ")) "Bearer $token" else token
        val response = apiService.switchMember(formattedToken, request)
        Result.success(response)
    } catch (e: Exception) {
        Result.failure(e)
    }

    // 创建个人信息
    suspend fun createPersonInfo(token: String, request: PersonInfoRequest): Result<PersonInfoResponse> = try {
        val formattedToken = if (!token.startsWith("Bearer ")) "Bearer $token" else token
        val response = apiService.createPersonInfo(formattedToken, request)
        Result.success(response)
    } catch (e: Exception) {
        Result.failure(e)
    }

    // 删除个人信息
    suspend fun deletePersonInfo(token: String, defaultId: String): Result<BaseResponse> = try {
        val formattedToken = if (!token.startsWith("Bearer ")) "Bearer $token" else token
        val response = apiService.deletePersonInfo(formattedToken, defaultId)
        Result.success(response)
    } catch (e: Exception) {
        Result.failure(e)
    }

    // 更新个人信息
    suspend fun updatePersonInfo(token: String, defaultId: String, request: PersonInfoRequest): Result<PersonInfoResponse> = try {
        val formattedToken = if (!token.startsWith("Bearer ")) "Bearer $token" else token
        val response = apiService.updatePersonInfo(formattedToken, defaultId, request)
        Result.success(response)
    } catch (e: Exception) {
        Result.failure(e)
    }

    // 获取个人信息
    suspend fun getPersonInfo(token: String, defaultId: String): Result<PersonInfoResponse> = try {
        val formattedToken = if (!token.startsWith("Bearer ")) "Bearer $token" else token
        val response = apiService.getPersonInfo(formattedToken, defaultId)
        Result.success(response)
    } catch (e: Exception) {
        Result.failure(e)
    }

    // 新的家庭关系接口
    // 设置家庭关系
    suspend fun setFamilyRelationship(token: String, request: SetFamilyRelationshipRequest): Result<SetFamilyRelationshipResponse> = try {
        val formattedToken = if (!token.startsWith("Bearer ")) "Bearer $token" else token
        Log.d("Repository", "调用设置家庭关系API: $request")
        Log.d("Repository", "使用token: ${formattedToken.take(20)}...")

        val response = apiService.setFamilyRelationship(formattedToken, request)

        Log.d("Repository", "API响应状态: ${response.code()}")

        if (response.isSuccessful) {
            val responseBody = response.body()?.string() ?: ""
            Log.d("Repository", "API响应内容: $responseBody")

            val apiResponse = SetFamilyRelationshipResponse(
                code = 200,
                message = responseBody.takeIf { it.isNotBlank() } ?: "关系设置成功",
                data = null,
                timestamp = System.currentTimeMillis()
            )

            Result.success(apiResponse)
        } else {
            val errorBody = response.errorBody()?.string() ?: ""
            Log.e("Repository", "API调用失败: ${response.code()}, 错误: $errorBody")

            val apiResponse = SetFamilyRelationshipResponse(
                code = response.code(),
                message = errorBody.takeIf { it.isNotBlank() } ?: "设置家庭关系失败",
                data = null,
                timestamp = System.currentTimeMillis()
            )

            Result.success(apiResponse)
        }
    } catch (e: Exception) {
        Log.e("Repository", "设置家庭关系API调用异常", e)
        Result.failure(e)
    }


    // 获取当前用户存在的所有关系
    suspend fun getAllRelationships(token: String, defaultId: String): Result<GetAllRelationshipsResponse> = try {
        val formattedToken = if (!token.startsWith("Bearer ")) "Bearer $token" else token
        val response = apiService.getAllRelationships(formattedToken, defaultId)

        Log.d("Repository", "关系API响应状态: ${response.code()}")

        if (response.isSuccessful) {
            val responseBody = response.body()?.string() ?: "{}"
            Log.d("Repository", "关系API响应内容: $responseBody")

            try {
                // 解析JSON对象为Map
                val gson = com.google.gson.Gson()
                val relationshipMap = gson.fromJson(responseBody, object : com.google.gson.reflect.TypeToken<Map<String, String>>() {}.type) as Map<String, String>

                val apiResponse = GetAllRelationshipsResponse(
                    code = 200,
                    message = "获取关系成功",
                    data = relationshipMap,
                    timestamp = System.currentTimeMillis()
                )

                Log.d("Repository", "解析后的关系数据: ${apiResponse.data}")
                Result.success(apiResponse)
            } catch (e: Exception) {
                Log.e("Repository", "解析关系数据失败", e)
                val apiResponse = GetAllRelationshipsResponse(
                    code = 500,
                    message = "解析关系数据失败: ${e.message}",
                    data = emptyMap(),
                    timestamp = System.currentTimeMillis()
                )
                Result.success(apiResponse)
            }
        } else {
            val errorBody = response.errorBody()?.string() ?: ""
            Log.e("Repository", "关系API调用失败: ${response.code()}, 错误: $errorBody")

            val apiResponse = GetAllRelationshipsResponse(
                code = response.code(),
                message = errorBody.takeIf { it.isNotBlank() } ?: "获取关系失败",
                data = emptyMap(),
                timestamp = System.currentTimeMillis()
            )

            Result.success(apiResponse)
        }
    } catch (e: Exception) {
        Log.e("Repository", "获取关系异常", e)
        Result.failure(e)
    }

    // 保持向后兼容的旧方法（标记为废弃）
    @Deprecated("使用新的setFamilyRelationship方法")
    suspend fun createFamilyRelation(token: String, request: CreateFamilyRequest): Result<CreateFamilyResponse> = try {
        val formattedToken = if (!token.startsWith("Bearer ")) "Bearer $token" else token
        val response = apiService.createFamilyRelation(formattedToken, request)
        Result.success(response)
    } catch (e: Exception) {
        Result.failure(e)
    }

    // 删除家庭关系
    suspend fun deleteFamilyRelation(token: String, defaultId: String): Result<DeleteFamilyResponse> = try {
        val formattedToken = if (!token.startsWith("Bearer ")) "Bearer $token" else token
        val response = apiService.deleteFamilyRelation(formattedToken, defaultId)
        Result.success(response)
    } catch (e: Exception) {
        Result.failure(e)
    }

    // 删除家庭成员
    suspend fun deleteFamilyMember(token: String, defaultId: String): Result<DeleteFamilyResponse> = try {
        val formattedToken = if (!token.startsWith("Bearer ")) "Bearer $token" else token
        val response = apiService.deleteFamilyMember(formattedToken, defaultId)
        Result.success(response)
    } catch (e: Exception) {
        Result.failure(e)
    }

    @Deprecated("使用新的setFamilyRelationship方法")
    suspend fun updateFamilyRelation(token: String, defaultId: String, request: UpdateFamilyRequest): Result<UpdateFamilyResponse> = try {
        val formattedToken = if (!token.startsWith("Bearer ")) "Bearer $token" else token
        val response = apiService.updateFamilyRelation(formattedToken, defaultId, request)
        Result.success(response)
    } catch (e: Exception) {
        Result.failure(e)
    }

    @Deprecated("使用新的getAllRelationships方法")
    suspend fun getFamilyRelation(token: String, defaultId: String): Result<GetFamilyResponse> = try {
        val formattedToken = if (!token.startsWith("Bearer ")) "Bearer $token" else token
        val response = apiService.getFamilyRelation(formattedToken, defaultId)
        Result.success(response)
    } catch (e: Exception) {
        Result.failure(e)
    }

    // 获取所有家庭成员信息
    suspend fun getAllFamilyMembers(token: String, account: String): Result<GetAllFamilyMembersResponse> = try {
        val formattedToken = if (!token.startsWith("Bearer ")) "Bearer $token" else token
        val response = apiService.getAllFamilyMembers(formattedToken, account)
        Result.success(response)
    } catch (e: Exception) {
        Result.failure(e)
    }

    // 健康信息相关模型

    // 获取健康信息
    suspend fun getHealthInfo(token: String, defaultId: String): Result<HealthInfoResponse> = try {
        val formattedToken = if (!token.startsWith("Bearer ")) "Bearer $token" else token
        val response = apiService.getHealthInfo(formattedToken, defaultId)
        Result.success(response)
    } catch (e: Exception) {
        Result.failure(e)
    }

    // 创建健康信息
    suspend fun createHealthInfo(token: String, request: CreateHealthRequest): Result<BaseResponse> = try {
        val formattedToken = if (!token.startsWith("Bearer ")) "Bearer $token" else token
        val response = apiService.createHealthInfo(formattedToken, request)
        Result.success(response)
    } catch (e: Exception) {
        Result.failure(e)
    }

    // 删除健康信息
    suspend fun deleteHealthInfo(token: String, healthId: Int): Result<BaseResponse> = try {
        val formattedToken = if (!token.startsWith("Bearer ")) "Bearer $token" else token
        val response = apiService.deleteHealthInfo(formattedToken, healthId)
        Result.success(response)
    } catch (e: Exception) {
        Result.failure(e)
    }

    // 更新健康信息
    suspend fun updateHealthInfo(token: String, request: UpdateHealthRequest): Result<BaseResponse> = try {
        val formattedToken = if (!token.startsWith("Bearer ")) "Bearer $token" else token
        val response = apiService.updateHealthInfo(formattedToken, request)
        Result.success(response)
    } catch (e: Exception) {
        Result.failure(e)
    }

    // 创建手术史记录
    suspend fun createSurgeryHistory(token: String, request: CreateSurgeryRequest): Result<BaseResponse> = try {
        val formattedToken = if (!token.startsWith("Bearer ")) "Bearer $token" else token
        val response = apiService.createSurgeryHistory(formattedToken, request)
        Result.success(response)
    } catch (e: Exception) {
        Result.failure(e)
    }

    // 删除手术史记录
    suspend fun deleteSurgeryHistory(token: String, surgeryId: Int): Result<BaseResponse> = try {
        val formattedToken = if (!token.startsWith("Bearer ")) "Bearer $token" else token
        val response = apiService.deleteSurgeryHistory(formattedToken, surgeryId)
        Result.success(response)
    } catch (e: Exception) {
        Result.failure(e)
    }

    // 更新手术史记录
    suspend fun updateSurgeryHistory(token: String, request: UpdateSurgeryRequest): Result<BaseResponse> = try {
        val formattedToken = if (!token.startsWith("Bearer ")) "Bearer $token" else token
        val response = apiService.updateSurgeryHistory(formattedToken, request)
        Result.success(response)
    } catch (e: Exception) {
        Result.failure(e)
    }

    // 获取手术史记录列表
    suspend fun getSurgeryHistories(token: String, healthId: Int): Result<SurgeryHistoryListResponse> = try {
        val formattedToken = if (!token.startsWith("Bearer ")) "Bearer $token" else token
        val response = apiService.getSurgeryHistories(formattedToken, healthId)
        Result.success(response)
    } catch (e: Exception) {
        Result.failure(e)
    }

    // 创建用药计划
    suspend fun createMedicationPlan(token: String, request: MedicationPlanRequest): Result<MedicationPlanResponse> = try {
        val formattedToken = if (!token.startsWith("Bearer ")) "Bearer $token" else token
        val response = apiService.createMedicationPlan(formattedToken, request)
        Result.success(response)
    } catch (e: Exception) {
        Result.failure(e)
    }

    // 更新用药计划
    suspend fun updateMedicationPlan(token: String, mplanId: Int, request: MedicationPlanRequest): Result<MedicationPlanResponse> = try {
        val formattedToken = if (!token.startsWith("Bearer ")) "Bearer $token" else token
        val response = apiService.updateMedicationPlan(formattedToken, mplanId, request)
        Result.success(response)
    } catch (e: Exception) {
        Result.failure(e)
    }

    // 获取用药计划
    suspend fun getMedicationPlan(token: String, mplanId: Int): Result<MedicationPlanDetail> = try {
        val formattedToken = if (!token.startsWith("Bearer ")) "Bearer $token" else token
        val response = apiService.getMedicationPlan(formattedToken, mplanId)
        Result.success(response)
    } catch (e: Exception) {
        Result.failure(e)
    }

    // 获取用户所有用药计划
    suspend fun getMedicationPlans(token: String, defaultId: String): Result<MedicationPlanListResponse> = try {
        val formattedToken = if (!token.startsWith("Bearer ")) "Bearer $token" else token
        val response = apiService.getMedicationPlans(formattedToken, defaultId)
        Result.success(response)
    } catch (e: Exception) {
        Result.failure(e)
    }

    // 创建资料夹
    suspend fun createFolder(token: String, request: CreateFolderRequest): Result<StandardFolderResponse> = try {
        val formattedToken = if (!token.startsWith("Bearer ")) "Bearer $token" else token
        val response = apiService.createFolder(formattedToken, request)
        Result.success(response)
    } catch (e: Exception) {
        Result.failure(e)
    }

    // 删除资料夹
    suspend fun deleteFolder(token: String, folderId: Int): Result<FolderDeleteResponse> = try {
        val formattedToken = if (!token.startsWith("Bearer ")) "Bearer $token" else token
        val response = apiService.deleteFolder(formattedToken, folderId)
        Result.success(response)
    } catch (e: Exception) {
        Result.failure(e)
    }

    // 根据用户ID删除资料夹
    suspend fun deleteFolderByUserId(token: String, defaultId: String): Result<FolderDeleteResponse> = try {
        val formattedToken = if (!token.startsWith("Bearer ")) "Bearer $token" else token
        val response = apiService.deleteFolderByUserId(formattedToken, defaultId)
        Result.success(response)
    } catch (e: Exception) {
        Result.failure(e)
    }

    // 更新资料夹
    suspend fun updateFolder(token: String, folderId: Int, request: CreateFolderRequest): Result<StandardFolderResponse> = try {
        val formattedToken = if (!token.startsWith("Bearer ")) "Bearer $token" else token
        val response = apiService.updateFolder(formattedToken, folderId, request)
        Result.success(response)
    } catch (e: Exception) {
        Result.failure(e)
    }

    // 获取单个资料夹
    suspend fun getFolder(token: String, folderId: Int): Result<StandardFolderResponse> = try {
        val formattedToken = if (!token.startsWith("Bearer ")) "Bearer $token" else token
        val response = apiService.getFolder(formattedToken, folderId)
        Result.success(response)
    } catch (e: Exception) {
        Result.failure(e)
    }

    // 根据用户ID获取资料夹
    suspend fun getFoldersByUserId(token: String, defaultId: String): Result<StandardFolderResponse> = try {
        val formattedToken = if (!token.startsWith("Bearer ")) "Bearer $token" else token
        val response = apiService.getFoldersByUserId(formattedToken, defaultId)
        Result.success(response)
    } catch (e: Exception) {
        Result.failure(e)
    }

    // 删除用药计划
    suspend fun deleteMedicationPlan(token: String, mplanId: Int): Result<BaseResponse> = try {
        val formattedToken = if (!token.startsWith("Bearer ")) "Bearer $token" else token
        val response = apiService.deleteMedicationPlan(formattedToken, mplanId)
        Result.success(response)
    } catch (e: Exception) {
        Result.failure(e)
    }

    // AI数据相关接口 - 暂未实现
    // suspend fun getAiData(token: String): Result<AiDataResponse> {
    //     return withContext(Dispatchers.IO) {
    //         try {
    //             val authToken = if (!token.startsWith("Bearer ")) "Bearer $token" else token
    //             val response = apiService.getAiData(authToken)
    //             if (response.code == 200) {
    //                 Result.success(response)
    //             } else {
    //                 Result.failure(Exception(response.msg ?: "获取AI数据失败"))
 
   //             }
    //         } catch (e: Exception) {
    //             Result.failure(Exception("获取AI数据异常: ${e.message}"))
    //         }
    //     }
    // }

}















