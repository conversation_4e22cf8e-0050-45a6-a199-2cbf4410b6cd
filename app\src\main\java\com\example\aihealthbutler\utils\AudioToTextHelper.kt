package com.example.aihealthbutler.utils

import android.content.Context
import android.media.MediaMetadataRetriever
import android.net.Uri
import android.util.Log
import com.example.aihealthbutler.dify.DifyApiClient
import com.example.aihealthbutler.dify.DifyConfig
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileOutputStream
import java.io.IOException

/**
 * 语音转文字辅助工具
 * 提供音频文件处理和语音识别功能
 */
object AudioToTextHelper {
    
    private const val TAG = "AudioToTextHelper"
    
    // 支持的音频格式
    val SUPPORTED_FORMATS = listOf("mp3", "mp4", "mpeg", "mpga", "m4a", "wav", "webm")
    
    // 文件大小限制 (15MB)
    const val MAX_FILE_SIZE_BYTES = 15 * 1024 * 1024
    
    /**
     * 验证音频文件
     */
    fun validateAudioFile(file: File): AudioValidationResult {
        return try {
            // 检查文件是否存在
            if (!file.exists()) {
                return AudioValidationResult(false, "音频文件不存在")
            }
            
            // 检查文件大小
            if (file.length() > MAX_FILE_SIZE_BYTES) {
                val sizeMB = file.length() / (1024 * 1024)
                return AudioValidationResult(false, "音频文件过大: ${sizeMB}MB，最大支持15MB")
            }
            
            // 检查文件格式
            val extension = file.extension.lowercase()
            if (!SUPPORTED_FORMATS.contains(extension)) {
                return AudioValidationResult(false, "不支持的音频格式: $extension，支持的格式: $SUPPORTED_FORMATS")
            }
            
            AudioValidationResult(true, "音频文件验证通过")
            
        } catch (e: Exception) {
            Log.e(TAG, "音频文件验证失败", e)
            AudioValidationResult(false, "音频文件验证异常: ${e.message}")
        }
    }
    
    /**
     * 获取音频文件信息
     */
    fun getAudioFileInfo(file: File): AudioFileInfo {
        return try {
            val retriever = MediaMetadataRetriever()
            retriever.setDataSource(file.absolutePath)
            
            val duration = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION)?.toLongOrNull() ?: 0L
            val bitrate = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_BITRATE)?.toIntOrNull() ?: 0
            val sampleRate = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_CAPTURE_FRAMERATE)?.toIntOrNull() ?: 0
            
            retriever.release()
            
            AudioFileInfo(
                fileName = file.name,
                fileSize = file.length(),
                duration = duration,
                bitrate = bitrate,
                sampleRate = sampleRate,
                format = file.extension.lowercase()
            )
            
        } catch (e: Exception) {
            Log.e(TAG, "获取音频文件信息失败", e)
            AudioFileInfo(
                fileName = file.name,
                fileSize = file.length(),
                duration = 0L,
                bitrate = 0,
                sampleRate = 0,
                format = file.extension.lowercase()
            )
        }
    }
    
    /**
     * 从URI复制音频文件到临时目录
     */
    suspend fun copyAudioFromUri(
        context: Context,
        uri: Uri,
        fileName: String? = null
    ): File = withContext(Dispatchers.IO) {
        try {
            val inputStream = context.contentResolver.openInputStream(uri)
                ?: throw IOException("无法打开音频文件")
            
            // 生成临时文件名
            val tempFileName = fileName ?: "audio_${System.currentTimeMillis()}.${getFileExtensionFromUri(context, uri)}"
            val tempFile = File(context.cacheDir, tempFileName)
            
            // 复制文件
            FileOutputStream(tempFile).use { outputStream ->
                inputStream.use { input ->
                    input.copyTo(outputStream)
                }
            }
            
            Log.d(TAG, "音频文件复制完成: ${tempFile.absolutePath}, 大小: ${tempFile.length()} bytes")
            tempFile
            
        } catch (e: Exception) {
            Log.e(TAG, "复制音频文件失败", e)
            throw IOException("复制音频文件失败: ${e.message}", e)
        }
    }
    
    /**
     * 从URI获取文件扩展名
     */
    private fun getFileExtensionFromUri(context: Context, uri: Uri): String {
        return try {
            val mimeType = context.contentResolver.getType(uri)
            when (mimeType) {
                "audio/mpeg" -> "mp3"
                "audio/mp4" -> "m4a"
                "audio/wav" -> "wav"
                "audio/webm" -> "webm"
                else -> "mp3" // 默认扩展名
            }
        } catch (e: Exception) {
            Log.w(TAG, "无法获取文件扩展名，使用默认值", e)
            "mp3"
        }
    }
    
    /**
     * 执行语音转文字
     */
    suspend fun convertAudioToText(
        audioFile: File,
        user: String,
        apiClient: DifyApiClient? = null
    ): AudioToTextResult = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "开始语音转文字: ${audioFile.name}")
            
            // 验证音频文件
            val validation = validateAudioFile(audioFile)
            if (!validation.isValid) {
                return@withContext AudioToTextResult(
                    success = false,
                    text = "",
                    error = validation.message
                )
            }
            
            // 获取音频文件信息
            val audioInfo = getAudioFileInfo(audioFile)
            Log.d(TAG, "音频文件信息: $audioInfo")
            
            // 使用API客户端进行转换
            val client = apiClient ?: DifyApiClient(DifyConfig.API_KEY, DifyConfig.BASE_URL)
            val response = client.audioToText(audioFile, user)
            
            Log.d(TAG, "语音转文字成功: ${response.text}")
            
            AudioToTextResult(
                success = true,
                text = response.text,
                audioInfo = audioInfo
            )
            
        } catch (e: Exception) {
            Log.e(TAG, "语音转文字失败", e)
            AudioToTextResult(
                success = false,
                text = "",
                error = "语音转文字失败: ${e.message}"
            )
        }
    }
    
    /**
     * 清理临时音频文件
     */
    fun cleanupTempFiles(context: Context) {
        try {
            val cacheDir = context.cacheDir
            val audioFiles = cacheDir.listFiles { file ->
                file.name.startsWith("audio_") && SUPPORTED_FORMATS.any { 
                    file.name.endsWith(".$it", ignoreCase = true) 
                }
            }
            
            audioFiles?.forEach { file ->
                if (file.delete()) {
                    Log.d(TAG, "删除临时音频文件: ${file.name}")
                }
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "清理临时文件失败", e)
        }
    }
}

/**
 * 音频验证结果
 */
data class AudioValidationResult(
    val isValid: Boolean,
    val message: String
)

/**
 * 音频文件信息
 */
data class AudioFileInfo(
    val fileName: String,
    val fileSize: Long,
    val duration: Long, // 毫秒
    val bitrate: Int,
    val sampleRate: Int,
    val format: String
) {
    fun getFormattedSize(): String {
        return when {
            fileSize < 1024 -> "${fileSize}B"
            fileSize < 1024 * 1024 -> "${fileSize / 1024}KB"
            else -> "${fileSize / (1024 * 1024)}MB"
        }
    }
    
    fun getFormattedDuration(): String {
        val seconds = duration / 1000
        val minutes = seconds / 60
        val remainingSeconds = seconds % 60
        return "${minutes}:${String.format("%02d", remainingSeconds)}"
    }
}

/**
 * 语音转文字结果
 */
data class AudioToTextResult(
    val success: Boolean,
    val text: String,
    val error: String? = null,
    val audioInfo: AudioFileInfo? = null
) {
    fun getDetailedInfo(): String {
        return buildString {
            appendLine("=== 语音转文字结果 ===")
            appendLine("状态: ${if (success) "✓ 成功" else "✗ 失败"}")
            
            if (success) {
                appendLine("识别文本: $text")
                audioInfo?.let { info ->
                    appendLine("音频信息:")
                    appendLine("  文件名: ${info.fileName}")
                    appendLine("  大小: ${info.getFormattedSize()}")
                    appendLine("  时长: ${info.getFormattedDuration()}")
                    appendLine("  格式: ${info.format}")
                    if (info.bitrate > 0) appendLine("  比特率: ${info.bitrate}")
                }
            } else {
                appendLine("错误信息: $error")
            }
        }
    }
}
