package com.example.aihealthbutler

import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.aihealthbutler.ui.theme.AIHealthButlerTheme
import kotlinx.coroutines.delay

class FirstUserPopupActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            AIHealthButlerTheme {
                FirstUserPopupScreen()
            }
        }
    }
}

@Composable
fun FirstUserPopupScreen() {
    val context = LocalContext.current
    
    // 弹窗显示状态
    var showSheet by remember { mutableStateOf(false) }
    
    // 延迟3.5秒后显示弹窗
    LaunchedEffect(Unit) {
        delay(3500)
        showSheet = true
    }
    
    // 主界面 - 浅灰色背景
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color(0xFFF5F5F5)),
        contentAlignment = Alignment.Center
    ) {
        // 竖向排列的"AI健康管家"文本
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            val title = "AI健康管家"
            title.forEach { char ->
                Text(
                    text = char.toString(),
                    fontSize = 28.sp,
                    fontWeight = FontWeight.Bold,
                    letterSpacing = 3.sp,
                    color = Color(0xFF1CCCB7),
                    modifier = Modifier.padding(vertical = 4.dp)
                )
            }
        }
    }
    
    // 使用自定义Dialog实现半屏弹窗效果
    if (showSheet) {
        CustomBottomSheetDialog(
            onDismissRequest = { showSheet = false },
            onContinue = {
                val intent = Intent(context, SignInActivity::class.java)
                context.startActivity(intent)
                (context as? ComponentActivity)?.finish()
            },
            onDecline = {
                (context as? ComponentActivity)?.finish()
            }
        )
    }
}

@Composable
fun CustomBottomSheetDialog(
    onDismissRequest: () -> Unit,
    onContinue: () -> Unit,
    onDecline: () -> Unit
) {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.BottomCenter
    ) {
        // 主对话框
        Surface(
            modifier = Modifier
                .fillMaxWidth()
                .fillMaxHeight(0.60f), // 增加高度占比
            shape = RoundedCornerShape(topStart = 20.dp, topEnd = 20.dp), // 增加圆角
            color = Color.White,
            shadowElevation = 8.dp
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 24.dp, bottom = 30.dp)
            ) {
                // 标题
                Text(
                    text = "温馨提示",
                    fontSize = 22.sp,
                    color = Color.Black,
                    textAlign = TextAlign.Center,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 16.dp)
                )
                
                // 分割线
                Divider(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 24.dp),
                    thickness = 0.8.dp,
                    color = Color.LightGray
                )
                
                // 内容区域
                Column(
                    modifier = Modifier.padding(
                        horizontal = 24.dp,
                        vertical = 20.dp
                    )
                ) {
                    // 第一段
                    Text(
                        text = "欢迎使用AI健康管家，在使用前，请认真阅读《用户服务协议》《隐私政策》。",
                        fontSize = 15.sp,
                        color = Color.Black,
                        lineHeight = 24.sp,
                        modifier = Modifier.padding(bottom = 16.dp)
                    )
                    
                    // 第二段
                    Text(
                        text = " 本服务提供的信息，仅供您参考，不得作为诊断、治疗的直接医疗处置依据，该信息并不构成诊疗意见，更不构成处方或电子处方。您应当自行承担通过该服务获得的任何医疗信息或建议使用所产生的风险和责任，包括但不限于自我诊断、自我治疗或错误使用药物。如您有任何疑问或不适，一定要及时咨询专业医生的意见和建议。",
                        fontSize = 15.sp,
                        color = Color.DarkGray,
                        lineHeight = 22.sp,
                        modifier = Modifier.padding(bottom = 16.dp)
                    )
                    
                    // 第三段
                    Text(
                        text = "建议您除了使用此应用程序外，在做出任何医疗决定之前，应寻求医生的建议。",
                        fontSize = 15.sp,
                        color = Color.DarkGray,
                        lineHeight = 22.sp
                    )
                }
                
                // 底部按钮区域
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 24.dp, vertical = 24.dp),
                    horizontalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    // 不同意按钮（左侧）
                    OutlinedButton(
                        onClick = onDecline,
                        modifier = Modifier
                            .weight(1f)
                            .height(48.dp),
                        shape = RoundedCornerShape(8.dp),
                        colors = ButtonDefaults.outlinedButtonColors(
                            contentColor = Color.DarkGray
                        ),
                        border = BorderStroke(
                            width = 1.dp,
                            color = Color.LightGray
                        )
                    ) {
                        Text(
                            text = "不同意",
                            fontSize = 16.sp
                        )
                    }
                    
                    // 同意并继续按钮（右侧）
                    Button(
                        onClick = onContinue,
                        modifier = Modifier
                            .weight(1f)
                            .height(48.dp),
                        shape = RoundedCornerShape(8.dp),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFF1CCCB7)
                        )
                    ) {
                        Text(
                            text = "同意并继续",
                            fontSize = 16.sp,
                            color = Color.White
                        )
                    }
                }
            }
        }
    }
}
