# 聊天 API 集成开发文档

## 1. API 接口文档

### 1.1 基本信息

- **接口地址**: `https://qcx.yuneyang.top/v1/chat-messages`
- **请求方法**: `POST`
- **内容类型**: `application/json`
- **鉴权方式**: Bearer Token

### 1.2 请求头

```
Content-Type: application/json
Authorization: Bearer app-nITiKdMvmPwBlTlOhRy98MEg
```

### 1.3 请求参数

```json
{
  "inputs": {
    "token": "1"
  },
  "query": "你好",
  "response_mode": "blocking",
  "conversation_id": "",
  "user": "abc-123"
}
```

#### 参数说明

| 参数名          | 类型   | 必填 | 说明                       |
| --------------- | ------ | ---- | -------------------------- |
| inputs          | Object | 是   | 输入参数对象               |
| inputs.token    | String | 是   | 令牌标识                   |
| query           | String | 是   | 用户发送的消息内容         |
| response_mode   | String | 是   | 响应模式，固定值"blocking" |
| conversation_id | String | 否   | 会话 ID，首次对话可为空    |
| user            | String | 是   | 用户标识                   |

### 1.4 响应格式

```json
{
  "event": "message",
  "task_id": "10317d7b-b38e-4802-bbd0-3d0323ca86f8",
  "id": "c878f4a9-3985-4fa9-97bf-ee1e0aee9d30",
  "message_id": "c878f4a9-3985-4fa9-97bf-ee1e0aee9d30",
  "conversation_id": "5d89a321-2a38-4675-b175-821e47fc169f",
  "mode": "advanced-chat",
  "answer": "你好呀！我是你的健康管家...",
  "metadata": {
    "annotation_reply": null,
    "retriever_resources": [],
    "usage": {
      "prompt_tokens": 455,
      "prompt_unit_price": "0.5",
      "prompt_price_unit": "0.000001",
      "prompt_price": "0.0002275",
      "completion_tokens": 44,
      "completion_unit_price": "2",
      "completion_price_unit": "0.000001",
      "completion_price": "0.000088",
      "total_tokens": 499,
      "total_price": "0.0003155",
      "currency": "RMB",
      "latency": 0.9014212839992979
    }
  },
  "created_at": 1753793521
}
```

#### 响应参数说明

| 参数名          | 类型   | 说明                           |
| --------------- | ------ | ------------------------------ |
| event           | String | 事件类型                       |
| task_id         | String | 任务 ID                        |
| id              | String | 消息 ID                        |
| message_id      | String | 消息 ID                        |
| conversation_id | String | 会话 ID，后续对话需要传递此 ID |
| mode            | String | 对话模式                       |
| answer          | String | AI 回复的消息内容              |
| metadata        | Object | 元数据信息                     |
| created_at      | Long   | 创建时间戳                     |
