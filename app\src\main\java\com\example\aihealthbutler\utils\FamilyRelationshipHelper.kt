package com.example.aihealthbutler.utils

import android.util.Log
import com.example.aihealthbutler.api.Repository
import com.example.aihealthbutler.api.SetFamilyRelationshipRequest
import com.example.aihealthbutler.api.GetAllRelationshipsResponse


/**
 * 家庭关系管理工具类
 * 提供新的家庭关系接口的便捷方法
 */
object FamilyRelationshipHelper {
    private const val TAG = "FamilyRelationshipHelper"
    
    /**
     * 设置家庭关系
     * @param repository Repository实例
     * @param token 认证token
     * @param fromMemberId 关系发起者ID
     * @param toMemberId 关系接收者ID  
     * @param relationship 关系名称（如：母亲、父亲、儿子、女儿等）
     * @return 是否设置成功
     */
    suspend fun setRelationship(
        repository: Repository,
        token: String,
        fromMemberId: String,
        toMemberId: String,
        relationship: String
    ): Result<Boolean> {
        return try {
            Log.d(TAG, "设置家庭关系: $fromMemberId 是 $toMemberId 的 $relationship")
            
            val request = SetFamilyRelationshipRequest(
                fromMemberId = fromMemberId,
                toMemberId = toMemberId,
                relationship = relationship
            )
            
            val result = repository.setFamilyRelationship(token, request)
            
            result.fold(
                onSuccess = { response ->
                    if (response.code == 200) {
                        Log.d(TAG, "设置家庭关系成功")
                        Result.success(true)
                    } else {
                        Log.e(TAG, "设置家庭关系失败: ${response.message}")
                        Result.failure(Exception(response.message))
                    }
                },
                onFailure = { exception ->
                    Log.e(TAG, "设置家庭关系请求失败", exception)
                    Result.failure(exception)
                }
            )
        } catch (e: Exception) {
            Log.e(TAG, "设置家庭关系异常", e)
            Result.failure(e)
        }
    }
    
    /**
     * 获取用户的所有家庭关系
     * @param repository Repository实例
     * @param token 认证token
     * @param defaultId 用户ID
     * @return 家庭关系Map，格式为 userId -> 关系名称
     */
    suspend fun getAllRelationships(
        repository: Repository,
        token: String,
        defaultId: String
    ): Result<Map<String, String>> {
        return try {
            Log.d(TAG, "获取用户 $defaultId 的所有家庭关系")

            val result = repository.getAllRelationships(token, defaultId)

            result.fold(
                onSuccess = { response ->
                    if (response.code == 200) {
                        Log.d(TAG, "获取家庭关系成功，共 ${response.data.size} 条关系")
                        Log.d(TAG, "关系详情: ${response.data}")
                        Result.success(response.data)
                    } else {
                        Log.e(TAG, "获取家庭关系失败: ${response.message}")
                        Result.failure(Exception(response.message))
                    }
                },
                onFailure = { exception ->
                    Log.e(TAG, "获取家庭关系请求失败", exception)
                    Result.failure(exception)
                }
            )
        } catch (e: Exception) {
            Log.e(TAG, "获取家庭关系异常", e)
            Result.failure(e)
        }
    }
    
    /**
     * 常用关系名称
     */
    object RelationshipTypes {
        const val FATHER = "父亲"
        const val MOTHER = "母亲"
        const val SON = "儿子"
        const val DAUGHTER = "女儿"
        const val HUSBAND = "丈夫"
        const val WIFE = "妻子"
        const val GRANDFATHER = "爷爷"
        const val GRANDMOTHER = "奶奶"
        const val GRANDSON = "孙子"
        const val GRANDDAUGHTER = "孙女"
        const val BROTHER = "兄弟"
        const val SISTER = "姐妹"
        
        /**
         * 获取所有可用的关系类型
         */
        fun getAllTypes(): List<String> {
            return listOf(
                FATHER, MOTHER, SON, DAUGHTER,
                HUSBAND, WIFE, GRANDFATHER, GRANDMOTHER,
                GRANDSON, GRANDDAUGHTER, BROTHER, SISTER
            )
        }
        
        /**
         * 验证关系类型是否有效
         */
        fun isValidRelationship(relationship: String): Boolean {
            return getAllTypes().contains(relationship)
        }
    }
    
    /**
     * 关系方向说明
     * 在新接口中：fromMemberId 是 toMemberId 的 relationship
     * 例如：setRelationship(childId, parentId, "母亲") 表示 childId 是 parentId 的母亲
     */
    object RelationshipDirection {
        /**
         * 设置父子关系
         * @param parentId 父/母ID
         * @param childId 子/女ID
         * @param parentGender 父母性别 ("父亲" 或 "母亲")
         */
        suspend fun setParentChildRelation(
            repository: Repository,
            token: String,
            parentId: String,
            childId: String,
            parentGender: String
        ): Result<Boolean> {
            return setRelationship(repository, token, parentId, childId, parentGender)
        }
        
        /**
         * 设置夫妻关系
         * @param husbandId 丈夫ID
         * @param wifeId 妻子ID
         */
        suspend fun setSpouseRelation(
            repository: Repository,
            token: String,
            husbandId: String,
            wifeId: String
        ): Result<Boolean> {
            // 设置双向关系
            val result1 = setRelationship(repository, token, husbandId, wifeId, RelationshipTypes.HUSBAND)
            val result2 = setRelationship(repository, token, wifeId, husbandId, RelationshipTypes.WIFE)
            
            return if (result1.isSuccess && result2.isSuccess) {
                Result.success(true)
            } else {
                Result.failure(Exception("设置夫妻关系失败"))
            }
        }
    }
}
