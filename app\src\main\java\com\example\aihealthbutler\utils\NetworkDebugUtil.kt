package com.example.aihealthbutler.utils

import android.content.Context
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.os.Build
import android.util.Log
import java.io.IOException
import java.net.HttpURLConnection
import java.net.InetSocketAddress
import java.net.InetAddress
import java.net.Socket
import java.net.URL
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import javax.net.ssl.HttpsURLConnection

/**
 * 网络调试工具类，用于诊断网络连接问题
 */
object NetworkDebugUtil {
    private const val TAG = "NetworkDebugUtil"
    private const val SERVER_DOMAIN = "qcx.yuneyang.top"
    private const val API_PORT = 443 // 仅使用HTTPS默认端口
    
    /**
     * 检查网络连接是否可用
     */
    fun isNetworkAvailable(context: Context): Boolean {
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        val networkCapabilities = connectivityManager.getNetworkCapabilities(connectivityManager.activeNetwork)
        
        if (networkCapabilities == null) {
            Log.e(TAG, "❌ 网络不可用：未检测到活动网络")
            return false
        }
        
        val hasInternet = networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
        val hasValidated = networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED)
        
        Log.d(TAG, "🌐 网络状态：互联网=$hasInternet, 已验证=$hasValidated")
        return hasInternet && hasValidated
    }
    
    /**
     * 获取网络类型
     */
    fun getNetworkType(context: Context): String {
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        val networkCapabilities = connectivityManager.getNetworkCapabilities(connectivityManager.activeNetwork)
        
        return when {
            networkCapabilities == null -> "无网络"
            networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) -> "WiFi"
            networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) -> "移动数据"
            networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET) -> "以太网"
            networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_BLUETOOTH) -> "蓝牙"
            networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_VPN) -> "VPN"
            else -> "未知网络"
        }
    }
    
    /**
     * 检查DNS解析
     */
    suspend fun checkDnsResolution(): Result<String> = withContext(Dispatchers.IO) {
        try {
            val startTime = System.currentTimeMillis()
            val inetAddress = java.net.InetAddress.getByName(SERVER_DOMAIN)
            val resolveTime = System.currentTimeMillis() - startTime
            
            val result = "✅ DNS解析成功: $SERVER_DOMAIN -> ${inetAddress.hostAddress}, 耗时: ${resolveTime}ms"
            Log.d(TAG, result)
            Result.success(result)
        } catch (e: Exception) {
            val error = "❌ DNS解析失败: ${e.message}"
            Log.e(TAG, error, e)
            Result.failure(IOException(error))
        }
    }
    
    /**
     * 检查服务器连接
     */
    suspend fun checkServerConnection(): Result<String> = withContext(Dispatchers.IO) {
        val resultBuilder = StringBuilder()
        
        try {
            // 首先尝试DNS解析
            try {
                val dnsStart = System.currentTimeMillis()
                val inetAddresses = InetAddress.getAllByName(SERVER_DOMAIN)
                val dnsTime = System.currentTimeMillis() - dnsStart
                
                if (inetAddresses.isNotEmpty()) {
                    val ips = inetAddresses.joinToString(", ") { it.hostAddress }
                    val dnsMsg = "✅ DNS解析成功: $SERVER_DOMAIN -> [$ips], 耗时: ${dnsTime}ms"
                    Log.d(TAG, dnsMsg)
                    resultBuilder.append("$dnsMsg\n")
                } else {
                    val dnsMsg = "⚠️ DNS解析未返回IP地址: $SERVER_DOMAIN"
                    Log.w(TAG, dnsMsg)
                    resultBuilder.append("$dnsMsg\n")
                }
            } catch (e: Exception) {
                val dnsError = "❌ DNS解析失败: ${e.message}"
                Log.e(TAG, dnsError, e)
                resultBuilder.append("$dnsError\n")
            }
            
            // 只尝试HTTPS连接
            Log.d(TAG, "尝试HTTPS连接到 $SERVER_DOMAIN:$API_PORT...")
            resultBuilder.append("尝试HTTPS连接到 $SERVER_DOMAIN:$API_PORT...\n")
            
            try {
                val httpsUrl = URL("https://$SERVER_DOMAIN/api/")
                val connection = httpsUrl.openConnection() as HttpsURLConnection
                
                connection.connectTimeout = 7000
                connection.readTimeout = 7000
                connection.requestMethod = "GET"
                
                val startTime = System.currentTimeMillis()
                
                try {
                    connection.connect()
                    val responseCode = connection.responseCode
                    val connectTime = System.currentTimeMillis() - startTime
                    
                    val successMsg = "✅ HTTPS连接成功: $SERVER_DOMAIN:$API_PORT, 响应码: $responseCode, 耗时: ${connectTime}ms"
                    Log.d(TAG, successMsg)
                    resultBuilder.append("$successMsg\n")
                    
                    // 日志记录响应头
                    Log.d(TAG, "响应头信息:")
                    for (i in 0 until connection.headerFields.size) {
                        val headerName = connection.headerFieldKey(i)
                        val headerValue = connection.getHeaderField(i)
                        if (headerName != null) {
                            Log.d(TAG, "  $headerName: $headerValue")
                        }
                    }
                    
                    return@withContext Result.success(resultBuilder.toString())
                } catch (e: Exception) {
                    val error = "❌ HTTPS连接失败: ${e.message}"
                    Log.e(TAG, error, e)
                    resultBuilder.append("$error\n")
                } finally {
                    connection.disconnect()
                }
            } catch (e: Exception) {
                val error = "❌ 创建HTTPS连接失败: ${e.message}"
                Log.e(TAG, error, e)
                resultBuilder.append("$error\n")
            }
            
            return@withContext Result.failure(IOException(resultBuilder.toString()))
        } catch (e: Exception) {
            val unexpectedError = "❌ 连接测试过程中出现未预期的异常: ${e.message}"
            Log.e(TAG, unexpectedError, e)
            resultBuilder.append("$unexpectedError\n")
            return@withContext Result.failure(IOException(resultBuilder.toString()))
        }
    }
    
    /**
     * 测试API接口连接
     */
    suspend fun testApiConnection(apiPath: String = "register"): Result<String> = withContext(Dispatchers.IO) {
        try {
            val apiUrl = "https://$SERVER_DOMAIN/api/$apiPath"
            Log.d(TAG, "🔍 测试API连接: $apiUrl")
            
            val url = URL(apiUrl)
            val connection = url.openConnection() as HttpsURLConnection
            
            connection.connectTimeout = 10000
            connection.readTimeout = 10000
            connection.requestMethod = "GET"
            connection.setRequestProperty("Connection", "close")
            connection.setRequestProperty("User-Agent", "AIHealthButler-Android-Debug")
            
            try {
                val startTime = System.currentTimeMillis()
                connection.connect()
                val responseCode = connection.responseCode
                val responseTime = System.currentTimeMillis() - startTime
                
                val result = "✅ API连接测试: $apiUrl -> 响应码: $responseCode, 耗时: ${responseTime}ms"
                Log.d(TAG, result)
                
                // 记录响应头信息
                val headers = StringBuilder()
                for (i in 0 until connection.headerFields.size) {
                    val headerName = connection.headerFieldKey(i)
                    val headerValue = connection.getHeaderField(i)
                    if (headerName != null) {
                        headers.append("  $headerName: $headerValue\n")
                    }
                }
                
                Log.d(TAG, "📋 响应头:\n$headers")
                
                Result.success(result)
            } finally {
                connection.disconnect()
            }
        } catch (e: Exception) {
            val error = "❌ API连接测试失败: ${e.message}"
            Log.e(TAG, error, e)
            Result.failure(IOException(error))
        }
    }

    private fun HttpsURLConnection.headerFieldKey(i: Int) {}

    /**
     * 执行完整的网络诊断
     */
    suspend fun runNetworkDiagnostics(context: Context): String = withContext(Dispatchers.IO) {
        val report = StringBuilder()
        report.append("🔍 网络诊断报告 🔍\n\n")
        
        // 检查基本网络连接
        val networkAvailable = isNetworkAvailable(context)
        val networkType = getNetworkType(context)
        report.append("1. 网络状态: ${if (networkAvailable) "✅ 可用" else "❌ 不可用"}\n")
        report.append("2. 网络类型: $networkType\n\n")
        
        // DNS解析测试
        try {
            checkDnsResolution().fold(
                onSuccess = { report.append("3. DNS测试: $it\n\n") },
                onFailure = { report.append("3. DNS测试: ❌ 失败 - ${it.message}\n\n") }
            )
        } catch (e: Exception) {
            report.append("3. DNS测试: ❌ 异常 - ${e.message}\n\n")
        }
        
        // 服务器连接测试
        try {
            checkServerConnection().fold(
                onSuccess = { report.append("4. 服务器连接: $it\n\n") },
                onFailure = { report.append("4. 服务器连接: ❌ 失败 - ${it.message}\n\n") }
            )
        } catch (e: Exception) {
            report.append("4. 服务器连接: ❌ 异常 - ${e.message}\n\n")
        }
        
        // API测试
        try {
            testApiConnection().fold(
                onSuccess = { report.append("5. API测试: $it\n\n") },
                onFailure = { report.append("5. API测试: ❌ 失败 - ${it.message}\n\n") }
            )
        } catch (e: Exception) {
            report.append("5. API测试: ❌ 异常 - ${e.message}\n\n")
        }
        
        report.append("📱 设备信息:\n")
        report.append("Android版本: ${android.os.Build.VERSION.RELEASE} (API ${android.os.Build.VERSION.SDK_INT})\n")
        report.append("设备型号: ${android.os.Build.MANUFACTURER} ${android.os.Build.MODEL}\n")
        
        val reportString = report.toString()
        Log.d(TAG, reportString)
        reportString
    }
} 