package com.example.aihealthbutler

import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.foundation.background
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.graphics.Color
import com.example.aihealthbutler.ui.theme.AIHealthButlerTheme
import com.example.aihealthbutler.ui.components.CustomDeleteDialog
import androidx.compose.ui.platform.LocalContext
import androidx.compose.material3.CenterAlignedTopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.foundation.clickable
import android.util.Log
import android.widget.Toast
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.lifecycle.viewmodel.compose.viewModel
import com.example.aihealthbutler.api.Repository
import com.example.aihealthbutler.api.SessionManager
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.launch
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.foundation.gestures.detectHorizontalDragGestures
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.input.pointer.consumeAllChanges
import androidx.compose.ui.unit.IntOffset
import kotlin.math.roundToInt
import com.example.aihealthbutler.api.MedicationPlanDetail
import com.example.aihealthbutler.api.MedicationPlanRequest
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow

class MedicationPlanListViewModel(
    private val repository: Repository,
    private val sessionManager: SessionManager
) : ViewModel() {
    private val TAG = "MedicationPlanListVM"

    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading

    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error

    private val _medicationPlans = MutableStateFlow<List<MedicationPlanDetail>>(emptyList())
    val medicationPlans: StateFlow<List<MedicationPlanDetail>> = _medicationPlans

    init {
        loadMedicationPlans()
    }

    fun loadMedicationPlans() {
        viewModelScope.launch {
            _isLoading.value = true
            _error.value = null
            try {
                val token = sessionManager.token.firstOrNull() ?: throw Exception("未登录")
                val defaultId = sessionManager.defaultId.firstOrNull() ?: throw Exception("未设置默认成员")
                val result = repository.getMedicationPlans(token, defaultId)
                result.fold(
                    onSuccess = { response ->
                        if (response.code == 200 && response.data != null) {
                            _medicationPlans.value = response.data
                        } else {
                            _error.value = response.message ?: "获取用药计划失败"
                        }
                    },
                    onFailure = { e ->
                        _error.value = e.message ?: "获取用药计划失败"
                    }
                )
            } catch (e: Exception) {
                _error.value = "加载失败: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun retryLoadingMedicationPlans() = loadMedicationPlans()

    fun deleteMedicationPlan(mplanId: Int) {
        viewModelScope.launch {
            _isLoading.value = true
            _error.value = null
            try {
                val token = sessionManager.token.firstOrNull() ?: throw Exception("未登录")
                val result = repository.deleteMedicationPlan(token, mplanId)
                result.fold(
                    onSuccess = { response ->
                        if (response.code == 200 || response.code == 204) {
                            loadMedicationPlans()
                        } else {
                            _error.value = response.message ?: "删除用药计划失败"
                        }
                    },
                    onFailure = { e ->
                        _error.value = e.message ?: "删除失败"
                    }
                )
            } catch (e: Exception) {
                _error.value = "删除失败: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }
}

class MedicationPlanActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            AIHealthButlerTheme {
                val sessionManager = remember { SessionManager(applicationContext) }
                val repository = remember { Repository() }
                val viewModel = remember { MedicationPlanListViewModel(repository, sessionManager) }

                MedicationPlanScreen(
                    viewModel = viewModel,
                    onBack = {
                        startActivity(Intent(this, MainActivity::class.java))
                        finish()
                    }
                )
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MedicationPlanScreen(
    viewModel: MedicationPlanListViewModel,
    onBack: () -> Unit
) {
    val context = LocalContext.current
    val isLoading by viewModel.isLoading.collectAsState()
    val error by viewModel.error.collectAsState()
    val plans by viewModel.medicationPlans.collectAsState()

    Scaffold(
        modifier = Modifier
            .fillMaxSize()
            .background(Color(0xFFF4F6F8)),
        topBar = {
            CenterAlignedTopAppBar(
                title = {
                    Text(
                        text = "用药计划",
                        fontSize = 20.sp,
                        fontWeight = FontWeight.Bold
                    )
                },
                navigationIcon = {
                    IconButton(onClick = onBack) {
                        Icon(
                            painter = painterResource(R.drawable.left_arrow),
                            contentDescription = "返回",
                            modifier = Modifier.size(24.dp)
                        )
                    }
                },
                colors = TopAppBarDefaults.centerAlignedTopAppBarColors(
                    containerColor = Color(0xFFF5F6F7)
                )
            )
        },
        containerColor = Color(0xFFF4F6F8)
    ) { innerPadding ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(innerPadding)
                .padding(16.dp)
        ) {
            when {
                isLoading -> {
                    // 显示加载中
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        CircularProgressIndicator(
                            color = Color(0xFF70C2BF),
                            modifier = Modifier.size(40.dp)
                        )
                    }
                }
                error != null -> {
                    // 显示错误信息
                    LaunchedEffect(error) {
                        Toast.makeText(context, error ?: "", Toast.LENGTH_SHORT).show()
                    }

                    Column(
                        modifier = Modifier.fillMaxSize(),
                        verticalArrangement = Arrangement.SpaceBetween
                    ) {
                        Box(
                            modifier = Modifier.weight(1f),
                        contentAlignment = Alignment.Center
                    ) {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            Text(
                                text = "获取用药计划失败",
                                fontSize = 18.sp,
                                color = Color.Gray,
                                fontWeight = FontWeight.Bold
                            )

                            Spacer(modifier = Modifier.height(8.dp))

                            Text(
                                    text = error ?: "",
                                fontSize = 14.sp,
                                color = Color.Gray,
                                textAlign = TextAlign.Center,
                                modifier = Modifier.padding(horizontal = 24.dp)
                            )

                            Spacer(modifier = Modifier.height(16.dp))

                            Button(
                                    onClick = { viewModel.retryLoadingMedicationPlans() },
                                    colors = ButtonDefaults.buttonColors(containerColor = Color(0xFF70C2BF)),
                                    contentPadding = PaddingValues(horizontal = 24.dp, vertical = 12.dp)
                            ) {
                                Text("重新加载", fontSize = 16.sp)
                            }
                        }
                    }
                        
                        // 底部 "新增用药计划" 按钮
                        Button(
                            onClick = {
                                context.startActivity(
                                    Intent(context, AddnewmedicationplanActivity::class.java)
                                )
                            },
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(64.dp)
                                .padding(top = 8.dp, bottom = 8.dp),
                            contentPadding = PaddingValues(0.dp),
                            colors = ButtonDefaults.buttonColors(containerColor = Color.Transparent)
                        ) {
                            Box(
                                modifier = Modifier
                                    .fillMaxSize()
                                    .background(
                                        brush = Brush.horizontalGradient(
                                            colors = listOf(Color(0xFF70E1C7), Color(0xFF3AABE2))
                                        ),
                                        shape = RoundedCornerShape(24.dp)
                                    ),
                                contentAlignment = Alignment.Center
                            ) {
                                Row(
                                    verticalAlignment = Alignment.CenterVertically,
                                    horizontalArrangement = Arrangement.Center
                                ) {
                                    Icon(
                                        painter = painterResource(id = R.drawable.ic_add),
                                        contentDescription = "添加",
                                        tint = Color.White,
                                        modifier = Modifier.size(24.dp)
                                    )
                                    Spacer(modifier = Modifier.width(8.dp))
                                    Text(
                                        text = "新增用药计划",
                                        color = Color.White,
                                        fontSize = 16.sp,
                                        fontWeight = FontWeight.Bold
                                    )
                                }
                            }
                        }
                    }
                }
                plans.isEmpty() -> {
                    // 显示空状态
                    Column(
                        modifier = Modifier.fillMaxSize(),
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.SpaceBetween
                    ) {
                        Box(
                            modifier = Modifier.weight(1f),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = "暂无用药计划",
                            fontSize = 16.sp,
                            color = Color.Gray
                        )
                    }
                        
                        // 底部 "新增用药计划" 按钮
                        Button(
                            onClick = {
                                context.startActivity(
                                    Intent(context, AddnewmedicationplanActivity::class.java)
                                )
                            },
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(64.dp)
                                .padding(top = 8.dp, bottom = 8.dp),
                            contentPadding = PaddingValues(0.dp),
                            colors = ButtonDefaults.buttonColors(containerColor = Color.Transparent)
                        ) {
                            Box(
                                modifier = Modifier
                                    .fillMaxSize()
                                    .background(
                                        brush = Brush.horizontalGradient(
                                            colors = listOf(Color(0xFF70E1C7), Color(0xFF3AABE2))
                                        ),
                                        shape = RoundedCornerShape(24.dp)
                                    ),
                                contentAlignment = Alignment.Center
                            ) {
                                Row(
                                    verticalAlignment = Alignment.CenterVertically,
                                    horizontalArrangement = Arrangement.Center
                                ) {
                                    Icon(
                                        painter = painterResource(id = R.drawable.ic_add),
                                        contentDescription = "添加",
                                        tint = Color.White,
                                        modifier = Modifier.size(24.dp)
                                    )
                                    Spacer(modifier = Modifier.width(8.dp))
                                    Text(
                                        text = "新增用药计划",
                                        color = Color.White,
                                        fontSize = 16.sp,
                                        fontWeight = FontWeight.Bold
                                    )
                                }
                            }
                        }
                    }
                }
                else -> {
                    // 显示用药计划列表
                    Column(
                        modifier = Modifier.fillMaxSize()
                    ) {
                        LazyColumn(
                            modifier = Modifier
                                .fillMaxWidth()
                                .weight(1f, fill = true)
                                .padding(top = 8.dp)
                        ) {
                            items(plans) { plan ->
                                SwipeableMedicationPlanCard(
                                    plan = plan,
                                    onDelete = {
                                        viewModel.deleteMedicationPlan(plan.mplanId)
                                    }
                                )
                                
                                Spacer(modifier = Modifier.height(16.dp))
                            }
                        }

                        // 底部 "新增用药计划" 按钮
                        Button(
                            onClick = {
                                context.startActivity(
                                    Intent(context, AddnewmedicationplanActivity::class.java)
                                )
                            },
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(64.dp)
                                .padding(top = 8.dp, bottom = 8.dp),
                            contentPadding = PaddingValues(0.dp),
                            colors = ButtonDefaults.buttonColors(containerColor = Color.Transparent)
                        ) {
                            Box(
                                modifier = Modifier
                                    .fillMaxSize()
                                    .background(
                                        brush = Brush.horizontalGradient(
                                            colors = listOf(Color(0xFF70E1C7), Color(0xFF3AABE2))
                                        ),
                                        shape = RoundedCornerShape(24.dp)
                                    ),
                                contentAlignment = Alignment.Center
                            ) {
                                Row(
                                    verticalAlignment = Alignment.CenterVertically,
                                    horizontalArrangement = Arrangement.Center
                                ) {
                                    Icon(
                                        painter = painterResource(id = R.drawable.ic_add),
                                        contentDescription = "添加",
                                        tint = Color.White,
                                        modifier = Modifier.size(24.dp)
                                    )
                                    Spacer(modifier = Modifier.width(8.dp))
                                Text(
                                        text = "新增用药计划",
                                    color = Color.White,
                                    fontSize = 16.sp,
                                    fontWeight = FontWeight.Bold
                                )
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SwipeableMedicationPlanCard(
    plan: MedicationPlanDetail,
    onDelete: () -> Unit
) {
    val context = LocalContext.current
    var offsetX by remember { mutableStateOf(0f) }
    val swipeThreshold = 200f // 滑动阈值，超过这个距离将触发删除
    
    // 删除确认对话框状态
    var showDeleteDialog by remember { mutableStateOf(false) }

    // 若显示确认对话框
    if (showDeleteDialog) {
        CustomDeleteDialog(
            title = "确认删除",
            message = "确定删除「${plan.pillName}」该用药计划吗？\n删除后数据将无法恢复",
            onDismiss = { showDeleteDialog = false },
            onConfirm = {
                onDelete()
                showDeleteDialog = false
            }
        )
    }

    Box(
        modifier = Modifier.fillMaxWidth()
    ) {
        // 卡片容器
    Card(
            modifier = Modifier
                .fillMaxWidth()
                .offset { IntOffset(offsetX.roundToInt(), 0) }
                .pointerInput(Unit) {
                    detectHorizontalDragGestures(
                        onDragEnd = {
                            // 检查是否超过滑动阈值
                            if (offsetX < -swipeThreshold) {
                                // 显示删除确认对话框
                                showDeleteDialog = true
                            }
                            // 无论如何，重置偏移量
                            offsetX = 0f
                        },
                        onHorizontalDrag = { change, dragAmount ->
                            change.consumeAllChanges()
                            // 只允许向左滑动（负值）
                            val newValue = (offsetX + dragAmount).coerceAtMost(0f)
                            offsetX = newValue
                        }
                    )
                },
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White),
            border = BorderStroke(1.dp, Color(0xFF70C2BF)),
            elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(modifier = Modifier.padding(16.dp)) {
                // 第一行：药品名 + 编辑/删除图标
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = plan.pillName,
                    fontSize = 18.sp,
                    color = Color.Black,
                    fontWeight = FontWeight.Bold
                )

                Row {
                        // 编辑按钮点击处理
                    IconButton(
                        onClick = {
                            // 从时间字符串中提取时间部分
                            fun extractTime(timeString: String?): String {
                                return timeString?.let {
                                    if (it.contains("T")) {
                                        it.split("T")[1].substring(0, 5) // 提取 HH:mm 部分
                                    } else {
                                        it
                                    }
                                } ?: ""
                            }

                            val intent = Intent(context, AddnewmedicationplanActivity::class.java).apply {
                                putExtra("isEdit", true)
                                putExtra("planId", plan.mplanId)
                                putExtra("pillName", plan.pillName)
                                putExtra("startDate", plan.startDate)
                                putExtra("duration", plan.duration)
                                putExtra("method", plan.method)
                                putExtra("frequency", plan.frequency)
                                putExtra("frequencyDetail", plan.frequencyDetail)
                                putExtra("firstTime", extractTime(plan.firstTime))
                                putExtra("secondTime", extractTime(plan.secondTime))
                                putExtra("thirdTime", extractTime(plan.thirdTime))
                                putExtra("dosage", plan.dosage)
                                putExtra("guide", plan.guide ?: "")
                                putExtra("note", plan.note ?: "")
                            }
                            context.startActivity(intent)
                        },
                        modifier = Modifier.size(24.dp)
                    ) {
                        Icon(
                            painter = painterResource(R.drawable.modify_information),
                            contentDescription = "编辑药品",
                            tint = Color(0xFF70C2BF),
                            modifier = Modifier.size(24.dp)
                        )
                    }

                        // 删除按钮
                    IconButton(
                        onClick = { showDeleteDialog = true },
                        modifier = Modifier.size(24.dp)
                    ) {
                        Icon(
                            painter = painterResource(R.drawable.ic_delete),
                            contentDescription = "删除药品",
                            tint = Color(0xFF70C2BF),
                            modifier = Modifier.size(24.dp)
                        )
                    }
                }
            }

                // 分割线
            Divider(color = Color(0xFF70C2BF), thickness = 1.dp)

            Spacer(modifier = Modifier.height(12.dp))

                // 具体字段
            if (plan.startDate.isNotBlank()) {
                    Text(text = "用药开始时间：${plan.startDate}", fontSize = 14.sp, color = Color.Black)
                Spacer(modifier = Modifier.height(4.dp))
            }

            Text(text = "用药频次：${plan.frequency}", fontSize = 14.sp, color = Color.Black)
            Spacer(modifier = Modifier.height(4.dp))

                // 支持API返回的时间字段
            if (plan.firstTime.isNotBlank()) {
                    Text(text = "用药时间：${plan.firstTime}", fontSize = 14.sp, color = Color.Black)
                Spacer(modifier = Modifier.height(4.dp))
            }

                // 处理第二、第三用药时间
            if (plan.secondTime.isNotBlank()) {
                    Text(text = "第二次用药时间：${plan.secondTime}", fontSize = 14.sp, color = Color.Black)
                Spacer(modifier = Modifier.height(4.dp))
            }

            if (plan.thirdTime?.isNotBlank() == true) {
                    Text(text = "第三次用药时间：${plan.thirdTime}", fontSize = 14.sp, color = Color.Black)
                Spacer(modifier = Modifier.height(4.dp))
            }

            Text(text = "每次剂量：${plan.dosage}", fontSize = 14.sp, color = Color.Black)
            Spacer(modifier = Modifier.height(4.dp))

            if (!plan.guide.isNullOrBlank()) {
                Text(text = "用药指导：${plan.guide}", fontSize = 14.sp, color = Color.Black)
                Spacer(modifier = Modifier.height(4.dp))
            }

            if (!plan.note.isNullOrBlank()) {
                Text(text = "备注：${plan.note}", fontSize = 14.sp, color = Color.Black)
                }
            }
        }
    }
}

































