package com.example.aihealthbutler.utils

import android.content.Context
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.os.Build
import android.util.Log
import java.io.File
import java.net.InetAddress
import java.net.InetSocketAddress
import java.net.Socket
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.net.URL
import javax.net.ssl.HttpsURLConnection

/**
 * 错误报告工具，用于收集和记录网络错误信息
 * 特别针对Android 15的网络问题进行跟踪和记录
 */
object ErrorReportUtil {
    private const val TAG = "ErrorReportUtil"
    private const val LOG_FILE_PREFIX = "network_error_log_"
    private const val MAX_LOG_FILES = 5
    
    /**
     * 记录错误信息到日志
     */
    fun logError(errorType: String, message: String, exception: Throwable? = null) {
        val timestamp = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(Date())
        val errorMsg = "[$timestamp] $errorType: $message"
        
        Log.e(TAG, errorMsg, exception)
        
        // 如果是Android 15特定的网络问题，添加更多详细信息
        if (Build.VERSION.SDK_INT >= 35) {
            Log.e(TAG, "Android 15特定错误信息: 设备=${Build.MANUFACTURER} ${Build.MODEL}, Android版本=${Build.VERSION.RELEASE}")
        }
    }
    
    /**
     * 记录错误到文件
     */
    fun logErrorToFile(context: Context, errorType: String, message: String, exception: Throwable? = null) {
        try {
            // 创建日志目录
            val logDir = File(context.filesDir, "error_logs")
            if (!logDir.exists()) {
                logDir.mkdirs()
            }
            
            // 创建日期格式的日志文件名
            val dateFormat = SimpleDateFormat("yyyyMMdd", Locale.getDefault())
            val fileName = "${LOG_FILE_PREFIX}${dateFormat.format(Date())}.txt"
            val logFile = File(logDir, fileName)
            
            // 格式化日志内容
            val timestamp = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(Date())
            val sb = StringBuilder()
            sb.append("\n[$timestamp] ERROR: $errorType\n")
            sb.append("Message: $message\n")
            
            // 设备信息
            sb.append("\nDevice Info:\n")
            sb.append("  Manufacturer: ${Build.MANUFACTURER}\n")
            sb.append("  Model: ${Build.MODEL}\n")
            sb.append("  Android: ${Build.VERSION.RELEASE} (SDK ${Build.VERSION.SDK_INT})\n")
            
            // 记录异常堆栈
            if (exception != null) {
                sb.append("\nException: ${exception.javaClass.name}\n")
                sb.append("  ${exception.message}\n")
                sb.append("\nStack Trace:\n")
                
                val stackTraceElements = exception.stackTrace
                for (element in stackTraceElements) {
                    sb.append("  at $element\n")
                }
                
                // 记录根本原因
                var cause = exception.cause
                if (cause != null) {
                    sb.append("\nCaused by: ${cause.javaClass.name}\n")
                    sb.append("  ${cause.message}\n")
                    
                    for (element in cause.stackTrace) {
                        sb.append("  at $element\n")
                    }
                }
            }
            
            // 添加分隔线
            sb.append("\n---------------------------------------------------\n")
            
            // 写入日志文件
            logFile.appendText(sb.toString())
            
            // 删除过多的旧日志文件
            cleanupOldLogFiles(logDir)
            
            Log.d(TAG, "错误日志已写入文件: ${logFile.path}")
        } catch (e: Exception) {
            Log.e(TAG, "写入错误日志文件时发生异常", e)
        }
    }
    
    /**
     * 清理旧的日志文件
     */
    private fun cleanupOldLogFiles(logDir: File) {
        try {
            val logFiles = logDir.listFiles { _, name ->
                name.startsWith(LOG_FILE_PREFIX) && name.endsWith(".txt")
            }
            
            if (logFiles != null && logFiles.size > MAX_LOG_FILES) {
                // 按修改时间排序
                val sortedFiles = logFiles.sortedBy { it.lastModified() }
                
                // 删除最旧的文件
                for (i in 0 until (sortedFiles.size - MAX_LOG_FILES)) {
                    val oldFile = sortedFiles[i]
                    if (oldFile.delete()) {
                        Log.d(TAG, "已删除旧日志文件: ${oldFile.name}")
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "清理旧日志文件时发生异常", e)
        }
    }
    
    /**
     * 收集网络诊断报告并写入文件
     */
    suspend fun collectNetworkDiagnosticReport(context: Context): String {
        try {
            val report = NetworkDebugUtil.runNetworkDiagnostics(context)
            
            // 将报告保存到文件
            val logDir = File(context.filesDir, "network_reports")
            if (!logDir.exists()) {
                logDir.mkdirs()
            }
            
            val dateFormat = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault())
            val fileName = "network_report_${dateFormat.format(Date())}.txt"
            val reportFile = File(logDir, fileName)
            
            reportFile.writeText(report)
            Log.d(TAG, "网络诊断报告已保存: ${reportFile.path}")
            
            return report
        } catch (e: Exception) {
            Log.e(TAG, "收集网络诊断报告时发生异常", e)
            return "生成诊断报告失败: ${e.message}"
        }
    }
    
    /**
     * 获取所有保存的错误报告
     */
    fun getSavedErrorReports(context: Context): List<File> {
        val logDir = File(context.filesDir, "error_logs")
        if (!logDir.exists()) {
            return emptyList()
        }
        
        return logDir.listFiles { _, name ->
            name.startsWith(LOG_FILE_PREFIX) && name.endsWith(".txt")
        }?.sortedByDescending { it.lastModified() } ?: emptyList()
    }
    
    /**
     * 收集网络问题的可能原因
     */
    fun getPossibleNetworkIssues(): String {
        val sb = StringBuilder()
        
        sb.append("可能的网络问题原因:\n\n")
        
        if (Build.VERSION.SDK_INT >= 35) {
            sb.append("Android 15特定问题:\n")
            sb.append("• Android 15引入了新的网络安全策略和DNS解析机制\n")
            sb.append("• 私有DNS设置可能与应用网络访问冲突\n")
            sb.append("• 应用可能需要特殊权限才能访问网络资源\n")
            sb.append("• 部分设备制造商可能有额外的网络限制\n\n")
        }
        
        sb.append("一般网络问题:\n")
        sb.append("• 网络连接不稳定或信号弱\n")
        sb.append("• DNS服务器可能无响应\n")
        sb.append("• 服务器临时不可用\n")
        sb.append("• 移动网络运营商可能限制了某些连接\n")
        sb.append("• WiFi网络可能有防火墙限制\n\n")
        
        sb.append("解决方案:\n")
        sb.append("1. 切换网络连接(如从WiFi切换到移动数据)\n")
        sb.append("2. 重启设备\n")
        sb.append("3. 检查是否有VPN干扰\n")
        sb.append("4. 在系统设置中重置网络设置\n")
        sb.append("5. 检查时间和日期设置是否正确\n")
        
        return sb.toString()
    }
    
    /**
     * 处理Socket连接错误并记录到日志
     */
    fun handleSocketError(context: Context, host: String, port: Int, exception: Throwable) {
        val errorType = "Socket连接错误"
        val isAndroid15 = Build.VERSION.SDK_INT >= 35
        
        // 分析错误类型
        val errorDetails = when (exception) {
            is java.net.ConnectException -> "连接被拒绝，服务器可能不可用或端口未开放"
            is java.net.SocketTimeoutException -> "连接超时，服务器响应过慢或网络不稳定"
            is java.net.NoRouteToHostException -> "无法路由到主机，网络配置可能有问题"
            is java.net.PortUnreachableException -> "端口无法到达，可能被防火墙或安全策略阻止"
            is java.net.UnknownHostException -> "无法解析主机名"
            else -> "未知Socket错误: ${exception.message}"
        }
        
        // 构建详细的错误消息
        val errorMessage = StringBuilder()
            .append("Socket连接到 $host:$port 失败\n")
            .append("错误类型: ${exception.javaClass.simpleName}\n")
            .append("错误详情: $errorDetails\n")
            
        if (isAndroid15) {
            errorMessage.append("\nAndroid 15特定信息:\n")
                .append("- Android 15引入了更严格的网络安全策略\n")
                .append("- 确认应用有正确的网络权限\n")
                .append("- 检查是否使用了私有DNS或VPN\n")
                .append("- 确认所有连接使用的是HTTPS端口(443)而非HTTP端口(80)\n")
        }
        
        // 记录到日志
        logError(errorType, errorMessage.toString(), exception)
        
        // 同时保存到文件
        logErrorToFile(context, errorType, errorMessage.toString(), exception)
        
        // 打印日志供开发者查看
        Log.e(TAG, "Socket连接失败诊断报告:\n$errorMessage")
    }
    
    /**
     * 诊断并尝试解决Socket连接问题
     */
    suspend fun diagnoseAndFixSocketIssue(context: Context, host: String, port: Int): String = withContext(Dispatchers.IO) {
        val report = StringBuilder("HTTPS连接诊断报告:\n\n")
        
        // 确认使用HTTPS端口
        if (port != 443) {
            report.append("⚠️ 警告: 应该使用HTTPS标准端口(443)而非当前端口($port)\n")
            report.append("已自动调整为使用端口443进行诊断\n\n")
        }
        
        // 尝试DNS解析
        report.append("1. DNS解析测试\n")
        try {
            val start = System.currentTimeMillis()
            val addresses = InetAddress.getAllByName(host)
            val time = System.currentTimeMillis() - start
            
            if (addresses.isNotEmpty()) {
                val ips = addresses.joinToString(", ") { it.hostAddress }
                report.append("✅ DNS解析成功: $host -> [$ips], 耗时: ${time}ms\n")
            } else {
                report.append("❌ DNS解析未返回IP地址\n")
            }
        } catch (e: Exception) {
            report.append("❌ DNS解析失败: ${e.message}\n")
        }
        
        // 尝试HTTPS连接
        report.append("\n2. HTTPS连接测试\n")
        try {
            val httpsUrl = URL("https://$host/aie/api/")
            val connection = httpsUrl.openConnection() as HttpsURLConnection
            
            connection.connectTimeout = 10000
            connection.readTimeout = 10000
            connection.requestMethod = "GET"
            connection.setRequestProperty("User-Agent", "AIHealthButler-Android-Diagnostic")
            
            val start = System.currentTimeMillis()
            report.append("尝试连接到 https://$host/aie/api/ ...\n")
            
            try {
                connection.connect()
                val responseCode = connection.responseCode
                val connectTime = System.currentTimeMillis() - start
                
                report.append("✅ HTTPS连接成功，状态码：$responseCode，耗时：${connectTime}ms\n")
                
                // 记录响应头信息
                report.append("响应头信息:\n")
                connection.headerFields.forEach { (key, value) ->
                    if (key != null) {
                        report.append("  $key: ${value.joinToString(", ")}\n")
                    } else {
                        // Print the status line (e.g., HTTP/1.1 200 OK) which has a null key
                        report.append("  ${value.joinToString(", ")}\n")
                    }
                }
            } catch (e: Exception) {
                report.append("❌ HTTPS连接失败: ${e.message}\n")
                val errorType = when(e) {
                    is java.net.UnknownHostException -> "DNS解析错误"
                    is java.net.ConnectException -> "连接被拒绝"
                    is java.net.SocketTimeoutException -> "连接超时"
                    is javax.net.ssl.SSLHandshakeException -> "SSL握手失败"
                    is javax.net.ssl.SSLException -> "SSL错误"
                    else -> "未知错误类型: ${e.javaClass.simpleName}"
                }
                report.append("错误类型: $errorType\n")
            } finally {
                connection.disconnect()
            }
        } catch (e: Exception) {
            report.append("❌ 创建HTTPS连接失败: ${e.message}\n")
        }
        
        // 检查网络权限
        report.append("\n3. 网络权限检查\n")
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        val networkCapabilities = connectivityManager.getNetworkCapabilities(connectivityManager.activeNetwork)
        
        if (networkCapabilities == null) {
            report.append("❌ 未检测到活动网络\n")
        } else {
            val hasInternet = networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
            val hasValidated = networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED)
            val hasTransport = networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) || 
                               networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR)
            
            report.append("网络状态: 互联网=$hasInternet, 已验证=$hasValidated, 传输=$hasTransport\n")
            
            if (!hasInternet || !hasValidated) {
                report.append("⚠️ 网络连接存在问题，可能无法访问互联网\n")
            }
        }
        
        // Android 15特殊说明
        if (Build.VERSION.SDK_INT >= 35) {
            report.append("\n4. Android 15特殊说明\n")
            report.append("Android 15增强了网络安全策略，可能需要以下检查:\n")
            report.append("- 确认AndroidManifest.xml中包含所有必要的网络权限\n")
            report.append("- 检查network_security_config.xml是否正确配置域名\n")
            report.append("- 确保应用使用最新的网络安全最佳实践\n")
        }
        
        // 解决建议
        report.append("\n5. 解决建议\n")
        report.append("- 只使用HTTPS连接(端口443)\n")
        report.append("- 确保网络连接稳定\n")
        report.append("- 尝试使用移动数据而非WiFi连接\n")
        report.append("- 重启设备后再次尝试\n")
        report.append("- 检查时间和日期设置是否正确\n")
        
        // 保存报告
        try {
            val logDir = File(context.filesDir, "https_diagnostics")
            if (!logDir.exists()) {
                logDir.mkdirs()
            }
            
            val dateFormat = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault())
            val fileName = "https_diagnostic_${dateFormat.format(Date())}.txt"
            val reportFile = File(logDir, fileName)
            
            reportFile.writeText(report.toString())
            Log.d(TAG, "HTTPS诊断报告已保存: ${reportFile.path}")
        } catch (e: Exception) {
            Log.e(TAG, "保存HTTPS诊断报告时出错", e)
        }        
        return@withContext report.toString()
    }
} 