package com.example.aihealthbutler.ui

import android.Manifest
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Bundle
import android.util.Log
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.compose.setContent
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.core.content.ContextCompat
import androidx.lifecycle.lifecycleScope
import com.example.aihealthbutler.utils.AudioToTextHelper
import kotlinx.coroutines.launch
import java.io.File

/**
 * 语音转文字功能演示Activity
 */
class AudioToTextActivity : ComponentActivity() {
    
    companion object {
        private const val TAG = "AudioToTextActivity"
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        setContent {
            AudioToTextScreen()
        }
    }
    
    @OptIn(ExperimentalMaterial3Api::class)
    @Composable
    private fun AudioToTextScreen() {
        val context = LocalContext.current
        var isLoading by remember { mutableStateOf(false) }
        var resultText by remember { mutableStateOf("") }
        var selectedAudioFile by remember { mutableStateOf<File?>(null) }
        var audioInfo by remember { mutableStateOf("") }
        
        // 文件选择器
        val audioPickerLauncher = rememberLauncherForActivityResult(
            contract = ActivityResultContracts.GetContent()
        ) { uri: Uri? ->
            uri?.let { audioUri ->
                lifecycleScope.launch {
                    try {
                        isLoading = true
                        
                        // 复制音频文件到临时目录
                        val tempFile = AudioToTextHelper.copyAudioFromUri(
                            context = context,
                            uri = audioUri
                        )
                        
                        selectedAudioFile = tempFile
                        
                        // 获取音频文件信息
                        val fileInfo = AudioToTextHelper.getAudioFileInfo(tempFile)
                        audioInfo = buildString {
                            appendLine("音频文件信息:")
                            appendLine("文件名: ${fileInfo.fileName}")
                            appendLine("大小: ${fileInfo.getFormattedSize()}")
                            appendLine("时长: ${fileInfo.getFormattedDuration()}")
                            appendLine("格式: ${fileInfo.format}")
                        }
                        
                    } catch (e: Exception) {
                        Log.e(TAG, "处理音频文件失败", e)
                        Toast.makeText(context, "处理音频文件失败: ${e.message}", Toast.LENGTH_LONG).show()
                    } finally {
                        isLoading = false
                    }
                }
            }
        }
        
        // 权限请求
        val permissionLauncher = rememberLauncherForActivityResult(
            contract = ActivityResultContracts.RequestPermission()
        ) { isGranted ->
            if (isGranted) {
                audioPickerLauncher.launch("audio/*")
            } else {
                Toast.makeText(context, "需要存储权限来选择音频文件", Toast.LENGTH_SHORT).show()
            }
        }
        
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
                .verticalScroll(rememberScrollState()),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = "语音转文字",
                style = MaterialTheme.typography.headlineMedium
            )
            
            Spacer(modifier = Modifier.height(24.dp))
            
            // 选择音频文件按钮
            Button(
                onClick = {
                    when (ContextCompat.checkSelfPermission(context, Manifest.permission.READ_EXTERNAL_STORAGE)) {
                        PackageManager.PERMISSION_GRANTED -> {
                            audioPickerLauncher.launch("audio/*")
                        }
                        else -> {
                            permissionLauncher.launch(Manifest.permission.READ_EXTERNAL_STORAGE)
                        }
                    }
                },
                enabled = !isLoading
            ) {
                Text("选择音频文件")
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 音频文件信息
            if (audioInfo.isNotEmpty()) {
                Card(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text(
                        text = audioInfo,
                        modifier = Modifier.padding(16.dp),
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
                
                Spacer(modifier = Modifier.height(16.dp))
            }
            
            // 转换按钮
            selectedAudioFile?.let { audioFile ->
                Button(
                    onClick = {
                        lifecycleScope.launch {
                            try {
                                isLoading = true
                                resultText = "正在转换..."
                                
                                // 执行语音转文字
                                val result = AudioToTextHelper.convertAudioToText(
                                    audioFile = audioFile,
                                    user = "android-user-${System.currentTimeMillis()}"
                                )
                                
                                if (result.success) {
                                    resultText = result.text
                                    Toast.makeText(context, "转换成功！", Toast.LENGTH_SHORT).show()
                                } else {
                                    resultText = "转换失败: ${result.error}"
                                    Toast.makeText(context, "转换失败", Toast.LENGTH_SHORT).show()
                                }
                                
                            } catch (e: Exception) {
                                Log.e(TAG, "语音转文字失败", e)
                                resultText = "转换异常: ${e.message}"
                                Toast.makeText(context, "转换异常", Toast.LENGTH_LONG).show()
                            } finally {
                                isLoading = false
                            }
                        }
                    },
                    enabled = !isLoading
                ) {
                    if (isLoading) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(16.dp),
                            strokeWidth = 2.dp
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                    }
                    Text("开始转换")
                }
            }
            
            Spacer(modifier = Modifier.height(24.dp))
            
            // 结果显示
            if (resultText.isNotEmpty()) {
                Card(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Text(
                            text = "转换结果:",
                            style = MaterialTheme.typography.titleMedium
                        )
                        
                        Spacer(modifier = Modifier.height(8.dp))
                        
                        Text(
                            text = resultText,
                            style = MaterialTheme.typography.bodyMedium
                        )
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(24.dp))
            
            // 支持格式说明
            Card(
                modifier = Modifier.fillMaxWidth()
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "支持的音频格式:",
                        style = MaterialTheme.typography.titleMedium
                    )
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    Text(
                        text = AudioToTextHelper.SUPPORTED_FORMATS.joinToString(", "),
                        style = MaterialTheme.typography.bodyMedium
                    )
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    Text(
                        text = "文件大小限制: 15MB",
                        style = MaterialTheme.typography.bodySmall
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 清理临时文件按钮
            OutlinedButton(
                onClick = {
                    AudioToTextHelper.cleanupTempFiles(context)
                    selectedAudioFile = null
                    audioInfo = ""
                    resultText = ""
                    Toast.makeText(context, "临时文件已清理", Toast.LENGTH_SHORT).show()
                }
            ) {
                Text("清理临时文件")
            }
        }
    }
    
    override fun onDestroy() {
        super.onDestroy()
        // 清理临时文件
        AudioToTextHelper.cleanupTempFiles(this)
    }
}
