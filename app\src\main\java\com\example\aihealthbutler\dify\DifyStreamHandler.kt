package com.example.aihealthbutler.dify

import android.util.Log
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.catch

/**
 * Dify 流式响应处理器
 */
class DifyStreamHandler {
    
    companion object {
        private const val TAG = "DifyStreamHandler"
    }
    
    // 当前消息内容
    private val _currentMessage = MutableStateFlow("")
    val currentMessage: StateFlow<String> = _currentMessage
    
    // 当前会话ID
    private val _conversationId = MutableStateFlow<String?>(null)
    val conversationId: StateFlow<String?> = _conversationId
    
    // 当前消息ID
    private val _messageId = MutableStateFlow<String?>(null)
    val messageId: StateFlow<String?> = _messageId
    
    // 当前任务ID
    private val _taskId = MutableStateFlow<String?>(null)
    val taskId: StateFlow<String?> = _taskId
    
    // 流式状态
    private val _streamState = MutableStateFlow(StreamState.IDLE)
    val streamState: StateFlow<StreamState> = _streamState
    
    // 错误信息
    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error
    
    // 元数据信息
    private val _metadata = MutableStateFlow<Metadata?>(null)
    val metadata: StateFlow<Metadata?> = _metadata
    
    // 文件列表
    private val _files = MutableStateFlow<List<StreamFileInfo>>(emptyList())
    val files: StateFlow<List<StreamFileInfo>> = _files
    
    // TTS音频数据
    private val _audioData = MutableStateFlow<String?>(null)
    val audioData: StateFlow<String?> = _audioData
    
    /**
     * 处理流式响应
     */
    fun handleStreamResponse(stream: Flow<StreamChatResponse>): Flow<StreamChatResponse> {
        reset()
        _streamState.value = StreamState.STREAMING
        
        return stream.catch { exception ->
            Log.e(TAG, "Stream error", exception)
            _error.value = exception.message ?: "Unknown error"
            _streamState.value = StreamState.ERROR
        }
    }
    
    /**
     * 处理单个流式事件
     */
    fun handleStreamEvent(event: StreamChatResponse) {
        when (event.event) {
            StreamEventType.MESSAGE -> {
                handleMessageEvent(event)
            }
            
            StreamEventType.MESSAGE_FILE -> {
                handleMessageFileEvent(event)
            }
            
            StreamEventType.MESSAGE_END -> {
                handleMessageEndEvent(event)
            }
            
            StreamEventType.TTS_MESSAGE -> {
                handleTtsMessageEvent(event)
            }
            
            StreamEventType.TTS_MESSAGE_END -> {
                handleTtsMessageEndEvent(event)
            }
            
            StreamEventType.MESSAGE_REPLACE -> {
                handleMessageReplaceEvent(event)
            }
            
            StreamEventType.WORKFLOW_STARTED -> {
                handleWorkflowStartedEvent(event)
            }
            
            StreamEventType.NODE_STARTED -> {
                handleNodeStartedEvent(event)
            }
            
            StreamEventType.NODE_FINISHED -> {
                handleNodeFinishedEvent(event)
            }
            
            StreamEventType.WORKFLOW_FINISHED -> {
                handleWorkflowFinishedEvent(event)
            }
            
            StreamEventType.ERROR -> {
                handleErrorEvent(event)
            }
            
            StreamEventType.PING -> {
                handlePingEvent(event)
            }
            
            else -> {
                Log.w(TAG, "Unknown event type: ${event.event}")
            }
        }
    }
    
    private fun handleMessageEvent(event: StreamChatResponse) {
        event.taskId?.let { _taskId.value = it }
        event.messageId?.let { _messageId.value = it }
        event.conversationId?.let { _conversationId.value = it }
        
        event.answer?.let { answer ->
            _currentMessage.value += answer
        }
    }
    
    private fun handleMessageFileEvent(event: StreamChatResponse) {
        event.id?.let { fileId ->
            val fileInfo = StreamFileInfo(
                id = fileId,
                type = event.type ?: "",
                belongsTo = event.belongsTo ?: "",
                url = event.url ?: ""
            )
            
            val currentFiles = _files.value.toMutableList()
            currentFiles.add(fileInfo)
            _files.value = currentFiles
        }
    }
    
    private fun handleMessageEndEvent(event: StreamChatResponse) {
        event.taskId?.let { _taskId.value = it }
        event.messageId?.let { _messageId.value = it }
        event.conversationId?.let { _conversationId.value = it }
        event.metadata?.let { _metadata.value = it }
        
        _streamState.value = StreamState.COMPLETED
    }
    
    private fun handleTtsMessageEvent(event: StreamChatResponse) {
        event.audio?.let { audio ->
            _audioData.value = audio
        }
    }
    
    private fun handleTtsMessageEndEvent(event: StreamChatResponse) {
        _audioData.value = null
    }
    
    private fun handleMessageReplaceEvent(event: StreamChatResponse) {
        event.answer?.let { answer ->
            _currentMessage.value = answer
        }
    }
    
    private fun handleWorkflowStartedEvent(event: StreamChatResponse) {
        Log.d(TAG, "Workflow started: ${event.workflowRunId}")
    }
    
    private fun handleNodeStartedEvent(event: StreamChatResponse) {
        Log.d(TAG, "Node started: ${event.data}")
    }
    
    private fun handleNodeFinishedEvent(event: StreamChatResponse) {
        Log.d(TAG, "Node finished: ${event.data}")
    }
    
    private fun handleWorkflowFinishedEvent(event: StreamChatResponse) {
        Log.d(TAG, "Workflow finished: ${event.data}")
    }
    
    private fun handleErrorEvent(event: StreamChatResponse) {
        val errorMessage = event.message ?: "Unknown error"
        _error.value = errorMessage
        _streamState.value = StreamState.ERROR
        Log.e(TAG, "Stream error: $errorMessage (code: ${event.code})")
    }
    
    private fun handlePingEvent(event: StreamChatResponse) {
        Log.d(TAG, "Ping received")
    }
    
    /**
     * 重置状态
     */
    fun reset() {
        _currentMessage.value = ""
        _conversationId.value = null
        _messageId.value = null
        _taskId.value = null
        _streamState.value = StreamState.IDLE
        _error.value = null
        _metadata.value = null
        _files.value = emptyList()
        _audioData.value = null
    }
    
    /**
     * 获取完整的消息内容
     */
    fun getCompleteMessage(): String {
        return _currentMessage.value
    }
    
    /**
     * 检查是否有错误
     */
    fun hasError(): Boolean {
        return _error.value != null
    }
    
    /**
     * 检查是否完成
     */
    fun isCompleted(): Boolean {
        return _streamState.value == StreamState.COMPLETED
    }
}

/**
 * 流式状态
 */
enum class StreamState {
    IDLE,       // 空闲
    STREAMING,  // 流式传输中
    COMPLETED,  // 完成
    ERROR       // 错误
}

/**
 * 流式文件信息
 */
data class StreamFileInfo(
    val id: String,
    val type: String,
    val belongsTo: String,
    val url: String
)
