package com.example.aihealthbutler.speech

import android.content.Context
import android.speech.SpeechRecognizer
import android.util.Log
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * 语音识别引擎管理器
 * 支持多种语音识别引擎的自动切换和管理
 */
class SpeechEngineManager(private val context: Context) {

    companion object {
        private const val TAG = "SpeechEngineManager"
    }

    // 当前使用的引擎
    private var currentEngine: SpeechRecognitionEngine? = null
    private var currentEngineType: SpeechEngineType = SpeechEngineType.GOOGLE

    // 可用引擎列表
    private val availableEngines = mutableMapOf<SpeechEngineType, SpeechRecognitionEngine>()

    // 引擎状态
    private val _engineStatus = MutableStateFlow<Map<SpeechEngineType, Boolean>>(emptyMap())
    val engineStatus: StateFlow<Map<SpeechEngineType, Boolean>> = _engineStatus

    // 当前引擎信息
    private val _currentEngineInfo = MutableStateFlow("")
    val currentEngineInfo: StateFlow<String> = _currentEngineInfo

    init {
        initializeEngines()
    }

    /**
     * 初始化所有可用的语音识别引擎
     */
    private fun initializeEngines() {
        Log.d(TAG, "Initializing speech engines")

        // 检查Google语音服务
        if (SpeechRecognizer.isRecognitionAvailable(context)) {
            try {
                val googleEngine = GoogleSpeechEngine(context)
                availableEngines[SpeechEngineType.GOOGLE] = googleEngine
                Log.d(TAG, "Google Speech Engine available")
            } catch (e: Exception) {
                Log.w(TAG, "Google Speech Engine not available", e)
            }
        }

        // 初始化讯飞语音引擎
        try {
            val xunfeiEngine = XunfeiSpeechEngine(context)
            availableEngines[SpeechEngineType.XUNFEI] = xunfeiEngine
            Log.d(TAG, "Xunfei Speech Engine available")
        } catch (e: Exception) {
            Log.w(TAG, "Xunfei Speech Engine not available", e)
        }

        // 初始化Vosk离线引擎
        try {
            val voskEngine = VoskSpeechEngine(context)
            availableEngines[SpeechEngineType.VOSK] = voskEngine
            Log.d(TAG, "Vosk Speech Engine available")
        } catch (e: Exception) {
            Log.w(TAG, "Vosk Speech Engine not available", e)
        }

        // 更新引擎状态
        CoroutineScope(Dispatchers.IO).launch {
            updateEngineStatus()
        }
        
        // 选择最佳引擎
        selectBestEngine()
    }

    /**
     * 更新引擎状态
     */
    private suspend fun updateEngineStatus() {
        val status = mutableMapOf<SpeechEngineType, Boolean>()
        
        for ((type, engine) in availableEngines) {
            try {
                val isAvailable = engine.initialize()
                status[type] = isAvailable
                Log.d(TAG, "${type.name} engine status: $isAvailable")
            } catch (e: Exception) {
                status[type] = false
                Log.w(TAG, "${type.name} engine initialization failed", e)
            }
        }
        
        _engineStatus.value = status
    }

    /**
     * 选择最佳可用引擎
     */
    private fun selectBestEngine() {
        val status = _engineStatus.value
        
        // 优先级顺序：Google > 讯飞 > Vosk
        val priorityOrder = listOf(
            SpeechEngineType.GOOGLE,
            SpeechEngineType.XUNFEI,
            SpeechEngineType.VOSK
        )
        
        for (engineType in priorityOrder) {
            if (status[engineType] == true) {
                switchToEngine(engineType)
                return
            }
        }
        
        // 如果没有可用引擎
        Log.w(TAG, "No available speech engines")
        _currentEngineInfo.value = "没有可用的语音识别引擎"
    }

    /**
     * 切换到指定引擎
     */
    fun switchToEngine(engineType: SpeechEngineType): Boolean {
        val engine = availableEngines[engineType]
        if (engine == null || _engineStatus.value[engineType] != true) {
            Log.w(TAG, "Engine $engineType not available")
            return false
        }

        // 停止当前引擎
        currentEngine?.release()
        
        // 切换到新引擎
        currentEngine = engine
        currentEngineType = engineType
        
        _currentEngineInfo.value = "${engine.getEngineName()}: ${engine.getEngineDescription()}"
        
        Log.d(TAG, "Switched to engine: $engineType")
        return true
    }

    /**
     * 获取当前引擎
     */
    fun getCurrentEngine(): SpeechRecognitionEngine? = currentEngine

    /**
     * 获取当前引擎类型
     */
    fun getCurrentEngineType(): SpeechEngineType = currentEngineType

    /**
     * 获取所有可用引擎
     */
    fun getAvailableEngines(): Map<SpeechEngineType, SpeechRecognitionEngine> = availableEngines

    /**
     * 检查指定引擎是否可用
     */
    fun isEngineAvailable(engineType: SpeechEngineType): Boolean {
        return _engineStatus.value[engineType] == true
    }

    /**
     * 获取引擎诊断信息
     */
    fun getDiagnosticInfo(): String {
        val info = StringBuilder()
        info.append("语音识别引擎状态:\n\n")
        
        for (engineType in SpeechEngineType.values()) {
            val engine = availableEngines[engineType]
            val status = _engineStatus.value[engineType]
            
            info.append("${engineType.name}: ")
            when {
                engine == null -> info.append("未集成\n")
                status == true -> info.append("✅ 可用\n")
                status == false -> info.append("❌ 不可用\n")
                else -> info.append("⚠️ 未知状态\n")
            }
            
            if (engine != null) {
                info.append("  - ${engine.getEngineDescription()}\n")
            }
            info.append("\n")
        }
        
        info.append("当前使用: ${currentEngine?.getEngineName() ?: "无"}")
        
        return info.toString()
    }

    /**
     * 重新初始化所有引擎
     */
    suspend fun reinitializeEngines() {
        Log.d(TAG, "Reinitializing all engines")
        
        // 释放当前引擎
        currentEngine?.release()
        currentEngine = null
        
        // 重新初始化
        CoroutineScope(Dispatchers.IO).launch {
            updateEngineStatus()
            selectBestEngine()
        }
    }

    /**
     * 释放所有资源
     */
    fun release() {
        Log.d(TAG, "Releasing all engines")
        
        for (engine in availableEngines.values) {
            try {
                engine.release()
            } catch (e: Exception) {
                Log.w(TAG, "Error releasing engine", e)
            }
        }
        
        availableEngines.clear()
        currentEngine = null
    }
}

/**
 * Google语音引擎包装器
 * 将现有的SpeechToTextManager包装为SpeechRecognitionEngine接口
 */
class GoogleSpeechEngine(
    private val context: Context,
    private val config: SpeechRecognitionConfig = SpeechRecognitionConfig()
) : SpeechRecognitionEngine {

    // 这里可以包装现有的SpeechToTextManager
    // 或者重新实现Google语音识别逻辑
    
    private val _state = MutableStateFlow(SpeechToTextState.IDLE)
    override val state: StateFlow<SpeechToTextState> = _state

    private val _result = MutableStateFlow(SpeechToTextResult())
    override val result: StateFlow<SpeechToTextResult> = _result

    private val _errorMessage = MutableStateFlow("")
    override val errorMessage: StateFlow<String> = _errorMessage

    override suspend fun initialize(): Boolean {
        return SpeechRecognizer.isRecognitionAvailable(context)
    }

    override fun startListening() {
        // 实现Google语音识别逻辑
        _state.value = SpeechToTextState.LISTENING
    }

    override fun stopListening() {
        _state.value = SpeechToTextState.PROCESSING
    }

    override fun cancelListening() {
        _state.value = SpeechToTextState.IDLE
    }

    override fun isAvailable(): Boolean {
        return SpeechRecognizer.isRecognitionAvailable(context)
    }

    override fun isListening(): Boolean {
        return _state.value == SpeechToTextState.LISTENING
    }

    override fun release() {
        _state.value = SpeechToTextState.IDLE
    }

    override fun reset() {
        _result.value = SpeechToTextResult()
        _errorMessage.value = ""
        _state.value = SpeechToTextState.IDLE
    }

    override fun getEngineName(): String = "Google语音识别"

    override fun getEngineDescription(): String = "Google语音识别服务，需要网络连接"
}
