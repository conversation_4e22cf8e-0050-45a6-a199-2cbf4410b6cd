# 语音转文字功能实现方案

## 🎯 功能概述

基于Dify API的`/audio-to-text`端点实现语音转文字功能，支持多种音频格式，提供完整的文件处理和错误处理机制。

## 📋 API规格

### 请求规格
```
POST /audio-to-text
Content-Type: multipart/form-data

参数:
- file: 音频文件 (必需)
  - 支持格式: mp3, mp4, mpeg, mpga, m4a, wav, webm
  - 文件大小限制: 15MB
- user: 用户标识 (必需)
  - 类型: string
  - 说明: 开发者定义的用户唯一标识
```

### 响应格式
```json
{
  "text": "识别出的文字内容"
}
```

## 🔧 实现架构

### 1. 数据模型 (DifyApiModels.kt)

```kotlin
/**
 * 语音转文字请求
 */
data class AudioToTextRequest(
    @SerializedName("user")
    val user: String
)

/**
 * 语音转文字响应
 */
data class AudioToTextResponse(
    @SerializedName("text")
    val text: String
)
```

### 2. API客户端 (DifyApiClient.kt)

```kotlin
/**
 * 语音转文字
 */
suspend fun audioToText(
    audioFile: File,
    user: String
): AudioToTextResponse = withContext(Dispatchers.IO) {
    // 文件格式验证
    val supportedFormats = listOf("mp3", "mp4", "mpeg", "mpga", "m4a", "wav", "webm")
    val fileExtension = audioFile.extension.lowercase()
    if (!supportedFormats.contains(fileExtension)) {
        throw IllegalArgumentException("不支持的音频格式: $fileExtension")
    }
    
    // 文件大小验证 (15MB限制)
    val maxSizeBytes = 15 * 1024 * 1024
    if (audioFile.length() > maxSizeBytes) {
        throw IllegalArgumentException("音频文件过大: ${audioFile.length()} bytes")
    }
    
    // 构建multipart请求
    val requestBody = MultipartBody.Builder()
        .setType(MultipartBody.FORM)
        .addFormDataPart("user", user)
        .addFormDataPart(
            "file",
            audioFile.name,
            audioFile.asRequestBody("audio/*".toMediaType())
        )
        .build()
    
    // 发送请求
    val request = Request.Builder()
        .url("$baseUrl/audio-to-text")
        .post(requestBody)
        .addHeader("Authorization", "Bearer $apiKey")
        .build()
    
    val response = client.newCall(request).execute()
    
    if (!response.isSuccessful) {
        val errorBody = response.body?.string()
        throw IOException("Audio to text failed: ${response.code} - $errorBody")
    }
    
    val responseBody = response.body?.string() ?: throw IOException("Empty response body")
    gson.fromJson(responseBody, AudioToTextResponse::class.java)
}
```

### 3. 辅助工具类 (AudioToTextHelper.kt)

#### 核心功能
- ✅ **音频文件验证** - 格式、大小检查
- ✅ **文件信息获取** - 时长、比特率、采样率等
- ✅ **URI文件处理** - 从URI复制到临时目录
- ✅ **语音转文字执行** - 完整的转换流程
- ✅ **临时文件管理** - 自动清理机制

#### 主要方法
```kotlin
// 验证音频文件
fun validateAudioFile(file: File): AudioValidationResult

// 获取音频文件信息
fun getAudioFileInfo(file: File): AudioFileInfo

// 从URI复制音频文件
suspend fun copyAudioFromUri(context: Context, uri: Uri, fileName: String?): File

// 执行语音转文字
suspend fun convertAudioToText(audioFile: File, user: String, apiClient: DifyApiClient?): AudioToTextResult

// 清理临时文件
fun cleanupTempFiles(context: Context)
```

### 4. 用户界面 (AudioToTextActivity.kt)

#### 功能特性
- ✅ **文件选择** - 支持从设备选择音频文件
- ✅ **权限管理** - 自动请求存储权限
- ✅ **文件信息显示** - 显示音频文件详细信息
- ✅ **转换进度** - 实时显示转换状态
- ✅ **结果展示** - 格式化显示识别文本
- ✅ **错误处理** - 友好的错误提示
- ✅ **临时文件清理** - 手动和自动清理

## 📊 支持的音频格式

| 格式 | 扩展名 | MIME类型 | 说明 |
|------|--------|----------|------|
| MP3 | .mp3 | audio/mpeg | 最常用的音频格式 |
| MP4 Audio | .mp4, .m4a | audio/mp4 | 高质量音频格式 |
| MPEG | .mpeg, .mpga | audio/mpeg | MPEG音频格式 |
| WAV | .wav | audio/wav | 无损音频格式 |
| WebM | .webm | audio/webm | 现代Web音频格式 |

### 文件限制
- **最大文件大小**: 15MB
- **推荐时长**: 建议不超过10分钟
- **音质要求**: 建议采样率≥16kHz，比特率≥64kbps

## 🛡️ 错误处理

### 1. 文件验证错误
```kotlin
// 文件不存在
AudioValidationResult(false, "音频文件不存在")

// 文件过大
AudioValidationResult(false, "音频文件过大: 20MB，最大支持15MB")

// 格式不支持
AudioValidationResult(false, "不支持的音频格式: aac，支持的格式: [mp3, mp4, mpeg, mpga, m4a, wav, webm]")
```

### 2. API请求错误
```kotlin
// 网络连接失败
AudioToTextResult(false, "", "语音转文字失败: Connection reset")

// API认证失败
AudioToTextResult(false, "", "语音转文字失败: Audio to text failed: 401 - Unauthorized")

// 服务器错误
AudioToTextResult(false, "", "语音转文字失败: Audio to text failed: 500 - Internal Server Error")
```

### 3. 文件处理错误
```kotlin
// URI读取失败
IOException("无法打开音频文件")

// 文件复制失败
IOException("复制音频文件失败: Permission denied")
```

## 🧪 测试和验证

### 1. 单元测试
```kotlin
// 文件验证测试
@Test
fun testAudioFileValidation() {
    val validFile = File("test.mp3")
    val result = AudioToTextHelper.validateAudioFile(validFile)
    assertTrue(result.isValid)
}

// API调用测试
@Test
suspend fun testAudioToTextApi() {
    val apiClient = DifyApiClient(testApiKey, testBaseUrl)
    val result = apiClient.audioToText(testAudioFile, "test-user")
    assertNotNull(result.text)
}
```

### 2. 集成测试
```kotlin
// 完整流程测试
@Test
suspend fun testCompleteAudioToTextFlow() {
    val result = AudioToTextHelper.convertAudioToText(
        audioFile = testFile,
        user = "test-user"
    )
    assertTrue(result.success)
    assertNotEmpty(result.text)
}
```

### 3. 连接诊断
```kotlin
// 在DifyConnectionTester中添加语音转文字API测试
val diagnostic = DifyConnectionTester.runFullDiagnostic(context)
assertTrue(diagnostic.isAudioToTextSupported())
```

## 📱 使用示例

### 1. 基本使用
```kotlin
// 创建API客户端
val apiClient = DifyApiClient(DifyConfig.API_KEY, DifyConfig.BASE_URL)

// 执行语音转文字
val result = apiClient.audioToText(
    audioFile = File("/path/to/audio.mp3"),
    user = "user-123"
)

println("识别结果: ${result.text}")
```

### 2. 使用辅助工具
```kotlin
// 完整的转换流程
val result = AudioToTextHelper.convertAudioToText(
    audioFile = audioFile,
    user = "user-123"
)

if (result.success) {
    println("转换成功: ${result.text}")
    println("音频信息: ${result.audioInfo?.getDetailedInfo()}")
} else {
    println("转换失败: ${result.error}")
}
```

### 3. 从URI处理
```kotlin
// 从URI选择音频文件
val audioPickerLauncher = rememberLauncherForActivityResult(
    contract = ActivityResultContracts.GetContent()
) { uri: Uri? ->
    uri?.let { audioUri ->
        lifecycleScope.launch {
            // 复制文件到临时目录
            val tempFile = AudioToTextHelper.copyAudioFromUri(context, audioUri)
            
            // 执行转换
            val result = AudioToTextHelper.convertAudioToText(tempFile, "user-123")
            
            // 处理结果
            if (result.success) {
                // 显示识别文本
                showResult(result.text)
            }
        }
    }
}

// 启动文件选择器
audioPickerLauncher.launch("audio/*")
```

## 🔧 配置和部署

### 1. 权限配置 (AndroidManifest.xml)
```xml
<!-- 读取外部存储权限 -->
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />

<!-- 网络权限 -->
<uses-permission android:name="android.permission.INTERNET" />
```

### 2. 依赖配置 (build.gradle)
```kotlin
dependencies {
    // OkHttp for multipart requests
    implementation 'com.squareup.okhttp3:okhttp:4.12.0'
    
    // Gson for JSON parsing
    implementation 'com.google.code.gson:gson:2.10.1'
    
    // Coroutines for async operations
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3'
}
```

## 📋 相关文件

- `app/src/main/java/com/example/aihealthbutler/dify/DifyApiModels.kt` - 数据模型
- `app/src/main/java/com/example/aihealthbutler/dify/DifyApiClient.kt` - API客户端
- `app/src/main/java/com/example/aihealthbutler/dify/DifyConfig.kt` - 配置文件
- `app/src/main/java/com/example/aihealthbutler/utils/AudioToTextHelper.kt` - 辅助工具
- `app/src/main/java/com/example/aihealthbutler/ui/AudioToTextActivity.kt` - 用户界面
- `app/src/main/java/com/example/aihealthbutler/utils/DifyConnectionTester.kt` - 连接测试

## 🎉 实现总结

✅ **完整实现** - 从API调用到用户界面的完整解决方案
✅ **格式支持** - 支持7种主流音频格式
✅ **错误处理** - 完善的验证和错误处理机制
✅ **用户体验** - 友好的界面和进度提示
✅ **资源管理** - 自动的临时文件清理
✅ **测试覆盖** - 完整的测试和诊断功能

语音转文字功能已完全实现并可投入使用！🎤➡️📝
