package com.example.aihealthbutler.ui.components

import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

/**
 * 弹窗组件使用示例
 * 展示如何使用 CustomDeleteDialog 和 GenderSelectionDialog
 */
@Composable
fun DialogUsageExample() {
    var showDeleteDialog by remember { mutableStateOf(false) }
    var showGenderDialog by remember { mutableStateOf(false) }
    var selectedGender by remember { mutableStateOf("") }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Text(
            "弹窗组件示例",
            fontSize = 24.sp,
            fontWeight = FontWeight.Bold,
            modifier = Modifier.padding(bottom = 32.dp)
        )
        
        // 显示选中的性别
        if (selectedGender.isNotEmpty()) {
            Text(
                "已选择性别: $selectedGender",
                fontSize = 16.sp,
                color = Color.Gray,
                modifier = Modifier.padding(bottom = 16.dp)
            )
        }
        
        // 性别选择按钮
        Button(
            onClick = { showGenderDialog = true },
            colors = ButtonDefaults.buttonColors(
                containerColor = Color(0xFF2EB0AC)
            ),
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 16.dp)
        ) {
            Text("选择性别", color = Color.White)
        }
        
        // 删除确认按钮
        Button(
            onClick = { showDeleteDialog = true },
            colors = ButtonDefaults.buttonColors(
                containerColor = Color(0xFFE57373)
            ),
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("删除确认", color = Color.White)
        }
    }
    
    // 性别选择弹窗
    if (showGenderDialog) {
        GenderSelectionDialog(
            onDismiss = { showGenderDialog = false },
            onGenderSelected = { gender ->
                selectedGender = gender
                showGenderDialog = false
            }
        )
    }
    
    // 删除确认弹窗
    if (showDeleteDialog) {
        CustomDeleteDialog(
            title = "确认删除",
            message = "确定要删除这个项目吗？",
            onDismiss = { showDeleteDialog = false },
            onConfirm = {
                // 执行删除操作
                showDeleteDialog = false
            }
        )
    }
}
