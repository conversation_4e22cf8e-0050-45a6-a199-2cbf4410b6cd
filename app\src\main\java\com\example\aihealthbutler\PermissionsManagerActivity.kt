package com.example.aihealthbutler

import android.Manifest
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.provider.Settings
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.content.ContextCompat
import com.example.aihealthbutler.ui.theme.AIHealthButlerTheme

// 权限数据类
data class PermissionItem(
    val name: String,
    val description: String,
    val permissionId: String,
    val icon: Int = R.drawable.ic_security
)

class PermissionsManagerActivity : ComponentActivity() {
    // 权限列表
    private val permissionsList = listOf(
        PermissionItem(
            name = "相册/图片权限",
            description = "允许访问您的相册，选择图片上传",
            permissionId = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) 
                Manifest.permission.READ_MEDIA_IMAGES 
            else 
                Manifest.permission.READ_EXTERNAL_STORAGE,
            icon = R.drawable.ic_image
        ),
        PermissionItem(
            name = "相机权限",
            description = "允许使用相机拍摄照片",
            permissionId = Manifest.permission.CAMERA,
            icon = R.drawable.ic_security
        ),
        PermissionItem(
            name = "语音识别权限",
            description = "设置允许使用语音输入功能进行聊天",
            permissionId = Manifest.permission.RECORD_AUDIO,
            icon = R.drawable.ic_mic
        ),
        PermissionItem(
            name = "位置权限",
            description = "获取您的位置信息，提供位置相关服务",
            permissionId = Manifest.permission.ACCESS_FINE_LOCATION,
            icon = R.drawable.ic_location
        )
        // 可以添加更多权限
    )
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            AIHealthButlerTheme {
                PermissionsManagerScreen(
                    permissionsList = permissionsList,
                    checkPermission = { permission ->
                        ContextCompat.checkSelfPermission(
                            this,
                            permission
                        ) == PackageManager.PERMISSION_GRANTED
                    },
                    openAppSettings = { openAppSettings() }
                )
            }
        }
    }
    
    // 打开应用设置
    private fun openAppSettings() {
        val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
            data = Uri.fromParts("package", packageName, null)
        }
        startActivity(intent)
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PermissionsManagerScreen(
    permissionsList: List<PermissionItem>,
    checkPermission: (String) -> Boolean,
    openAppSettings: () -> Unit
) {
    val context = LocalContext.current
    
    Scaffold(
        topBar = {
            CenterAlignedTopAppBar(
                title = {
                    Text(
                        text = "权限管理",
                        fontSize = 20.sp,
                        fontWeight = FontWeight.Bold
                    )
                },
                navigationIcon = {
                    IconButton(onClick = {
                        (context as? ComponentActivity)?.finish()
                    }) {
                        Icon(
                            painter = painterResource(id = R.drawable.left_arrow),
                            contentDescription = "返回",
                            modifier = Modifier.size(24.dp)
                        )
                    }
                },
                colors = TopAppBarDefaults.centerAlignedTopAppBarColors(
                    containerColor = Color(0xFFF5F6F7)
                )
            )
        },
        containerColor = Color(0xFFF5F6F7)
    ) { innerPadding ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(innerPadding)
        ) {
            // 背景
            Image(
                painter = painterResource(id = R.drawable.permission_back),
                contentDescription = null,
                modifier = Modifier.fillMaxSize(),
                contentScale = ContentScale.FillBounds
            )
            
            // 页面内容
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(16.dp)
            ) {
                // 说明文本
                Text(
                    text = "请管理应用权限，开启相关权限可以获得更好的使用体验",
                    fontSize = 12.sp,
                    color = Color.Gray,
                    modifier = Modifier.padding(bottom = 24.dp)
                )
                
                // 权限列表
                LazyColumn(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    items(permissionsList) { permission ->
                        PermissionCard(
                            permission = permission,
                            isGranted = checkPermission(permission.permissionId),
                            onOpenSettings = openAppSettings
                        )
                        Spacer(modifier = Modifier.height(16.dp))
                    }
                }
            }
        }
    }
}

@Composable
fun PermissionCard(
    permission: PermissionItem,
    isGranted: Boolean,
    onOpenSettings: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth(),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            // 权限名称和图标
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    painter = painterResource(id = permission.icon),
                    contentDescription = permission.name,
                    tint = Color(0xFF22C1C3),
                    modifier = Modifier.size(28.dp)
                )
                
                Spacer(modifier = Modifier.width(12.dp))
                
                Text(
                    text = permission.name,
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.Black
                )
                
                Spacer(modifier = Modifier.weight(1f))
                
                // 权限状态
                Icon(
                    painter = painterResource(
                        id = if (isGranted) R.drawable.ic_check_circle else R.drawable.ic_warning
                    ),
                    contentDescription = if (isGranted) "已授权" else "未授权",
                    tint = if (isGranted) Color(0xFF22C1C3) else Color(0xFFFF9800),
                    modifier = Modifier.size(24.dp)
                )
            }
            
            // 权限描述
            Text(
                text = permission.description,
                fontSize = 14.sp,
                color = Color.Gray,
                modifier = Modifier.padding(start = 40.dp, top = 4.dp, bottom = 12.dp)
            )
            
            // 权限设置按钮
            if (!isGranted) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = 2.dp),
                    horizontalArrangement = Arrangement.End // 关键布局参数
                ) {
                    Button(
                        onClick = onOpenSettings,
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFF22C1C3)
                        ),
                        modifier = Modifier
                            .fillMaxWidth(0.36f)
                            .padding(top = 2.dp)
                    ) {
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(text = "前往设置")
                        }
                    }
                }
            }
        }
    }
} 