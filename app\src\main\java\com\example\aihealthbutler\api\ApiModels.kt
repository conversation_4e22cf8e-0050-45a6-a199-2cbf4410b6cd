package com.example.aihealthbutler.api

import com.google.gson.annotations.SerializedName

// 基础响应模型
data class BaseResponse(
    val code: Int,
    val message: String,
    val timestamp: Long? = null
)

data class AuthData(
    val token: String,
    val defaultId: String
)
// 用户认证相关模型
data class RegisterRequest(
    val account: String,
    val password: String
)

//用户注册
data class RegisterResponse(
    val code: Int,
    val message: String,
    val data: AuthData? = null,
    val timestamp: Long? = null
)
//用户登录
data class LoginRequest(
    val account: String,
    val password: String
)

data class LoginResponse(
    val code: Int,
    val message: String,
    val data: AuthData? = null,
    val timestamp: Long? = null
)

// 家庭成员相关模型

// 添加账号下家庭成员
data class AddMemberRequest(
    val account: String
)

data class AddMemberResponse(
    val code: Int,
    val message: String,
    val data: AddMemberData,
    val timestamp: Long
)

data class AddMemberData(
    @SerializedName("defaultId")
    val default_id: String?
)
// 切换家庭成员
data class SwitchMemberRequest(
    val account: String,
    val defaultId: String
)

data class SwitchMemberResponse(
    val code: Int,
    val message: String,
    val data: SwitchMemberData,
    val timestamp: Long
)

data class SwitchMemberData(
    val token: String,
    val defaultId: String
)

// 家庭关系相关模型
// 创建家庭关系
data class CreateFamilyRequest(
    val role: String
)

data class CreateFamilyResponse(
    val code: Int,
    val message: String,
    val data: FamilyRelationData,
    val timestamp: Long
)

data class FamilyRelationData(
    val account: String,
    val defaultId: String,
    val role: String
)

// 更改家庭关系
data class UpdateFamilyRequest(
    val role: String
)

data class UpdateFamilyResponse(
    val code: Int,
    val message: String,
    val data: FamilyRelationData?,
    val timestamp: Long
)

//删除家庭关系
data class DeleteFamilyResponse(
    val code: Int,
    val message: String,
    val data: Boolean,
    val timestamp: Long
)

//获取家庭关系
data class GetFamilyResponse(
    val code: Int,
    val message: String,
    val data: FamilyRelationData,
    val timestamp: Long
)

// 获取用户所有关系响应 - 根据实际API响应格式
data class GetAllRelationshipsResponse(
    val code: Int,
    val message: String,
    val data: Map<String, String>, // 格式：{"userId": "关系名称"}
    val timestamp: Long
)

// 设置家庭关系 - 新接口
data class SetFamilyRelationshipRequest(
    val fromMemberId: String,
    val toMemberId: String,
    val relationship: String
)

data class SetFamilyRelationshipResponse(
    val code: Int = 200,
    val message: String = "",
    val data: Any? = null,
    val timestamp: Long = System.currentTimeMillis()
)


// 个人信息相关模型
data class PersonInfoRequest(
    val name: String,
    val sex: Int,
    val birthday: String,
    val height: Int,
    val weight: Float,
    val role: String? = null
)

data class PersonInfoResponse(
    val code: Int,
    val message: String,
    val data: PersonInfoData? = null,
    val timestamp: Long? = null
)

data class PersonInfoData(
    val defaultId: String,
    val name: String,
    val sex: Int,
    val sexText: String,
    val birthday: String,
    val age: Int,
    val height: Int,
    val weight: Float,
    val bmi: Float,
    val account: String,
    val consultants: Int = 0
)

// 健康信息相关模型
data class CreateHealthRequest(
    val defaultId: String,
    val medicalCondition: String,
    val allergyHistory: String,
    val vaccinationHistory: String,
    val smokingStatus: String,
    val drinkingStatus: String,
    val familyMedicalHistory: String,
    val menstrualStatus: String,
    val pregnancyStatus: String
)

data class UpdateHealthRequest(
    val healthId: Int,
    val defaultId: String,
    val medicalCondition: String,
    val allergyHistory: String,
    val vaccinationHistory: String,
    val smokingStatus: String,
    val drinkingStatus: String,
    val familyMedicalHistory: String,
    val menstrualStatus: String,
    val pregnancyStatus: String
)

data class HealthInfo(
    val healthId: Int?,
    val medicalCondition: String?,
    val allergyHistory: String?,
    val vaccinationHistory: String?,
    val smokingStatus: String?,
    val drinkingStatus: String?,
    val familyMedicalHistory: String?,
    val menstrualStatus: String?,
    val pregnancyStatus: String?,
    val defaultId: String? = null,
    val surgeryHistories: List<SurgeryHistory>? = null
) : java.io.Serializable

data class HealthInfoResponse(
    val code: Int,
    val message: String,
    val data: HealthInfo? = null,
    val timestamp: Long? = null
)

// 手术史相关模型
data class SurgeryHistory(
    val surgeryId: Int?,
    val healthId: Int? = null,
    val surgeryName: String?,
    val surgeryDate: String?,
    val surgeryDescription: String?
) : java.io.Serializable

data class CreateSurgeryRequest(
    val healthId: Int,
    val surgeryName: String,
    val surgeryDate: String,
    val surgeryDescription: String
)

// 更新手术史记录请求
data class UpdateSurgeryRequest(
    val surgeryId: Int,
    val healthId: Int,
    val surgeryName: String,
    val surgeryDate: String,
    val surgeryDescription: String
)

// 手术史记录列表响应
data class SurgeryHistoryListResponse(
    val code: Int,
    val message: String,
    val data: List<SurgeryHistory>? = null,
    val timestamp: Long? = null
)

// 用药计划相关模型
data class MedicationPlanRequest(
    val pillName: String,
    val startDate: String,
    val duration: Int,
    val method: String,
    val frequency: String,
    val frequencyDetail: Int,
    val firstTime: String,
    val secondTime: String,
    val thirdTime: String?,
    val dosage: String,
    val guide: String?,
    val note: String?,
    val defaultId: String  // 根据Postman测试结果使用defaultId
)

// 用药计划响应模型
data class MedicationPlanResponse(
    val code: Int,
    val message: String,
    val data: MedicationPlanDetail?,
    val timestamp: Long
)

// 用药计划详情模型
data class MedicationPlanDetail(
    val mplanId: Int,
    val pillName: String,
    val startDate: String,
    val duration: Int,
    val method: String,
    val frequency: String,
    val frequencyDetail: Int,
    val firstTime: String,
    val secondTime: String,
    val thirdTime: String?,
    val dosage: String,
    val guide: String?,
    val note: String?
)

// 用药计划列表响应
data class MedicationPlanListResponse(
    val code: Int,
    val message: String,
    val data: List<MedicationPlanDetail>?,
    val timestamp: Long
)

// 资料夹相关模型
data class CreateFolderRequest(
    val picture: String
)

// 图片详情模型
data class PictureDetail(
    val pictureId: Int,
    val pictureUrl: String,
    val uploadedAt: String
)

data class FolderDetail(
    val folderId: Int,
    val pictures: List<PictureDetail>? = null,  // 新的图片数组格式
    val defaultId: String? = null,
    // 保持向后兼容
    val picture: String? = null   // 旧格式兼容
)

// 标准的资料夹响应模型（符合Postman文档）
data class StandardFolderResponse(
    val code: Int,
    val message: String,  // 注意：使用message而不是msg
    val data: FolderDetail? = null,
    val timestamp: Long? = null
)





data class FolderListResponse(
    val code: Int,
    val message: String,
    val data: List<FolderDetail>? = null,
    val timestamp: Long? = null
)

data class FolderDeleteResponse(
    val code: Int,
    val message: String,
    val data: Boolean,
    val timestamp: Long
)

// 删除家庭成员请求
data class DeleteFamilyMemberRequest(
    val target_id: String
)

// 获取所有家庭成员信息响应
data class GetAllFamilyMembersResponse(
    val code: Int,
    val message: String,
    val data: List<FamilyMemberData>,
    val timestamp: Long
)

// 家庭成员数据模型
data class FamilyMemberData(
    val defaultId: String,
    val name: String?,
    val sex: Int,
    val sexText: String?,
    val birthday: String?,
    val age: Int,
    val height: Int,
    val weight: Float,
    val bmi: Float,
    val account: String? = null,
    val consultants: Int = 0,
    val role: String? = null,
    val healthInfo: HealthInfo? = null
) : java.io.Serializable









