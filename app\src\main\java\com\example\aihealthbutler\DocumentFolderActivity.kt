package com.example.aihealthbutler

import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.aihealthbutler.ui.theme.AIHealthButlerTheme
import com.example.aihealthbutler.ui.components.CustomDeleteDialog
import androidx.compose.ui.platform.LocalContext
import androidx.compose.runtime.mutableStateOf
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.IconButton
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.Arrangement
import android.Manifest
import android.content.pm.PackageManager
import android.net.Uri
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.content.ContextCompat
import java.io.File
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.runtime.getValue
import android.util.Log
import android.widget.Toast
import android.os.Build
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.aihealthbutler.api.SessionManager
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import coil.compose.AsyncImage
import coil.request.ImageRequest
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.layout.ContentScale
import com.example.aihealthbutler.api.*
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.compose.runtime.remember
import androidx.compose.runtime.remember
import androidx.compose.runtime.remember
import kotlinx.coroutines.flow.firstOrNull
import androidx.lifecycle.ViewModelProvider
import android.content.Context
import android.content.SharedPreferences
import com.example.aihealthbutler.utils.ImageCacheManager

// 辅助函数：修复图片URL
private fun fixImageUrl(url: String?): String? {
    if (url == null || url.isBlank()) return null

    return when {
        // 如果是content URI，直接返回（Android可以处理）
        url.startsWith("content://") -> url
        // 如果已经是正确的HTTP/HTTPS URL，直接返回
        url.startsWith("http://") || url.startsWith("https://") -> url
        // 如果是HTTP/HTTPS URL，修复错误的格式
        url.contains("https://aie-picture.https://") -> url.replace("https://aie-picture.https://", "https://")
        // 如果是Android应用内部存储路径，转换为file URI
        url.startsWith("/data/user/0/com.example.aihealthbutler/") -> "file://$url"
        // 如果是Windows文件路径，转换为HTTPS URL（假设有图片服务器）
        url.contains(":\\") -> {
            // 提取文件名
            val fileName = url.substringAfterLast("\\")
            "https://qcx.yuneyang.top/api/images/$fileName"
        }
        // 如果是Linux文件路径，转换为file URI
        url.startsWith("/") -> "file://$url"
        // 其他情况，假设是文件名，构造HTTPS URL
        else -> "https://qcx.yuneyang.top/api/images/$url"
    }
}

class DocumentFolderViewModel(
    private val repository: Repository,
    private val sessionManager: SessionManager
) : ViewModel() {
    private val TAG = "DocumentFolderVM"
    
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()
    
    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error.asStateFlow()
    
    private val _folders = MutableStateFlow<List<FolderItem>>(emptyList())
    val folders: StateFlow<List<FolderItem>> = _folders.asStateFlow()
    
    init {
        loadFolders()
    }
    
    fun loadFolders() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                _error.value = null
                
                val token = sessionManager.token.firstOrNull() ?: throw Exception("未登录")
                val defaultId = sessionManager.defaultId.firstOrNull() ?: throw Exception("未设置默认成员")

                Log.d(TAG, "请求资料夹列表 - defaultId: $defaultId")
                Log.d(TAG, "请求URL: https://qcx.yuneyang.top/api/folder/user/$defaultId")

                val result = repository.getFoldersByUserId(token, defaultId)
                
                result.fold(
                    onSuccess = { response ->
                        Log.d(TAG, "=== API响应调试信息 ===")
                        Log.d(TAG, "响应码: ${response.code}")
                        Log.d(TAG, "响应消息: ${response.message}")
                        Log.d(TAG, "响应数据: ${response.data}")

                        if (response.code == 200 && response.data != null) {
                            // 根据新API格式，转换FolderDetail为FolderItem
                            val folderItem = FolderItem(
                                folderId = response.data.folderId,
                                pictures = response.data.pictures,
                                defaultId = response.data.defaultId,
                                picture = response.data.picture // 向后兼容
                            )

                            Log.d(TAG, "转换后的FolderItem: $folderItem")
                            Log.d(TAG, "图片URL: '${folderItem.picture}'")

                            _folders.value = listOf(folderItem)
                            _error.value = null
                            Log.d(TAG, "获取资料夹成功，设置到UI")
                        } else {
                            _error.value = response.message ?: "获取资料夹失败"
                            Log.w(TAG, "获取资料夹失败: ${response.message}")
                        }
                        Log.d(TAG, "====================")
                    },
                    onFailure = { e ->
                        val errorMessage = when {
                            e.message?.contains("404") == true -> "暂无资料夹记录，点击右下角按钮开始上传"
                            e.message?.contains("网络") == true -> "网络连接异常，请检查网络设置"
                            else -> "获取资料夹列表失败，请稍后重试"
                        }
                        _error.value = errorMessage
                        Log.e(TAG, "获取资料夹列表失败", e)
                    }
                )
            } catch (e: Exception) {
                Log.e(TAG, "获取资料夹列表异常", e)
                val errorMessage = when {
                    e.message?.contains("404") == true -> "暂无资料夹记录，点击右下角按钮开始上传"
                    e.message?.contains("网络") == true -> "网络连接异常，请检查网络设置"
                    else -> "获取资料夹列表失败，请稍后重试"
                }
                _error.value = errorMessage
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    fun createFolder(picture: String) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                _error.value = null
                
                val token = sessionManager.token.firstOrNull() ?: throw Exception("未登录")
                
                val request = CreateFolderRequest(picture = picture)
                val result = repository.createFolder(token, request)
                
                result.fold(
                    onSuccess = { response ->
                        if (response.code == 200) {
                            loadFolders()
                            _error.value = null
                            Log.d(TAG, "创建资料夹成功: ${response.data}")
                        } else {
                            _error.value = response.message ?: "创建资料夹失败"
                            Log.w(TAG, "创建资料夹失败: ${response.message}")
                        }
                    },
                    onFailure = { e ->
                        _error.value = "创建资料夹失败: ${e.message}"
                        Log.e(TAG, "创建资料夹失败", e)
                    }
                )
            } catch (e: Exception) {
                Log.e(TAG, "创建资料夹异常", e)
                _error.value = e.message ?: "创建资料夹失败"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    fun deleteFolder(folderId: Int) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                _error.value = null
                
                val token = sessionManager.token.firstOrNull() ?: throw Exception("未登录")
                
                val result = repository.deleteFolder(token, folderId)
                
                result.fold(
                    onSuccess = { response ->
                        if (response.code == 200 && response.data) {
                            loadFolders()
                            _error.value = null
                        } else {
                            _error.value = response.message
                        }
                    },
                    onFailure = { e ->
                        _error.value = "删除资料夹失败: ${e.message}"
                        Log.e(TAG, "删除资料夹失败", e)
                    }
                )
            } catch (e: Exception) {
                Log.e(TAG, "删除资料夹异常", e)
                _error.value = e.message ?: "删除资料夹失败"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    fun updateFolder(folderId: Int, picture: String) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                _error.value = null
                
                val token = sessionManager.token.firstOrNull() ?: throw Exception("未登录")
                
                val request = CreateFolderRequest(picture = picture)
                val result = repository.updateFolder(token, folderId, request)
                
                result.fold(
                    onSuccess = { response ->
                        if (response.code == 200) {
                            loadFolders()
                            _error.value = null
                            Log.d(TAG, "更新资料夹成功: ${response.data}")
                        } else {
                            _error.value = response.message ?: "更新资料夹失败"
                            Log.w(TAG, "更新资料夹失败: ${response.message}")
                        }
                    },
                    onFailure = { e ->
                        _error.value = "更新资料夹失败: ${e.message}"
                        Log.e(TAG, "更新资料夹失败", e)
                    }
                )
            } catch (e: Exception) {
                Log.e(TAG, "更新资料夹异常", e)
                _error.value = e.message ?: "更新资料夹失败"
            } finally {
                _isLoading.value = false
            }
        }
    }
}

class DocumentFolderActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        // 注册上传资料ActivityResult
        val uploadLauncher = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
            if (result.resultCode == RESULT_OK) {
                // 上传或编辑成功，刷新列表
                // 可根据需要获取返回的folderId、imageUrl等
                // val data = result.data
                // val folderId = data?.getStringExtra("FOLDER_ID")
                // val imageUrl = data?.getStringExtra("IMAGE_URL")
                // ...
                // 这里只需刷新
                //(viewModel as? DocumentFolderViewModel)?.loadFolders()
            }
        }
        setContent {
            AIHealthButlerTheme {
                val sessionManager = remember { SessionManager(applicationContext) }
                val repository = remember { Repository() }
                val viewModel = remember {
                    DocumentFolderViewModel(repository, sessionManager)
                }
                DocumentFolderScreen(
                    viewModel = viewModel,
                    onBack = {
                        startActivity(Intent(this@DocumentFolderActivity, MainActivity::class.java))
                        finish()
                    },
                    onUpload = {
                        // 跳转到上传页面
                        val intent = Intent(this@DocumentFolderActivity, UploadMaterialsActivity::class.java)
                        uploadLauncher.launch(intent)
                    },
                    onEdit = { folder ->
                        // 跳转到上传页面，带资料夹信息用于编辑
                        val intent = Intent(this@DocumentFolderActivity, UploadMaterialsActivity::class.java)
                        intent.putExtra("FOLDER_ID", folder.folderId.toString())

                        // 优先传递服务器图片URL，避免传递可能失效的content URI
                        val serverImageUrl = folder.firstPictureUrl
                        intent.putExtra("IMAGE_URL", serverImageUrl)

                        Log.d("DocumentFolderActivity", "编辑资料夹: folderId=${folder.folderId}")
                        Log.d("DocumentFolderActivity", "服务器图片URL: '$serverImageUrl'")
                        Log.d("DocumentFolderActivity", "图片来源: ${if (folder.pictures?.isNotEmpty() == true) "新格式pictures数组" else "旧格式picture字段"}")

                        uploadLauncher.launch(intent)
                    }
                )
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DocumentFolderScreen(
    viewModel: DocumentFolderViewModel,
    onBack: () -> Unit,
    onUpload: () -> Unit,
    onEdit: (FolderItem) -> Unit
) {
    val context = LocalContext.current
    val showBottomSheet = remember { mutableStateOf(false) }
    val showDeleteDialog = remember { mutableStateOf<Pair<Boolean, FolderItem?>>(false to null) }
    val isLoading by viewModel.isLoading.collectAsState()
    val error by viewModel.error.collectAsState()
    val folders by viewModel.folders.collectAsState()
    Scaffold(
        topBar = {
            CenterAlignedTopAppBar(
                title = {
                    Text(
                        text = "资料夹",
                        fontSize = 20.sp,
                        fontWeight = FontWeight.Bold
                    )
                },
                navigationIcon = {
                    IconButton(onClick = { onBack() }) {
                        Icon(
                            painter = painterResource(id = R.drawable.left_arrow),
                            contentDescription = "返回",
                            modifier = Modifier.size(24.dp)
                        )
                    }
                },
                colors = TopAppBarDefaults.centerAlignedTopAppBarColors(
                    containerColor = Color(0xFFF5F6F7)
                )
            )
        },
        containerColor = Color(0xFFF5F6F7)
    ) { innerPadding ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(Color(0xFFF5F6F7))
                .padding(innerPadding)
        ) {
            when {
                isLoading -> {
                    CircularProgressIndicator(
                        modifier = Modifier.align(Alignment.Center),
                        color = Color(0xFF22C1C3)
                    )
                }
                error != null -> {
                    Column(
                        modifier = Modifier
                            .align(Alignment.Center)
                            .padding(16.dp),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Text(
                            text = error!!,
                            color = Color.Red,
                            fontSize = 16.sp,
                            textAlign = TextAlign.Center
                        )
                        Spacer(modifier = Modifier.height(16.dp))
                        Button(
                            onClick = { viewModel.loadFolders() },
                            colors = ButtonDefaults.buttonColors(
                                containerColor = Color(0xFF22C1C3)
                            )
                        ) {
                            Text("重试")
                        }
                    }
                }
                folders.isEmpty() -> {
                    Column(
                        modifier = Modifier
                            .align(Alignment.Center)
                            .padding(16.dp),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Text(
                            text = "没有资料",
                            color = Color.Gray,
                            fontSize = 18.sp,
                            textAlign = TextAlign.Center
                        )
                    }
                }
                else -> {
                    LazyColumn(
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(top = 8.dp, start = 16.dp, end = 16.dp)
                    ) {
                        items(folders) { item ->
                            DocumentCard(
                                folderItem = item,
                                onDelete = {
                                    showDeleteDialog.value = true to item
                                },
                                onEdit = {
                                    onEdit(item)
                                }
                            )
                            Spacer(modifier = Modifier.height(16.dp))
                        }
                    }
                }
            }
            // 删除确认弹窗
            if (showDeleteDialog.value.first && showDeleteDialog.value.second != null) {
                CustomDeleteDialog(
                    title = "确认删除",
                    message = "确定要删除该资料夹吗？",
                    onDismiss = { showDeleteDialog.value = false to null },
                    onConfirm = {
                        val folder = showDeleteDialog.value.second
                        if (folder != null) {
                            viewModel.deleteFolder(folder.folderId)
                        }
                        showDeleteDialog.value = false to null
                    }
                )
            }
            // 右下角悬浮按钮
            Box(
                modifier = Modifier
                    .align(Alignment.BottomEnd)
                    .padding(28.dp)
            ) {
                FloatingActionButton(
                    onClick = { showBottomSheet.value = true },
                    containerColor = Color(0xFF22C1C3),
                    contentColor = Color.White,
                    shape = CircleShape
                ) {
                    Icon(
                        painter = painterResource(id = R.drawable.ic_addnew),
                        contentDescription = "添加",
                        modifier = Modifier.size(32.dp)
                    )
                }
            }
            // 添加底部弹窗
            if (showBottomSheet.value) {
                UploadBottomSheet(
                    onDismiss = { showBottomSheet.value = false },
                    onUpload = {
                        showBottomSheet.value = false
                        onUpload()
                    }
                )
            }
        }
    }
}

@Composable
fun DocumentCard(
    folderItem: FolderItem,
    onDelete: () -> Unit,
    onEdit: () -> Unit
) {
    val dropdownMenuExpanded = remember { mutableStateOf(false) }
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .height(120.dp)
            .background(Color.White, RoundedCornerShape(13.dp))
            .padding(horizontal = 16.dp, vertical = 12.dp)
    ) {
        Row(
            modifier = Modifier.fillMaxSize(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 左侧图片
            Box(
                modifier = Modifier
                    .size(80.dp)
                    .clip(RoundedCornerShape(8.dp)),
                contentAlignment = Alignment.Center
            ) {
                val context = LocalContext.current
                // 优先使用缓存的本地图片URI
                val cachedImageUri = ImageCacheManager.getImageUri(context, folderItem.folderId)
                // 使用新的firstPictureUrl属性，它会自动选择pictures数组的第一张图片或旧的picture字段
                val serverImageUrl = folderItem.firstPictureUrl
                val imageUrl = cachedImageUri ?: fixImageUrl(serverImageUrl)

                AsyncImage(
                    model = ImageRequest.Builder(LocalContext.current)
                        .data(imageUrl)
                        .crossfade(true)
                        .allowHardware(false) // 有时候硬件加速会导致问题
                        .build(),
                    contentDescription = "文件图片",
                    modifier = Modifier
                        .fillMaxSize()
                        .clip(RoundedCornerShape(8.dp)),
                    contentScale = ContentScale.Crop,
                    error = painterResource(id = R.drawable.ic_picture),
                    placeholder = painterResource(id = R.drawable.ic_picture),
                    onSuccess = {
                        Log.d("DocumentCard", "图片加载成功: $imageUrl")
                    },
                    onError = { error ->
                        Log.e("DocumentCard", "图片加载失败: $imageUrl, 错误: ${error.result.throwable}")
                    }
                )

                LaunchedEffect(folderItem.folderId) {
                    Log.d("DocumentCard", "=== 图片URL调试信息 ===")
                    Log.d("DocumentCard", "FolderItem: $folderItem")
                    Log.d("DocumentCard", "旧格式图片URL: '${folderItem.picture}'")
                    Log.d("DocumentCard", "新格式图片数组: ${folderItem.pictures}")
                    Log.d("DocumentCard", "第一张图片URL: '${folderItem.firstPictureUrl}'")
                    Log.d("DocumentCard", "缓存图片URI: '$cachedImageUri'")
                    Log.d("DocumentCard", "服务器图片URL: '$serverImageUrl'")
                    Log.d("DocumentCard", "最终使用URL: '$imageUrl'")
                    Log.d("DocumentCard", "========================")
                }
            }
            Spacer(modifier = Modifier.width(16.dp))
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "文件ID: ${folderItem.folderId}",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color(0xFF333333)
                )
            }
            // 右侧操作区
            Column(
                horizontalAlignment = Alignment.End,
                verticalArrangement = Arrangement.SpaceBetween,
                modifier = Modifier.fillMaxHeight()
            ) {
                // 右上角菜单按钮
                Box(
                    modifier = Modifier.wrapContentSize(Alignment.TopEnd)
                ) {
                    IconButton(
                        onClick = { dropdownMenuExpanded.value = true }
                    ) {
                        Icon(
                            painter = painterResource(id = R.drawable.ic_more_vert),
                            contentDescription = "更多选项",
                            tint = Color.Gray,
                            modifier = Modifier.size(24.dp)
                        )
                    }

                    // 下拉菜单
                    DropdownMenu(
                        expanded = dropdownMenuExpanded.value,
                        onDismissRequest = { dropdownMenuExpanded.value = false },
                        modifier = Modifier.background(Color.White)
                    ) {
                        Box(
                            modifier = Modifier
                                .clip(RoundedCornerShape(12.dp))
                                .background(Color.White)
                                .clickable {
                                    dropdownMenuExpanded.value = false
                                    onEdit()
                                }
                                .padding(horizontal = 5.dp, vertical = 1.dp)
                        ) {
                            Row(
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Icon(
                                    painter = painterResource(id = R.drawable.ic_edit),
                                    contentDescription = "编辑",
                                    tint = Color(0xFF757575),
                                    modifier = Modifier.size(24.dp)
                                )
                                Spacer(modifier = Modifier.width(8.dp))
                                Text(
                                    text = "编辑",
                                    color = Color(0xFF222222),
                                    fontSize = 18.sp,
                                    fontWeight = FontWeight.Normal
                                )
                            }
                        }
                        Spacer(modifier = Modifier.height(4.dp))
                        Box(
                            modifier = Modifier
                                .clip(RoundedCornerShape(12.dp))
                                .background(Color.White)
                                .clickable {
                                    dropdownMenuExpanded.value = false
                                    onDelete()
                                }
                                .padding(horizontal = 5.dp, vertical = 1.dp)
                        ) {
                            Row(
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Icon(
                                    painter = painterResource(id = R.drawable.ic_delete),
                                    contentDescription = "删除",
                                    tint = Color(0xFF757575),
                                    modifier = Modifier.size(24.dp)
                                )
                                Spacer(modifier = Modifier.width(8.dp))
                                Text(
                                    text = "删除",
                                    color = Color(0xFF222222),
                                    fontSize = 18.sp,
                                    fontWeight = FontWeight.Normal
                                )
                            }
                        }
                        Spacer(modifier = Modifier.height(4.dp))
                    }
                }
                Spacer(modifier = Modifier.weight(1f))

                // 右下角的"去解读"
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "去解读",
                        color = Color(0xFF549994),
                        fontSize = 14.sp,
                        modifier = Modifier.clickable { /* 去解读 */ }
                    )
                    Icon(
                        painter = painterResource(id = R.drawable.right_arrow),
                        contentDescription = null,
                        tint = Color(0xFF22C1C3),
                        modifier = Modifier.size(16.dp)
                    )
                }
            }
        }
    }
}

@Composable
fun UploadBottomSheet(
    onDismiss: () -> Unit,
    onUpload: () -> Unit
) {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color(0x80000000))
            .clickable { onDismiss() }
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .align(Alignment.BottomCenter)
                .background(
                    color = Color.White,
                    shape = RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp)
                )
                .clickable(enabled = false) { }  // 防止点击穿透
        ) {
            // 标题行，带两侧横线
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 24.dp, vertical = 20.dp),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.Center
            ) {
                // 左横线
                Box(
                    modifier = Modifier
                        .weight(0.1f)
                        .height(1.5.dp)
                        .background(Color(0xFFD8D8D8))
                )
                Text(
                    text = "上传资料",
                    modifier = Modifier.padding(horizontal = 16.dp),
                    textAlign = TextAlign.Center,
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Medium
                )
                // 右横线
                Box(
                    modifier = Modifier
                        .weight(0.1f)
                        .height(1.5.dp)
                        .background(Color(0xFFD8D8D8))
                )
            }


            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp, vertical = 24.dp)
            ) {
                // 支持上传
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "支持上传：",
                        color = Color(0xFF549994),
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Bold
                    )
                    Text(
                        text = "病历、报告单、药物、体检报告",
                        color = Color(0xFF666666),
                        fontSize = 16.sp,
                        modifier = Modifier.padding(start = 4.dp)
                    )
                }

                Spacer(modifier = Modifier.height(12.dp))

                // 文件类型
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "文件类型：",
                        color = Color(0xFF549994),
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Bold
                    )
                    Text(
                        text = "图片",
                        color = Color(0xFF666666),
                        fontSize = 16.sp,
                        modifier = Modifier.padding(start = 4.dp)
                    )
                }
            }

            // 相册获取按钮
            Button(
                onClick = { onUpload() },
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 20.dp, vertical = 24.dp)
                    .height(56.dp),
                shape = RoundedCornerShape(28.dp),
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color(0xFF22C1C3)
                )
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.Center
                ) {
                    Icon(
                        painter = painterResource(id = R.drawable.ic_image),
                        contentDescription = null,
                        modifier = Modifier.size(26.dp)
                    )
                    Spacer(modifier = Modifier.width(14.dp))
                    Text(
                        text = "相册获取",
                        fontSize = 18.sp,
                        color = Color.White
                    )
                }
            }

      }


 }
}











