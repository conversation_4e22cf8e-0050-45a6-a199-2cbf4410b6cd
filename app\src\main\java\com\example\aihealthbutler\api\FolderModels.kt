package com.example.aihealthbutler.api

import com.google.gson.annotations.SerializedName
import com.google.gson.JsonDeserializationContext
import com.google.gson.JsonDeserializer
import com.google.gson.JsonElement
import com.google.gson.JsonParseException
import android.util.Log
import java.lang.reflect.Type

// 基本响应接口，为了与ApiService.kt中的区分，这里代表非泛型接口
interface FolderApiResponse {
    val code: Int
    val msg: String
}

// 根据Postman文档的资料夹响应模型
data class FolderResponse(
    override val code: Int,
    override val msg: String,
    val data: FolderItem? = null,
    val timestamp: Long? = null
) : FolderApiResponse

// 上传响应模型
data class FolderUploadResponse(
    override val code: Int,
    override val msg: String,
    val data: FolderUploadData
) : FolderApiResponse

// 资料夹数据模型 - 与新API格式一致
data class FolderItem(
    val folderId: Int,
    val pictures: List<PictureDetail>? = null,  // 新的图片数组格式
    val defaultId: String? = null,
    // 保持向后兼容
    val picture: String? = null   // 旧格式兼容
) {
    // 获取第一张图片的URL（用于显示）
    val firstPictureUrl: String?
        get() = pictures?.firstOrNull()?.pictureUrl ?: picture
}

// 上传数据模型
data class FolderUploadData(
    @SerializedName("folder_id") val folderId: Int,
    val url: String
)

// 上传响应模型
data class UploadResponse(
    val code: Int,
    val message: String,
    val data: UploadData
)

data class UploadData(
    @SerializedName("folder_id") val folderId: Int,
    val url: String
)

// 统一的Response反序列化器
class FolderResponseDeserializer : JsonDeserializer<FolderResponse> {
    @Throws(JsonParseException::class)
    override fun deserialize(
        json: JsonElement, 
        typeOfT: Type, 
        context: JsonDeserializationContext
    ): FolderResponse {
        val jsonObject = json.asJsonObject
        val code = jsonObject.get("code").asInt
        val msg = jsonObject.get("msg").asString
        
        val folderItems = mutableListOf<FolderItem>()
        var singleItem: FolderItem? = null
        val dataElement = jsonObject.get("data")
        
        if (dataElement != null && !dataElement.isJsonNull) { // 添加对JsonNull的检查
            if (dataElement.isJsonObject) {
                // 单个对象情况
                val obj = dataElement.asJsonObject
                try {
                    val defaultId = if (obj.has("defaultId")) obj.get("defaultId").asString
                                   else if (obj.has("default_id")) obj.get("default_id").asString
                                   else ""

                    // 处理folderId可能是数字或字符串的情况
                    val folderId = if (obj.has("folderId")) {
                        when {
                            obj.get("folderId").isJsonPrimitive && obj.get("folderId").asJsonPrimitive.isNumber ->
                                obj.get("folderId").asInt.toString()
                            obj.get("folderId").isJsonPrimitive ->
                                obj.get("folderId").asString
                            else -> ""
                        }
                    } else if (obj.has("folder_id")) {
                        when {
                            obj.get("folder_id").isJsonPrimitive && obj.get("folder_id").asJsonPrimitive.isNumber ->
                                obj.get("folder_id").asInt.toString()
                            obj.get("folder_id").isJsonPrimitive ->
                                obj.get("folder_id").asString
                            else -> ""
                        }
                    } else ""

                    // 解析新格式的pictures数组
                    val pictures = if (obj.has("pictures") && obj.get("pictures").isJsonArray) {
                        val picturesArray = obj.get("pictures").asJsonArray
                        picturesArray.map { pictureElement ->
                            val pictureObj = pictureElement.asJsonObject
                            PictureDetail(
                                pictureId = pictureObj.get("pictureId").asInt,
                                pictureUrl = pictureObj.get("pictureUrl").asString,
                                uploadedAt = pictureObj.get("uploadedAt").asString
                            )
                        }
                    } else null

                    // 保持向后兼容的picture字段
                    val picture = if (obj.has("picture")) obj.get("picture").asString else ""
                    
                    // 创建单个项
                    singleItem = FolderItem(
                        folderId = folderId.toInt(),
                        pictures = pictures, // 新格式的pictures数组
                        defaultId = defaultId,
                        picture = picture // 向后兼容
                    )
                    // 同时也添加到列表中，保持兼容性
                    singleItem?.let { folderItems.add(it) }
                    
                    Log.d("FolderResponseDeserializer", "解析单个对象成功: defaultId=$defaultId, folderId=$folderId, picture=$picture")
                } catch (e: Exception) {
                    Log.e("FolderResponseDeserializer", "解析单个对象失败", e)
                }
            } else if (dataElement.isJsonArray) {
                // 数组情况
                dataElement.asJsonArray.forEach { element ->
                    if (element.isJsonObject) {
                        val obj = element.asJsonObject
                        try {
                            val defaultId = if (obj.has("default_id")) obj.get("default_id").asString else ""
                            
                            // 处理folder_id可能是数字或字符串的情况
                            val folderId = if (obj.has("folder_id")) {
                                when {
                                    obj.get("folder_id").isJsonPrimitive && obj.get("folder_id").asJsonPrimitive.isNumber ->
                                        obj.get("folder_id").asInt.toString()
                                    obj.get("folder_id").isJsonPrimitive -> 
                                        obj.get("folder_id").asString
                                    else -> ""
                                }
                            } else ""
                            
                            val picture = if (obj.has("picture")) obj.get("picture").asString else ""
                            
                            folderItems.add(FolderItem(
                                folderId = folderId.toInt(),
                                pictures = null, // 旧格式没有pictures数组
                                defaultId = defaultId,
                                picture = picture
                            ))
                            Log.d("FolderResponseDeserializer", "解析数组项成功: defaultId=$defaultId, folderId=$folderId, picture=$picture")
                        } catch (e: Exception) {
                            Log.e("FolderResponseDeserializer", "解析项目失败", e)
                        }
                    }
                }
            }
        }
        
        // 根据新的FolderResponse结构，只返回单个对象
        val singleData = if (folderItems.isNotEmpty()) folderItems[0] else singleItem
        return FolderResponse(code, msg, singleData, null)
    }
}

// 辅助函数：修复图片URL
fun fixImageUrl(url: String?): String? {
    if (url == null) return null
    return url.replace("https://aie-picture.https://", "https://")
}

// UI显示用的扩展属性
val FolderItem.displayUrl: String?
    get() = fixImageUrl(picture) ?: picture