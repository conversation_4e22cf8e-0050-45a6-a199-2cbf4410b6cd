package com.example.aihealthbutler.dify

import com.google.gson.annotations.SerializedName

/**
 * Dify API 数据模型
 */

/**
 * 语音转文字请求
 */
data class AudioToTextRequest(
    @SerializedName("user")
    val user: String
)

/**
 * 语音转文字响应
 */
data class AudioToTextResponse(
    @SerializedName("text")
    val text: String
)

/**
 * 聊天消息请求
 */
data class ChatMessageRequest(
    @SerializedName("inputs")
    val inputs: Map<String, Any> = emptyMap(),
    
    @SerializedName("query")
    val query: String,
    
    @SerializedName("response_mode")
    val responseMode: String = "streaming", // "streaming" 或 "blocking"
    
    @SerializedName("user")
    val user: String,
    
    @SerializedName("conversation_id")
    val conversationId: String? = null,
    
    @SerializedName("files")
    val files: List<FileInfo>? = null,
    
    @SerializedName("auto_generate_name")
    val autoGenerateName: Boolean = true,
    
    @SerializedName("trace_id")
    val traceId: String? = null
)

/**
 * 文件信息
 */
data class FileInfo(
    @SerializedName("type")
    val type: String, // "image", "document", "audio", "video", "custom"
    
    @SerializedName("transfer_method")
    val transferMethod: String, // "remote_url" 或 "local_file"
    
    @SerializedName("url")
    val url: String? = null, // 当 transfer_method 为 "remote_url" 时使用
    
    @SerializedName("upload_file_id")
    val uploadFileId: String? = null // 当 transfer_method 为 "local_file" 时使用
)

/**
 * 聊天完成响应（阻塞模式）
 */
data class ChatCompletionResponse(
    @SerializedName("event")
    val event: String,
    
    @SerializedName("task_id")
    val taskId: String,
    
    @SerializedName("id")
    val id: String,
    
    @SerializedName("message_id")
    val messageId: String,
    
    @SerializedName("conversation_id")
    val conversationId: String,
    
    @SerializedName("mode")
    val mode: String,
    
    @SerializedName("answer")
    val answer: String,
    
    @SerializedName("metadata")
    val metadata: Metadata,
    
    @SerializedName("created_at")
    val createdAt: Long
)

/**
 * 流式聊天响应
 */
data class StreamChatResponse(
    @SerializedName("event")
    val event: String,
    
    @SerializedName("task_id")
    val taskId: String? = null,
    
    @SerializedName("message_id")
    val messageId: String? = null,
    
    @SerializedName("conversation_id")
    val conversationId: String? = null,
    
    @SerializedName("answer")
    val answer: String? = null,
    
    @SerializedName("metadata")
    val metadata: Metadata? = null,
    
    @SerializedName("created_at")
    val createdAt: Long? = null,
    
    // 文件事件相关
    @SerializedName("id")
    val id: String? = null,
    
    @SerializedName("type")
    val type: String? = null,
    
    @SerializedName("belongs_to")
    val belongsTo: String? = null,
    
    @SerializedName("url")
    val url: String? = null,
    
    // TTS 相关
    @SerializedName("audio")
    val audio: String? = null,
    
    // 工作流相关
    @SerializedName("workflow_run_id")
    val workflowRunId: String? = null,
    
    @SerializedName("data")
    val data: Any? = null,
    
    // 错误相关
    @SerializedName("status")
    val status: Int? = null,
    
    @SerializedName("code")
    val code: String? = null,
    
    @SerializedName("message")
    val message: String? = null
)

/**
 * 元数据
 */
data class Metadata(
    @SerializedName("usage")
    val usage: Usage? = null,
    
    @SerializedName("retriever_resources")
    val retrieverResources: List<RetrieverResource>? = null
)

/**
 * 使用量信息
 */
data class Usage(
    @SerializedName("prompt_tokens")
    val promptTokens: Int,
    
    @SerializedName("prompt_unit_price")
    val promptUnitPrice: String,
    
    @SerializedName("prompt_price_unit")
    val promptPriceUnit: String,
    
    @SerializedName("prompt_price")
    val promptPrice: String,
    
    @SerializedName("completion_tokens")
    val completionTokens: Int,
    
    @SerializedName("completion_unit_price")
    val completionUnitPrice: String,
    
    @SerializedName("completion_price_unit")
    val completionPriceUnit: String,
    
    @SerializedName("completion_price")
    val completionPrice: String,
    
    @SerializedName("total_tokens")
    val totalTokens: Int,
    
    @SerializedName("total_price")
    val totalPrice: String,
    
    @SerializedName("currency")
    val currency: String,
    
    @SerializedName("latency")
    val latency: Double
)

/**
 * 检索资源
 */
data class RetrieverResource(
    @SerializedName("position")
    val position: Int,
    
    @SerializedName("dataset_id")
    val datasetId: String,
    
    @SerializedName("dataset_name")
    val datasetName: String,
    
    @SerializedName("document_id")
    val documentId: String,
    
    @SerializedName("document_name")
    val documentName: String,
    
    @SerializedName("segment_id")
    val segmentId: String,
    
    @SerializedName("score")
    val score: Double,
    
    @SerializedName("content")
    val content: String
)

/**
 * 文件上传响应
 */
data class FileUploadResponse(
    @SerializedName("id")
    val id: String,
    
    @SerializedName("name")
    val name: String,
    
    @SerializedName("size")
    val size: Int,
    
    @SerializedName("extension")
    val extension: String,
    
    @SerializedName("mime_type")
    val mimeType: String,
    
    @SerializedName("created_by")
    val createdBy: String,
    
    @SerializedName("created_at")
    val createdAt: Long
)

/**
 * 停止响应请求
 */
data class StopResponseRequest(
    @SerializedName("user")
    val user: String
)

/**
 * 停止响应结果
 */
data class StopResponseResult(
    @SerializedName("result")
    val result: String
)

/**
 * 消息反馈请求
 */
data class MessageFeedbackRequest(
    @SerializedName("rating")
    val rating: String?, // "like", "dislike", null
    
    @SerializedName("user")
    val user: String,
    
    @SerializedName("content")
    val content: String? = null
)

/**
 * 消息反馈结果
 */
data class MessageFeedbackResult(
    @SerializedName("result")
    val result: String
)

/**
 * API 错误响应
 */
data class DifyApiError(
    @SerializedName("status")
    val status: Int,
    
    @SerializedName("code")
    val code: String,
    
    @SerializedName("message")
    val message: String
)

/**
 * 流式事件类型
 */
object StreamEventType {
    const val MESSAGE = "message"
    const val MESSAGE_FILE = "message_file"
    const val MESSAGE_END = "message_end"
    const val TTS_MESSAGE = "tts_message"
    const val TTS_MESSAGE_END = "tts_message_end"
    const val MESSAGE_REPLACE = "message_replace"
    const val WORKFLOW_STARTED = "workflow_started"
    const val NODE_STARTED = "node_started"
    const val NODE_FINISHED = "node_finished"
    const val WORKFLOW_FINISHED = "workflow_finished"
    const val ERROR = "error"
    const val PING = "ping"
}

/**
 * 响应模式
 */
object ResponseMode {
    const val STREAMING = "streaming"
    const val BLOCKING = "blocking"
}

/**
 * 文件类型
 */
object FileType {
    const val IMAGE = "image"
    const val DOCUMENT = "document"
    const val AUDIO = "audio"
    const val VIDEO = "video"
    const val CUSTOM = "custom"
}

/**
 * 传输方式
 */
object TransferMethod {
    const val REMOTE_URL = "remote_url"
    const val LOCAL_FILE = "local_file"
}
