1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.aihealthbutler"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <!-- 相关应用权限 -->
12    <!-- 网络 -->
13    <uses-permission android:name="android.permission.INTERNET" />
13-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:8:5-67
13-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:8:22-64
14    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
14-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:9:5-79
14-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:9:22-76
15    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
15-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:10:5-76
15-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:10:22-73
16    <!-- 存储 -->
17    <uses-permission
17-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:12:5-13:51
18        android:name="android.permission.READ_EXTERNAL_STORAGE"
18-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:12:22-77
19        android:maxSdkVersion="32" />
19-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:13:22-48
20    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
20-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:14:5-80
20-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:14:22-78
21    <!-- 相机 -->
22    <uses-permission android:name="android.permission.CAMERA" />
22-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:16:5-17:71
22-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:16:22-62
23    <!-- 麦克风 -->
24    <uses-permission android:name="android.permission.RECORD_AUDIO" />
24-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:19:5-70
24-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:19:22-68
25    <!-- OPPO设备特殊权限 -->
26    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
26-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:21:5-79
26-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:21:22-77
27    <uses-permission android:name="android.permission.WAKE_LOCK" />
27-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:22:5-67
27-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:22:22-65
28    <!-- 位置 -->
29    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
29-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:24:5-78
29-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:24:22-76
30    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
30-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:25:5-80
30-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:25:22-78
31
32    <!-- 如果需要在Android 15上获取连接信息，需要添加此权限 -->
33    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
33-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:28:5-77
33-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:28:22-74
34    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
34-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:29:5-76
34-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:29:22-73
35
36    <permission
36-->[androidx.core:core:1.12.0] E:\Android\GradleRepository\caches\8.11.1\transforms\cd03543d47fe14dfe0d4dd3fa7052145\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
37        android:name="com.example.aihealthbutler.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
37-->[androidx.core:core:1.12.0] E:\Android\GradleRepository\caches\8.11.1\transforms\cd03543d47fe14dfe0d4dd3fa7052145\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
38        android:protectionLevel="signature" />
38-->[androidx.core:core:1.12.0] E:\Android\GradleRepository\caches\8.11.1\transforms\cd03543d47fe14dfe0d4dd3fa7052145\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
39
40    <uses-permission android:name="com.example.aihealthbutler.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" /> <!-- Don't require camera, as this requires a rear camera. This allows it to work on the Nexus 7 -->
40-->[androidx.core:core:1.12.0] E:\Android\GradleRepository\caches\8.11.1\transforms\cd03543d47fe14dfe0d4dd3fa7052145\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
40-->[androidx.core:core:1.12.0] E:\Android\GradleRepository\caches\8.11.1\transforms\cd03543d47fe14dfe0d4dd3fa7052145\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
41    <uses-feature
41-->[com.journeyapps:zxing-android-embedded:4.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\53177004b6b735f0506f1ff457ffd742\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:25:5-27:36
42        android:name="android.hardware.camera"
42-->[com.journeyapps:zxing-android-embedded:4.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\53177004b6b735f0506f1ff457ffd742\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:26:9-47
43        android:required="false" />
43-->[com.journeyapps:zxing-android-embedded:4.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\53177004b6b735f0506f1ff457ffd742\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:27:9-33
44    <uses-feature
44-->[com.journeyapps:zxing-android-embedded:4.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\53177004b6b735f0506f1ff457ffd742\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:28:5-30:36
45        android:name="android.hardware.camera.front"
45-->[com.journeyapps:zxing-android-embedded:4.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\53177004b6b735f0506f1ff457ffd742\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:29:9-53
46        android:required="false" /> <!-- TODO replace above two with next line after Android 4.2 -->
46-->[com.journeyapps:zxing-android-embedded:4.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\53177004b6b735f0506f1ff457ffd742\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:30:9-33
47    <!-- <uses-feature android:name="android.hardware.camera.any"/> -->
48    <uses-feature
48-->[com.journeyapps:zxing-android-embedded:4.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\53177004b6b735f0506f1ff457ffd742\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:33:5-35:36
49        android:name="android.hardware.camera.autofocus"
49-->[com.journeyapps:zxing-android-embedded:4.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\53177004b6b735f0506f1ff457ffd742\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:34:9-57
50        android:required="false" />
50-->[com.journeyapps:zxing-android-embedded:4.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\53177004b6b735f0506f1ff457ffd742\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:35:9-33
51    <uses-feature
51-->[com.journeyapps:zxing-android-embedded:4.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\53177004b6b735f0506f1ff457ffd742\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:36:5-38:36
52        android:name="android.hardware.camera.flash"
52-->[com.journeyapps:zxing-android-embedded:4.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\53177004b6b735f0506f1ff457ffd742\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:37:9-53
53        android:required="false" />
53-->[com.journeyapps:zxing-android-embedded:4.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\53177004b6b735f0506f1ff457ffd742\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:38:9-33
54    <uses-feature
54-->[com.journeyapps:zxing-android-embedded:4.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\53177004b6b735f0506f1ff457ffd742\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:39:5-41:36
55        android:name="android.hardware.screen.landscape"
55-->[com.journeyapps:zxing-android-embedded:4.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\53177004b6b735f0506f1ff457ffd742\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:40:9-57
56        android:required="false" />
56-->[com.journeyapps:zxing-android-embedded:4.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\53177004b6b735f0506f1ff457ffd742\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:41:9-33
57    <uses-feature
57-->[com.journeyapps:zxing-android-embedded:4.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\53177004b6b735f0506f1ff457ffd742\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:42:5-44:36
58        android:name="android.hardware.wifi"
58-->[com.journeyapps:zxing-android-embedded:4.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\53177004b6b735f0506f1ff457ffd742\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:43:9-45
59        android:required="false" />
59-->[com.journeyapps:zxing-android-embedded:4.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\53177004b6b735f0506f1ff457ffd742\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:44:9-33
60
61    <application
61-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:30:5-240:19
62        android:allowBackup="true"
62-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:31:9-35
63        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
63-->[androidx.core:core:1.12.0] E:\Android\GradleRepository\caches\8.11.1\transforms\cd03543d47fe14dfe0d4dd3fa7052145\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
64        android:dataExtractionRules="@xml/data_extraction_rules"
64-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:32:9-65
65        android:debuggable="true"
66        android:enableOnBackInvokedCallback="true"
66-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:41:9-51
67        android:extractNativeLibs="false"
68        android:fullBackupContent="@xml/backup_rules"
68-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:33:9-54
69        android:icon="@mipmap/ic_launcher"
69-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:34:9-43
70        android:label="@string/app_name"
70-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:35:9-41
71        android:networkSecurityConfig="@xml/network_security_config"
71-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:39:9-69
72        android:roundIcon="@mipmap/ic_launcher_round"
72-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:36:9-54
73        android:supportsRtl="true"
73-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:37:9-35
74        android:testOnly="true"
75        android:theme="@style/Theme.AIHealthButler"
75-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:38:9-52
76        android:usesCleartextTraffic="true" >
76-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:40:9-44
77
78        <!--
79        allowBackup：允许应用程序参与数据备份和恢复；
80        dataExtractionRules：XML文件，定义应用程序数据提取的规则；
81        fullBackupContent：定义应用程序全量备份的内容规则；
82        icon：设置应用程序的图标；
83        label：应用程序的显示名称；
84        roundIcon：设置应用程序的圆形图标；
85        android:usesCleartextTraffic="true"：HTTP明文流量限制
86        -->
87
88
89        <!-- 相关服务 -->
90        <provider
91            android:name="androidx.core.content.FileProvider"
91-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:56:13-62
92            android:authorities="com.example.aihealthbutler.provider"
92-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:57:13-60
93            android:exported="false"
93-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:58:13-37
94            android:grantUriPermissions="true" >
94-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:59:13-47
95            <meta-data
95-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:60:13-62:54
96                android:name="android.support.FILE_PROVIDER_PATHS"
96-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:61:17-67
97                android:resource="@xml/file_paths" />
97-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:62:17-51
98        </provider>
99
100        <!-- 登录界面为起始页 -->
101        <!-- 登录页面 -->
102        <activity
102-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:67:9-76:20
103            android:name="com.example.aihealthbutler.SignInActivity"
103-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:68:13-43
104            android:exported="true"
104-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:69:13-36
105            android:label="@string/app_name"
105-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:70:13-45
106            android:theme="@style/Theme.AIHealthButler" >
106-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:71:13-56
107            <intent-filter>
107-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:72:13-75:29
108                <action android:name="android.intent.action.MAIN" />
108-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:73:17-69
108-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:73:25-66
109
110                <category android:name="android.intent.category.LAUNCHER" />
110-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:74:17-77
110-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:74:27-74
111            </intent-filter>
112        </activity>
113
114        <!-- 对话界面 -->
115        <activity
115-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:81:9-86:20
116            android:name="com.example.aihealthbutler.ConversationInterface"
116-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:82:13-50
117            android:exported="true"
117-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:83:13-36
118            android:label="@string/app_name"
118-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:84:13-45
119            android:theme="@style/Theme.AIHealthButler" >
119-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:85:13-56
120        </activity>
121
122        <!-- &lt;!&ndash; 对话界面 &ndash;&gt; -->
123        <!-- <activity -->
124        <!-- android:name=".ConversationInterface" -->
125        <!-- android:exported="true" -->
126        <!-- android:theme="@style/Theme.AIHealthButler" /> -->
127
128
129        <!-- 我的界面为起始页 -->
130
131
132        <!-- 登录页面 -->
133        <!-- <activity -->
134        <!-- android:name=".SignInActivity" -->
135        <!-- android:exported="true" -->
136        <!-- android:theme="@style/Theme.AIHealthButler" /> -->
137
138
139        <!-- &lt;!&ndash; 我的页面 &ndash;&gt; -->
140        <!-- <activity -->
141        <!-- android:name=".MainActivity" -->
142        <!-- android:exported="true"> -->
143        <!-- <intent-filter> -->
144        <!-- <action android:name="android.intent.action.MAIN" /> -->
145        <!-- <category android:name="android.intent.category.LAUNCHER" /> -->
146        <!-- </intent-filter> -->
147        <!-- </activity> -->
148
149
150        <!-- 用户协议界面为首界面 -->
151        <!-- 用户协议界面 -->
152        <!-- <activity -->
153        <!-- android:name=".FirstUserPopupActivity" -->
154        <!-- android:exported="true"> -->
155        <!-- <intent-filter> -->
156        <!-- <action android:name="android.intent.action.MAIN" /> -->
157        <!-- <category android:name="android.intent.category.LAUNCHER" /> -->
158        <!-- </intent-filter> -->
159        <!-- </activity> -->
160
161
162        <!-- &lt;!&ndash;登录页面 &ndash;&gt; -->
163        <!-- <activity -->
164        <!-- android:name=".SignInActivity" -->
165        <!-- android:exported="true" -->
166        <!-- android:theme="@style/Theme.AIHealthButler" /> -->
167
168
169        <!-- &lt;!&ndash; 我的页面 &ndash;&gt; -->
170        <!-- <activity -->
171        <!-- android:name=".MainActivity" -->
172        <!-- android:exported="true" -->
173        <!-- android:theme="@style/Theme.AIHealthButler" /> -->
174
175
176        <!-- 我的页面 -->
177        <activity
177-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:137:9-140:59
178            android:name="com.example.aihealthbutler.MainActivity"
178-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:138:13-41
179            android:exported="true"
179-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:139:13-36
180            android:theme="@style/Theme.AIHealthButler" />
180-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:140:13-56
181
182        <!-- 用药计划页面 -->
183        <activity
183-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:143:9-146:59
184            android:name="com.example.aihealthbutler.MedicationPlanActivity"
184-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:144:13-51
185            android:exported="true"
185-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:145:13-36
186            android:theme="@style/Theme.AIHealthButler" />
186-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:146:13-56
187
188        <!-- 新增用药计划 -->
189        <activity
189-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:149:9-152:59
190            android:name="com.example.aihealthbutler.AddnewmedicationplanActivity"
190-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:150:13-57
191            android:exported="true"
191-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:151:13-36
192            android:theme="@style/Theme.AIHealthButler" />
192-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:152:13-56
193
194        <!-- 运动建议 -->
195        <activity
195-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:155:9-158:59
196            android:name="com.example.aihealthbutler.ExerciseAdviceActivity"
196-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:156:13-51
197            android:exported="true"
197-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:157:13-36
198            android:theme="@style/Theme.AIHealthButler" />
198-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:158:13-56
199
200        <!-- 饮食建议 -->
201        <activity
201-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:161:9-164:59
202            android:name="com.example.aihealthbutler.DietaryAdviceActivity"
202-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:162:13-50
203            android:exported="true"
203-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:163:13-36
204            android:theme="@style/Theme.AIHealthButler" />
204-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:164:13-56
205
206        <!-- 个人信息 -->
207        <activity
207-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:167:9-170:59
208            android:name="com.example.aihealthbutler.PersonalInformationActivity"
208-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:168:13-56
209            android:exported="true"
209-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:169:13-36
210            android:theme="@style/Theme.AIHealthButler" />
210-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:170:13-56
211
212        <!-- 新增家庭成员 -->
213        <activity
213-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:173:9-176:59
214            android:name="com.example.aihealthbutler.AddNewFamilyMember"
214-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:174:13-47
215            android:exported="true"
215-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:175:13-36
216            android:theme="@style/Theme.AIHealthButler" />
216-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:176:13-56
217
218        <!-- 资料夹主界面 -->
219        <activity
219-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:181:9-184:59
220            android:name="com.example.aihealthbutler.DocumentFolderActivity"
220-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:182:13-51
221            android:exported="true"
221-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:183:13-36
222            android:theme="@style/Theme.AIHealthButler" />
222-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:184:13-56
223
224        <!-- 上传资料 -->
225        <activity
225-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:187:9-190:59
226            android:name="com.example.aihealthbutler.UploadMaterialsActivity"
226-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:188:13-52
227            android:exported="true"
227-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:189:13-36
228            android:theme="@style/Theme.AIHealthButler" />
228-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:190:13-56
229
230        <!-- 健康史 -->
231        <activity
231-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:193:9-196:52
232            android:name="com.example.aihealthbutler.HealthHistoryActivity"
232-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:194:13-50
233            android:exported="false"
233-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:195:13-37
234            android:screenOrientation="portrait" />
234-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:196:13-49
235
236        <!-- 权限设置 -->
237        <activity
237-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:199:9-202:59
238            android:name="com.example.aihealthbutler.PermissionsManagerActivity"
238-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:200:13-55
239            android:exported="true"
239-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:201:13-36
240            android:theme="@style/Theme.AIHealthButler" />
240-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:202:13-56
241
242        <!-- 家庭成员切换 -->
243        <activity
243-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:205:9-208:59
244            android:name="com.example.aihealthbutler.FamilyMemberSwitchActivity"
244-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:206:13-55
245            android:exported="true"
245-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:207:13-36
246            android:theme="@style/Theme.AIHealthButler" />
246-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:208:13-56
247
248        <!-- 邀请二维码生成 -->
249        <activity
249-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:211:9-214:59
250            android:name="com.example.aihealthbutler.InviteQRCodeActivity"
250-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:212:13-49
251            android:exported="true"
251-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:213:13-36
252            android:theme="@style/Theme.AIHealthButler" />
252-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:214:13-56
253
254        <!-- 二维码扫描 -->
255        <activity
255-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:217:9-220:59
256            android:name="com.example.aihealthbutler.QRScannerActivity"
256-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:218:13-46
257            android:exported="true"
257-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:219:13-36
258            android:theme="@style/Theme.AIHealthButler" />
258-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:220:13-56
259
260        <!-- 添加成员确认 -->
261        <activity
261-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:223:9-226:59
262            android:name="com.example.aihealthbutler.AddMemberConfirmActivity"
262-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:224:13-53
263            android:exported="true"
263-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:225:13-36
264            android:theme="@style/Theme.AIHealthButler" />
264-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:226:13-56
265
266        <!-- 关系设置 -->
267        <activity
267-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:229:9-232:59
268            android:name="com.example.aihealthbutler.RelationshipSettingActivity"
268-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:230:13-56
269            android:exported="true"
269-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:231:13-36
270            android:theme="@style/Theme.AIHealthButler" />
270-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:232:13-56
271
272        <!-- 网络调试工具 -->
273        <activity
273-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:235:9-238:59
274            android:name="com.example.aihealthbutler.debug.NetworkDebugActivity"
274-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:236:13-55
275            android:exported="false"
275-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:237:13-37
276            android:theme="@style/Theme.AIHealthButler" />
276-->E:\AIHealthButler\app\src\main\AndroidManifest.xml:238:13-56
277        <activity
277-->[androidx.compose.ui:ui-tooling-android:1.6.0] E:\Android\GradleRepository\caches\8.11.1\transforms\85f7238b6a16c9bd9d8a581b064da59f\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
278            android:name="androidx.compose.ui.tooling.PreviewActivity"
278-->[androidx.compose.ui:ui-tooling-android:1.6.0] E:\Android\GradleRepository\caches\8.11.1\transforms\85f7238b6a16c9bd9d8a581b064da59f\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
279            android:exported="true" />
279-->[androidx.compose.ui:ui-tooling-android:1.6.0] E:\Android\GradleRepository\caches\8.11.1\transforms\85f7238b6a16c9bd9d8a581b064da59f\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
280
281        <service
281-->[androidx.camera:camera-camera2:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\c314f6fd8963a93598e7e827dca44cf2\transformed\camera-camera2-1.3.1\AndroidManifest.xml:24:9-33:19
282            android:name="androidx.camera.core.impl.MetadataHolderService"
282-->[androidx.camera:camera-camera2:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\c314f6fd8963a93598e7e827dca44cf2\transformed\camera-camera2-1.3.1\AndroidManifest.xml:25:13-75
283            android:enabled="false"
283-->[androidx.camera:camera-camera2:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\c314f6fd8963a93598e7e827dca44cf2\transformed\camera-camera2-1.3.1\AndroidManifest.xml:26:13-36
284            android:exported="false" >
284-->[androidx.camera:camera-camera2:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\c314f6fd8963a93598e7e827dca44cf2\transformed\camera-camera2-1.3.1\AndroidManifest.xml:27:13-37
285            <meta-data
285-->[androidx.camera:camera-camera2:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\c314f6fd8963a93598e7e827dca44cf2\transformed\camera-camera2-1.3.1\AndroidManifest.xml:30:13-32:89
286                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
286-->[androidx.camera:camera-camera2:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\c314f6fd8963a93598e7e827dca44cf2\transformed\camera-camera2-1.3.1\AndroidManifest.xml:31:17-103
287                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
287-->[androidx.camera:camera-camera2:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\c314f6fd8963a93598e7e827dca44cf2\transformed\camera-camera2-1.3.1\AndroidManifest.xml:32:17-86
288        </service>
289        <service
289-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\f3d7a0950d8edd522d3a2e68917e9fd0\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:9:9-15:19
290            android:name="com.google.mlkit.common.internal.MlKitComponentDiscoveryService"
290-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\f3d7a0950d8edd522d3a2e68917e9fd0\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:10:13-91
291            android:directBootAware="true"
291-->[com.google.mlkit:common:18.9.0] E:\Android\GradleRepository\caches\8.11.1\transforms\856b4a7a015be168cc7fc8a4d2056f38\transformed\common-18.9.0\AndroidManifest.xml:17:13-43
292            android:exported="false" >
292-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\f3d7a0950d8edd522d3a2e68917e9fd0\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:11:13-37
293            <meta-data
293-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\f3d7a0950d8edd522d3a2e68917e9fd0\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:12:13-14:85
294                android:name="com.google.firebase.components:com.google.mlkit.vision.barcode.internal.BarcodeRegistrar"
294-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\f3d7a0950d8edd522d3a2e68917e9fd0\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:13:17-120
295                android:value="com.google.firebase.components.ComponentRegistrar" />
295-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\f3d7a0950d8edd522d3a2e68917e9fd0\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:14:17-82
296            <meta-data
296-->[com.google.mlkit:vision-common:17.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\8c7116a2e8c72f5558ec46ba66165641\transformed\vision-common-17.3.0\AndroidManifest.xml:12:13-14:85
297                android:name="com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar"
297-->[com.google.mlkit:vision-common:17.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\8c7116a2e8c72f5558ec46ba66165641\transformed\vision-common-17.3.0\AndroidManifest.xml:13:17-124
298                android:value="com.google.firebase.components.ComponentRegistrar" />
298-->[com.google.mlkit:vision-common:17.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\8c7116a2e8c72f5558ec46ba66165641\transformed\vision-common-17.3.0\AndroidManifest.xml:14:17-82
299            <meta-data
299-->[com.google.mlkit:common:18.9.0] E:\Android\GradleRepository\caches\8.11.1\transforms\856b4a7a015be168cc7fc8a4d2056f38\transformed\common-18.9.0\AndroidManifest.xml:20:13-22:85
300                android:name="com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar"
300-->[com.google.mlkit:common:18.9.0] E:\Android\GradleRepository\caches\8.11.1\transforms\856b4a7a015be168cc7fc8a4d2056f38\transformed\common-18.9.0\AndroidManifest.xml:21:17-120
301                android:value="com.google.firebase.components.ComponentRegistrar" />
301-->[com.google.mlkit:common:18.9.0] E:\Android\GradleRepository\caches\8.11.1\transforms\856b4a7a015be168cc7fc8a4d2056f38\transformed\common-18.9.0\AndroidManifest.xml:22:17-82
302        </service>
303
304        <provider
304-->[com.google.mlkit:common:18.9.0] E:\Android\GradleRepository\caches\8.11.1\transforms\856b4a7a015be168cc7fc8a4d2056f38\transformed\common-18.9.0\AndroidManifest.xml:9:9-13:38
305            android:name="com.google.mlkit.common.internal.MlKitInitProvider"
305-->[com.google.mlkit:common:18.9.0] E:\Android\GradleRepository\caches\8.11.1\transforms\856b4a7a015be168cc7fc8a4d2056f38\transformed\common-18.9.0\AndroidManifest.xml:10:13-78
306            android:authorities="com.example.aihealthbutler.mlkitinitprovider"
306-->[com.google.mlkit:common:18.9.0] E:\Android\GradleRepository\caches\8.11.1\transforms\856b4a7a015be168cc7fc8a4d2056f38\transformed\common-18.9.0\AndroidManifest.xml:11:13-69
307            android:exported="false"
307-->[com.google.mlkit:common:18.9.0] E:\Android\GradleRepository\caches\8.11.1\transforms\856b4a7a015be168cc7fc8a4d2056f38\transformed\common-18.9.0\AndroidManifest.xml:12:13-37
308            android:initOrder="99" />
308-->[com.google.mlkit:common:18.9.0] E:\Android\GradleRepository\caches\8.11.1\transforms\856b4a7a015be168cc7fc8a4d2056f38\transformed\common-18.9.0\AndroidManifest.xml:13:13-35
309
310        <activity
310-->[com.google.android.gms:play-services-base:18.1.0] E:\Android\GradleRepository\caches\8.11.1\transforms\ea1d6e38040e7b8436c098f4eca33857\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
311            android:name="com.google.android.gms.common.api.GoogleApiActivity"
311-->[com.google.android.gms:play-services-base:18.1.0] E:\Android\GradleRepository\caches\8.11.1\transforms\ea1d6e38040e7b8436c098f4eca33857\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:19-85
312            android:exported="false"
312-->[com.google.android.gms:play-services-base:18.1.0] E:\Android\GradleRepository\caches\8.11.1\transforms\ea1d6e38040e7b8436c098f4eca33857\transformed\play-services-base-18.1.0\AndroidManifest.xml:22:19-43
313            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
313-->[com.google.android.gms:play-services-base:18.1.0] E:\Android\GradleRepository\caches\8.11.1\transforms\ea1d6e38040e7b8436c098f4eca33857\transformed\play-services-base-18.1.0\AndroidManifest.xml:21:19-78
314
315        <meta-data
315-->[com.google.android.gms:play-services-basement:18.1.0] E:\Android\GradleRepository\caches\8.11.1\transforms\237eff3a41ee89ac3ef69d6430961f46\transformed\play-services-basement-18.1.0\AndroidManifest.xml:21:9-23:69
316            android:name="com.google.android.gms.version"
316-->[com.google.android.gms:play-services-basement:18.1.0] E:\Android\GradleRepository\caches\8.11.1\transforms\237eff3a41ee89ac3ef69d6430961f46\transformed\play-services-basement-18.1.0\AndroidManifest.xml:22:13-58
317            android:value="@integer/google_play_services_version" />
317-->[com.google.android.gms:play-services-basement:18.1.0] E:\Android\GradleRepository\caches\8.11.1\transforms\237eff3a41ee89ac3ef69d6430961f46\transformed\play-services-basement-18.1.0\AndroidManifest.xml:23:13-66
318
319        <activity
319-->[androidx.compose.ui:ui-test-manifest:1.6.0] E:\Android\GradleRepository\caches\8.11.1\transforms\d876ee35e176acdeab4ede638c85e11d\transformed\ui-test-manifest-1.6.0\AndroidManifest.xml:23:9-25:39
320            android:name="androidx.activity.ComponentActivity"
320-->[androidx.compose.ui:ui-test-manifest:1.6.0] E:\Android\GradleRepository\caches\8.11.1\transforms\d876ee35e176acdeab4ede638c85e11d\transformed\ui-test-manifest-1.6.0\AndroidManifest.xml:24:13-63
321            android:exported="true" />
321-->[androidx.compose.ui:ui-test-manifest:1.6.0] E:\Android\GradleRepository\caches\8.11.1\transforms\d876ee35e176acdeab4ede638c85e11d\transformed\ui-test-manifest-1.6.0\AndroidManifest.xml:25:13-36
322
323        <provider
323-->[androidx.emoji2:emoji2:1.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\55d31107e6254ca36703c0e778ec1289\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
324            android:name="androidx.startup.InitializationProvider"
324-->[androidx.emoji2:emoji2:1.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\55d31107e6254ca36703c0e778ec1289\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
325            android:authorities="com.example.aihealthbutler.androidx-startup"
325-->[androidx.emoji2:emoji2:1.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\55d31107e6254ca36703c0e778ec1289\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
326            android:exported="false" >
326-->[androidx.emoji2:emoji2:1.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\55d31107e6254ca36703c0e778ec1289\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
327            <meta-data
327-->[androidx.emoji2:emoji2:1.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\55d31107e6254ca36703c0e778ec1289\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
328                android:name="androidx.emoji2.text.EmojiCompatInitializer"
328-->[androidx.emoji2:emoji2:1.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\55d31107e6254ca36703c0e778ec1289\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
329                android:value="androidx.startup" />
329-->[androidx.emoji2:emoji2:1.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\55d31107e6254ca36703c0e778ec1289\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
330            <meta-data
330-->[androidx.lifecycle:lifecycle-process:2.7.0] E:\Android\GradleRepository\caches\8.11.1\transforms\0fa4b7e7a7fef6254fbd677efdefa57e\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
331                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
331-->[androidx.lifecycle:lifecycle-process:2.7.0] E:\Android\GradleRepository\caches\8.11.1\transforms\0fa4b7e7a7fef6254fbd677efdefa57e\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
332                android:value="androidx.startup" />
332-->[androidx.lifecycle:lifecycle-process:2.7.0] E:\Android\GradleRepository\caches\8.11.1\transforms\0fa4b7e7a7fef6254fbd677efdefa57e\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
333            <meta-data
333-->[androidx.profileinstaller:profileinstaller:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\1f9356cc7725a92deea72a3b5f9be356\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
334                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
334-->[androidx.profileinstaller:profileinstaller:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\1f9356cc7725a92deea72a3b5f9be356\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
335                android:value="androidx.startup" />
335-->[androidx.profileinstaller:profileinstaller:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\1f9356cc7725a92deea72a3b5f9be356\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
336        </provider>
337
338        <uses-library
338-->[androidx.window:window:1.0.0] E:\Android\GradleRepository\caches\8.11.1\transforms\56ac8c8b02471706ed6c589469cdd72e\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
339            android:name="androidx.window.extensions"
339-->[androidx.window:window:1.0.0] E:\Android\GradleRepository\caches\8.11.1\transforms\56ac8c8b02471706ed6c589469cdd72e\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
340            android:required="false" />
340-->[androidx.window:window:1.0.0] E:\Android\GradleRepository\caches\8.11.1\transforms\56ac8c8b02471706ed6c589469cdd72e\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
341        <uses-library
341-->[androidx.window:window:1.0.0] E:\Android\GradleRepository\caches\8.11.1\transforms\56ac8c8b02471706ed6c589469cdd72e\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
342            android:name="androidx.window.sidecar"
342-->[androidx.window:window:1.0.0] E:\Android\GradleRepository\caches\8.11.1\transforms\56ac8c8b02471706ed6c589469cdd72e\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
343            android:required="false" />
343-->[androidx.window:window:1.0.0] E:\Android\GradleRepository\caches\8.11.1\transforms\56ac8c8b02471706ed6c589469cdd72e\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
344
345        <receiver
345-->[androidx.profileinstaller:profileinstaller:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\1f9356cc7725a92deea72a3b5f9be356\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
346            android:name="androidx.profileinstaller.ProfileInstallReceiver"
346-->[androidx.profileinstaller:profileinstaller:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\1f9356cc7725a92deea72a3b5f9be356\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
347            android:directBootAware="false"
347-->[androidx.profileinstaller:profileinstaller:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\1f9356cc7725a92deea72a3b5f9be356\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
348            android:enabled="true"
348-->[androidx.profileinstaller:profileinstaller:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\1f9356cc7725a92deea72a3b5f9be356\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
349            android:exported="true"
349-->[androidx.profileinstaller:profileinstaller:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\1f9356cc7725a92deea72a3b5f9be356\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
350            android:permission="android.permission.DUMP" >
350-->[androidx.profileinstaller:profileinstaller:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\1f9356cc7725a92deea72a3b5f9be356\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
351            <intent-filter>
351-->[androidx.profileinstaller:profileinstaller:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\1f9356cc7725a92deea72a3b5f9be356\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
352                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
352-->[androidx.profileinstaller:profileinstaller:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\1f9356cc7725a92deea72a3b5f9be356\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
352-->[androidx.profileinstaller:profileinstaller:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\1f9356cc7725a92deea72a3b5f9be356\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
353            </intent-filter>
354            <intent-filter>
354-->[androidx.profileinstaller:profileinstaller:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\1f9356cc7725a92deea72a3b5f9be356\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
355                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
355-->[androidx.profileinstaller:profileinstaller:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\1f9356cc7725a92deea72a3b5f9be356\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
355-->[androidx.profileinstaller:profileinstaller:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\1f9356cc7725a92deea72a3b5f9be356\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
356            </intent-filter>
357            <intent-filter>
357-->[androidx.profileinstaller:profileinstaller:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\1f9356cc7725a92deea72a3b5f9be356\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
358                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
358-->[androidx.profileinstaller:profileinstaller:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\1f9356cc7725a92deea72a3b5f9be356\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
358-->[androidx.profileinstaller:profileinstaller:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\1f9356cc7725a92deea72a3b5f9be356\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
359            </intent-filter>
360            <intent-filter>
360-->[androidx.profileinstaller:profileinstaller:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\1f9356cc7725a92deea72a3b5f9be356\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
361                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
361-->[androidx.profileinstaller:profileinstaller:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\1f9356cc7725a92deea72a3b5f9be356\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
361-->[androidx.profileinstaller:profileinstaller:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\1f9356cc7725a92deea72a3b5f9be356\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
362            </intent-filter>
363        </receiver>
364
365        <service
365-->[com.google.android.datatransport:transport-backend-cct:2.3.3] E:\Android\GradleRepository\caches\8.11.1\transforms\e1b06c4bf7aa3bfb49d4614b0cbceabc\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:29:9-35:19
366            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
366-->[com.google.android.datatransport:transport-backend-cct:2.3.3] E:\Android\GradleRepository\caches\8.11.1\transforms\e1b06c4bf7aa3bfb49d4614b0cbceabc\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:30:13-103
367            android:exported="false" >
367-->[com.google.android.datatransport:transport-backend-cct:2.3.3] E:\Android\GradleRepository\caches\8.11.1\transforms\e1b06c4bf7aa3bfb49d4614b0cbceabc\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:31:13-37
368            <meta-data
368-->[com.google.android.datatransport:transport-backend-cct:2.3.3] E:\Android\GradleRepository\caches\8.11.1\transforms\e1b06c4bf7aa3bfb49d4614b0cbceabc\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:32:13-34:39
369                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
369-->[com.google.android.datatransport:transport-backend-cct:2.3.3] E:\Android\GradleRepository\caches\8.11.1\transforms\e1b06c4bf7aa3bfb49d4614b0cbceabc\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:33:17-94
370                android:value="cct" />
370-->[com.google.android.datatransport:transport-backend-cct:2.3.3] E:\Android\GradleRepository\caches\8.11.1\transforms\e1b06c4bf7aa3bfb49d4614b0cbceabc\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:34:17-36
371        </service>
372        <service
372-->[com.google.android.datatransport:transport-runtime:2.2.6] E:\Android\GradleRepository\caches\8.11.1\transforms\aaa1dbd1c9bbb60e869a4dfb4b371a5b\transformed\transport-runtime-2.2.6\AndroidManifest.xml:26:9-30:19
373            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
373-->[com.google.android.datatransport:transport-runtime:2.2.6] E:\Android\GradleRepository\caches\8.11.1\transforms\aaa1dbd1c9bbb60e869a4dfb4b371a5b\transformed\transport-runtime-2.2.6\AndroidManifest.xml:27:13-117
374            android:exported="false"
374-->[com.google.android.datatransport:transport-runtime:2.2.6] E:\Android\GradleRepository\caches\8.11.1\transforms\aaa1dbd1c9bbb60e869a4dfb4b371a5b\transformed\transport-runtime-2.2.6\AndroidManifest.xml:28:13-37
375            android:permission="android.permission.BIND_JOB_SERVICE" >
375-->[com.google.android.datatransport:transport-runtime:2.2.6] E:\Android\GradleRepository\caches\8.11.1\transforms\aaa1dbd1c9bbb60e869a4dfb4b371a5b\transformed\transport-runtime-2.2.6\AndroidManifest.xml:29:13-69
376        </service>
377
378        <receiver
378-->[com.google.android.datatransport:transport-runtime:2.2.6] E:\Android\GradleRepository\caches\8.11.1\transforms\aaa1dbd1c9bbb60e869a4dfb4b371a5b\transformed\transport-runtime-2.2.6\AndroidManifest.xml:32:9-34:40
379            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
379-->[com.google.android.datatransport:transport-runtime:2.2.6] E:\Android\GradleRepository\caches\8.11.1\transforms\aaa1dbd1c9bbb60e869a4dfb4b371a5b\transformed\transport-runtime-2.2.6\AndroidManifest.xml:33:13-132
380            android:exported="false" />
380-->[com.google.android.datatransport:transport-runtime:2.2.6] E:\Android\GradleRepository\caches\8.11.1\transforms\aaa1dbd1c9bbb60e869a4dfb4b371a5b\transformed\transport-runtime-2.2.6\AndroidManifest.xml:34:13-37
381
382        <activity
382-->[com.journeyapps:zxing-android-embedded:4.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\53177004b6b735f0506f1ff457ffd742\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:47:9-53:63
383            android:name="com.journeyapps.barcodescanner.CaptureActivity"
383-->[com.journeyapps:zxing-android-embedded:4.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\53177004b6b735f0506f1ff457ffd742\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:48:13-74
384            android:clearTaskOnLaunch="true"
384-->[com.journeyapps:zxing-android-embedded:4.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\53177004b6b735f0506f1ff457ffd742\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:49:13-45
385            android:screenOrientation="sensorLandscape"
385-->[com.journeyapps:zxing-android-embedded:4.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\53177004b6b735f0506f1ff457ffd742\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:50:13-56
386            android:stateNotNeeded="true"
386-->[com.journeyapps:zxing-android-embedded:4.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\53177004b6b735f0506f1ff457ffd742\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:51:13-42
387            android:theme="@style/zxing_CaptureTheme"
387-->[com.journeyapps:zxing-android-embedded:4.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\53177004b6b735f0506f1ff457ffd742\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:52:13-54
388            android:windowSoftInputMode="stateAlwaysHidden" />
388-->[com.journeyapps:zxing-android-embedded:4.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\53177004b6b735f0506f1ff457ffd742\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:53:13-60
389    </application>
390
391</manifest>
