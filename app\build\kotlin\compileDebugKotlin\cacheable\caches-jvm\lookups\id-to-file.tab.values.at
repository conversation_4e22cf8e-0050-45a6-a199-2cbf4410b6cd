/ Header Record For PersistentHashMapValueStorageI Happ/src/main/java/com/example/aihealthbutler/AddMemberConfirmActivity.ktC Bapp/src/main/java/com/example/aihealthbutler/AddNewFamilyMember.ktE Dapp/src/main/java/com/example/aihealthbutler/Addnewmedicationplan.ktE Dapp/src/main/java/com/example/aihealthbutler/AudioRecorderManager.ktF Eapp/src/main/java/com/example/aihealthbutler/ConversationInterface.ktF Eapp/src/main/java/com/example/aihealthbutler/DietaryAdviceActivity.ktG Fapp/src/main/java/com/example/aihealthbutler/DocumentFolderActivity.ktI Happ/src/main/java/com/example/aihealthbutler/EditFamilyMemberActivity.ktG Fapp/src/main/java/com/example/aihealthbutler/ExerciseAdviceActivity.ktK Japp/src/main/java/com/example/aihealthbutler/FamilyMemberSwitchActivity.ktG Fapp/src/main/java/com/example/aihealthbutler/FirstUserPopupActivity.ktF Eapp/src/main/java/com/example/aihealthbutler/HealthHistoryActivity.ktE Dapp/src/main/java/com/example/aihealthbutler/InviteQRCodeActivity.kt= <app/src/main/java/com/example/aihealthbutler/MainActivity.ktG Fapp/src/main/java/com/example/aihealthbutler/MedicationPlanActivity.ktK Japp/src/main/java/com/example/aihealthbutler/PermissionsManagerActivity.ktL Kapp/src/main/java/com/example/aihealthbutler/PersonalInformationActivity.ktB Aapp/src/main/java/com/example/aihealthbutler/QRScannerActivity.ktL Kapp/src/main/java/com/example/aihealthbutler/RelationshipSettingActivity.kt? >app/src/main/java/com/example/aihealthbutler/SignInActivity.ktL Kapp/src/main/java/com/example/aihealthbutler/SpeechEngineSelectionDialog.ktD Capp/src/main/java/com/example/aihealthbutler/SpeechToTextManager.kt? >app/src/main/java/com/example/aihealthbutler/SpeechToTextUI.ktH Gapp/src/main/java/com/example/aihealthbutler/UploadMaterialsActivity.ktF Eapp/src/main/java/com/example/aihealthbutler/VoiceRecordingExample.ktF Eapp/src/main/java/com/example/aihealthbutler/VoiceRecordingOverlay.kt> =app/src/main/java/com/example/aihealthbutler/api/ApiClient.kt> =app/src/main/java/com/example/aihealthbutler/api/ApiModels.kt? >app/src/main/java/com/example/aihealthbutler/api/ApiService.ktA @app/src/main/java/com/example/aihealthbutler/api/FolderModels.kt? >app/src/main/java/com/example/aihealthbutler/api/Repository.ktC Bapp/src/main/java/com/example/aihealthbutler/api/SessionManager.ktE Dapp/src/main/java/com/example/aihealthbutler/camera/CameraPreview.ktK Japp/src/main/java/com/example/aihealthbutler/debug/NetworkDebugActivity.ktC Bapp/src/main/java/com/example/aihealthbutler/dify/DifyApiClient.ktC Bapp/src/main/java/com/example/aihealthbutler/dify/DifyApiModels.kt@ ?app/src/main/java/com/example/aihealthbutler/dify/DifyConfig.ktE Dapp/src/main/java/com/example/aihealthbutler/dify/DifyFileManager.ktG Fapp/src/main/java/com/example/aihealthbutler/dify/DifyStreamHandler.ktC Bapp/src/main/java/com/example/aihealthbutler/qr/QRCodeGenerator.kt@ ?app/src/main/java/com/example/aihealthbutler/qr/QRCodeModels.ktE Dapp/src/main/java/com/example/aihealthbutler/qr/SimpleQRGenerator.ktK Japp/src/main/java/com/example/aihealthbutler/speech/SpeechEngineManager.ktO Napp/src/main/java/com/example/aihealthbutler/speech/SpeechRecognitionEngine.ktH Gapp/src/main/java/com/example/aihealthbutler/speech/VoskSpeechEngine.ktJ Iapp/src/main/java/com/example/aihealthbutler/speech/XunfeiSpeechEngine.ktG Fapp/src/main/java/com/example/aihealthbutler/ui/AudioToTextActivity.ktL Kapp/src/main/java/com/example/aihealthbutler/ui/components/CustomDialogs.ktQ Papp/src/main/java/com/example/aihealthbutler/ui/components/DialogUsageExample.ktS Rapp/src/main/java/com/example/aihealthbutler/ui/components/OppoPermissionDialog.kt? >app/src/main/java/com/example/aihealthbutler/ui/theme/Color.kt? >app/src/main/java/com/example/aihealthbutler/ui/theme/Theme.kt> =app/src/main/java/com/example/aihealthbutler/ui/theme/Type.ktH Gapp/src/main/java/com/example/aihealthbutler/utils/AudioToTextHelper.ktM Lapp/src/main/java/com/example/aihealthbutler/utils/ConnectionResetHandler.kt@ ?app/src/main/java/com/example/aihealthbutler/utils/CustomDns.ktA @app/src/main/java/com/example/aihealthbutler/utils/DebugUtils.ktK Japp/src/main/java/com/example/aihealthbutler/utils/DifyConnectionTester.ktF Eapp/src/main/java/com/example/aihealthbutler/utils/ErrorReportUtil.ktO Napp/src/main/java/com/example/aihealthbutler/utils/FamilyRelationshipHelper.ktH Gapp/src/main/java/com/example/aihealthbutler/utils/HttpsConfigHelper.ktH Gapp/src/main/java/com/example/aihealthbutler/utils/ImageCacheManager.ktG Fapp/src/main/java/com/example/aihealthbutler/utils/NetworkDebugUtil.ktC Bapp/src/main/java/com/example/aihealthbutler/utils/NetworkUtils.ktK Japp/src/main/java/com/example/aihealthbutler/utils/OppoPermissionHelper.ktK Japp/src/main/java/com/example/aihealthbutler/FamilyMemberSwitchActivity.ktF Eapp/src/main/java/com/example/aihealthbutler/HealthHistoryActivity.kt? >app/src/main/java/com/example/aihealthbutler/SignInActivity.ktC Bapp/src/main/java/com/example/aihealthbutler/dify/DifyApiClient.kt@ ?app/src/main/java/com/example/aihealthbutler/dify/DifyConfig.ktF Eapp/src/main/java/com/example/aihealthbutler/ConversationInterface.ktK Japp/src/main/java/com/example/aihealthbutler/debug/NetworkDebugActivity.ktH Gapp/src/main/java/com/example/aihealthbutler/utils/AudioToTextHelper.ktK Japp/src/main/java/com/example/aihealthbutler/utils/DifyConnectionTester.ktC Bapp/src/main/java/com/example/aihealthbutler/dify/DifyApiClient.kt@ ?app/src/main/java/com/example/aihealthbutler/dify/DifyConfig.ktF Eapp/src/main/java/com/example/aihealthbutler/ConversationInterface.ktK Japp/src/main/java/com/example/aihealthbutler/debug/NetworkDebugActivity.ktH Gapp/src/main/java/com/example/aihealthbutler/utils/AudioToTextHelper.ktK Japp/src/main/java/com/example/aihealthbutler/utils/DifyConnectionTester.ktF Eapp/src/main/java/com/example/aihealthbutler/ConversationInterface.ktF Eapp/src/main/java/com/example/aihealthbutler/ConversationInterface.ktF Eapp/src/main/java/com/example/aihealthbutler/ConversationInterface.kt