-- Merging decision tree log ---
provider#androidx.core.content.FileProvider
INJECTED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:55:9-63:20
	android:grantUriPermissions
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:59:13-47
	android:authorities
		INJECTED from E:\AIHealthButler\app\src\main\AndroidManifest.xml
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:57:13-60
	android:exported
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:58:13-37
	android:name
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:56:13-62
manifest
ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:2:1-241:12
INJECTED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:2:1-241:12
INJECTED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:2:1-241:12
INJECTED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:2:1-241:12
MERGED from [androidx.compose.material3:material3-window-size-class-android:1.2.0] E:\Android\GradleRepository\caches\8.11.1\transforms\2f41c2bf5cd93f347fc35cf9bdb9d91d\transformed\material3-window-size-class-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material3:material3-android:1.2.0] E:\Android\GradleRepository\caches\8.11.1\transforms\86b5dfd63cec3973e80a6022d540b3e9\transformed\material3-release\AndroidManifest.xml:17:1-22:12
MERGED from [io.coil-kt:coil-compose:2.4.0] E:\Android\GradleRepository\caches\8.11.1\transforms\ab21ea38002e4144992f12c17e998733\transformed\coil-compose-2.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil-compose-base:2.4.0] E:\Android\GradleRepository\caches\8.11.1\transforms\e73c396c31ea48760386989516db2619\transformed\coil-compose-base-2.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.0] E:\Android\GradleRepository\caches\8.11.1\transforms\85f7238b6a16c9bd9d8a581b064da59f\transformed\ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.material:material-android:1.6.0] E:\Android\GradleRepository\caches\8.11.1\transforms\21a835513d52cc852e2ae5ad4f6a8015\transformed\material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.6.0] E:\Android\GradleRepository\caches\8.11.1\transforms\98b438f8eae45236225ffffa180286a6\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.6.0] E:\Android\GradleRepository\caches\8.11.1\transforms\9e816d3c7fd7d539922913409377f4ef\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-video:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\d0248a544f037ae1e986415bab63cbe3\transformed\camera-video-1.3.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-lifecycle:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\aebfb8c2a99005efac30e07324da7ade\transformed\camera-lifecycle-1.3.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-camera2:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\c314f6fd8963a93598e7e827dca44cf2\transformed\camera-camera2-1.3.1\AndroidManifest.xml:17:1-36:12
MERGED from [androidx.camera:camera-core:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\3e370a08e3987525bf1b894c2d626a6f\transformed\camera-core-1.3.1\AndroidManifest.xml:17:1-36:12
MERGED from [androidx.camera:camera-view:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\0e64989f53416def46aa18b9e7aae563\transformed\camera-view-1.3.1\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil:2.4.0] E:\Android\GradleRepository\caches\8.11.1\transforms\026a1795aa1152f40d5fcb9fca71aaa0\transformed\coil-2.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil-base:2.4.0] E:\Android\GradleRepository\caches\8.11.1\transforms\89c55e4f46ae88f012b8f8e6dba3d043\transformed\coil-base-2.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] E:\Android\GradleRepository\caches\8.11.1\transforms\47ecf685396a9221b9a09834661916ac\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] E:\Android\GradleRepository\caches\8.11.1\transforms\98923f9ee7dfb9b0a26244e15c6569b0\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.mlkit:barcode-scanning:17.2.0] E:\Android\GradleRepository\caches\8.11.1\transforms\aa58d2b0e946be5f142c74110d1e6a5a\transformed\barcode-scanning-17.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\f3d7a0950d8edd522d3a2e68917e9fd0\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.mlkit:barcode-scanning-common:17.0.0] E:\Android\GradleRepository\caches\8.11.1\transforms\a9ad2c77aac517e1ef17ede1c543cfce\transformed\barcode-scanning-common-17.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.mlkit:vision-common:17.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\8c7116a2e8c72f5558ec46ba66165641\transformed\vision-common-17.3.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.mlkit:common:18.9.0] E:\Android\GradleRepository\caches\8.11.1\transforms\856b4a7a015be168cc7fc8a4d2056f38\transformed\common-18.9.0\AndroidManifest.xml:2:1-26:12
MERGED from [com.google.android.gms:play-services-base:18.1.0] E:\Android\GradleRepository\caches\8.11.1\transforms\ea1d6e38040e7b8436c098f4eca33857\transformed\play-services-base-18.1.0\AndroidManifest.xml:16:1-24:12
MERGED from [com.google.mlkit:vision-interfaces:16.2.0] E:\Android\GradleRepository\caches\8.11.1\transforms\e183da1785eac375f2b9348a7155748b\transformed\vision-interfaces-16.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] E:\Android\GradleRepository\caches\8.11.1\transforms\0f69453f5e0116ca1e270fa5e55fd93b\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:2:1-6:12
MERGED from [com.google.android.gms:play-services-basement:18.1.0] E:\Android\GradleRepository\caches\8.11.1\transforms\237eff3a41ee89ac3ef69d6430961f46\transformed\play-services-basement-18.1.0\AndroidManifest.xml:16:1-26:12
MERGED from [androidx.fragment:fragment:1.3.6] E:\Android\GradleRepository\caches\8.11.1\transforms\3c679ce1ad26295b627835c89519c1ab\transformed\fragment-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.compose.animation:animation-android:1.6.0] E:\Android\GradleRepository\caches\8.11.1\transforms\7b0c59989fe9f7830317d13fe9fe3848\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.0] E:\Android\GradleRepository\caches\8.11.1\transforms\9db548b2bbe19e65621ab49498ad5d00\transformed\ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.0] E:\Android\GradleRepository\caches\8.11.1\transforms\cc030595ea9277977bffb5fe2942e414\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.0] E:\Android\GradleRepository\caches\8.11.1\transforms\eafe6cbe8efa2ee047ddd5a1b58b1258\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-unit-android:1.6.0] E:\Android\GradleRepository\caches\8.11.1\transforms\5af9735ae5b86d8631afc671c6ddfbaa\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.0] E:\Android\GradleRepository\caches\8.11.1\transforms\d876ee35e176acdeab4ede638c85e11d\transformed\ui-test-manifest-1.6.0\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.activity:activity:1.8.2] E:\Android\GradleRepository\caches\8.11.1\transforms\1fea433c97f471a9137ce06f998eda68\transformed\activity-1.8.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.8.2] E:\Android\GradleRepository\caches\8.11.1\transforms\82688ef9838c3451823a067ea0e59854\transformed\activity-ktx-1.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.8.2] E:\Android\GradleRepository\caches\8.11.1\transforms\410c4ce1fea79cc790f161d4d76790a0\transformed\activity-compose-1.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.loader:loader:1.0.0] E:\Android\GradleRepository\caches\8.11.1\transforms\87a477057b42ef90f12fce8fc68a7f8f\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\26ac74328117b1bd14521c851a36bdd2\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\55d31107e6254ca36703c0e778ec1289\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] E:\Android\GradleRepository\caches\8.11.1\transforms\0fa4b7e7a7fef6254fbd677efdefa57e\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] E:\Android\GradleRepository\caches\8.11.1\transforms\e96073b33bb9c6136af0c75c0aa73fe6\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] E:\Android\GradleRepository\caches\8.11.1\transforms\cbf2104000376f0a577b8de602bc5818\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] E:\Android\GradleRepository\caches\8.11.1\transforms\d20f7d67e940eaf1fd21d12ddeb5bd7d\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] E:\Android\GradleRepository\caches\8.11.1\transforms\43bea879fddc0a79466f9a9d6f3a0bd9\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] E:\Android\GradleRepository\caches\8.11.1\transforms\8365898dd6622c2a175015ae80ab25e3\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] E:\Android\GradleRepository\caches\8.11.1\transforms\d059301b8dfb6a0c2f94ff3d200123a6\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] E:\Android\GradleRepository\caches\8.11.1\transforms\d6a0b7cff17faf686184fbf83b9c11b9\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] E:\Android\GradleRepository\caches\8.11.1\transforms\94836782e804062b52bd9a05d3e2d56e\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer:1.2.1] E:\Android\GradleRepository\caches\8.11.1\transforms\967a26396f59d47749511e6680b66c88\transformed\media3-exoplayer-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-extractor:1.2.1] E:\Android\GradleRepository\caches\8.11.1\transforms\2725db465e8b9b28fc341b348a3e7786\transformed\media3-extractor-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-container:1.2.1] E:\Android\GradleRepository\caches\8.11.1\transforms\32527f2999eabc806cadcf90305de024\transformed\media3-container-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-datasource:1.2.1] E:\Android\GradleRepository\caches\8.11.1\transforms\5489a46b3e5d5cc00f637d06b62f8852\transformed\media3-datasource-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-decoder:1.2.1] E:\Android\GradleRepository\caches\8.11.1\transforms\5cc4dacbfb9646b932ebba880cdd3c6a\transformed\media3-decoder-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-database:1.2.1] E:\Android\GradleRepository\caches\8.11.1\transforms\e19550e485f474c20e5db05740a1de7b\transformed\media3-database-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-common:1.2.1] E:\Android\GradleRepository\caches\8.11.1\transforms\1a76c4fb311d6a7106fd192b46b736dd\transformed\media3-common-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-ui:1.2.1] E:\Android\GradleRepository\caches\8.11.1\transforms\49597db2778997481be37e668c0dc570\transformed\media3-ui-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\1eda4a37a41c7fa43796aa98ac846637\transformed\recyclerview-1.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] E:\Android\GradleRepository\caches\8.11.1\transforms\d6fe2bb6ecd36d5f7434cb2927f0e07f\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.12.0] E:\Android\GradleRepository\caches\8.11.1\transforms\d6e22c339ab62c836de58fc4ef784c6a\transformed\core-ktx-1.12.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] E:\Android\GradleRepository\caches\8.11.1\transforms\b6a099da019093cf35d55d20186f2361\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media:media:1.6.0] E:\Android\GradleRepository\caches\8.11.1\transforms\d6110d3a31ca85efccec738fee86e9c9\transformed\media-1.6.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] E:\Android\GradleRepository\caches\8.11.1\transforms\50685627f4ec947d1f399166a8d5cf8c\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] E:\Android\GradleRepository\caches\8.11.1\transforms\4b5757f1097306c9306b50e12d2bc729\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] E:\Android\GradleRepository\caches\8.11.1\transforms\dc49ce241955db2bb69dfc9cee135fea\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.0.0] E:\Android\GradleRepository\caches\8.11.1\transforms\33a9f2af54abd32088741ddccfc68190\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.window:window:1.0.0] E:\Android\GradleRepository\caches\8.11.1\transforms\56ac8c8b02471706ed6c589469cdd72e\transformed\window-1.0.0\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.autofill:autofill:1.0.0] E:\Android\GradleRepository\caches\8.11.1\transforms\dc6edea77ef75e810b10ee688fb62663\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.12.0] E:\Android\GradleRepository\caches\8.11.1\transforms\cd03543d47fe14dfe0d4dd3fa7052145\transformed\core-1.12.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] E:\Android\GradleRepository\caches\8.11.1\transforms\466aae8efd789a52b7bee5036e9c7057\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] E:\Android\GradleRepository\caches\8.11.1\transforms\970605b8de4735519bcd0279714bc005\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] E:\Android\GradleRepository\caches\8.11.1\transforms\4e045eda57cc785f31f67570accce1f4\transformed\lifecycle-viewmodel-compose-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.30.1] E:\Android\GradleRepository\caches\8.11.1\transforms\5959171c2bbdaa790da3d06f13761028\transformed\accompanist-drawablepainter-0.30.1\AndroidManifest.xml:17:1-25:12
MERGED from [androidx.compose.ui:ui-android:1.6.0] E:\Android\GradleRepository\caches\8.11.1\transforms\20b457db33980931945b459e8f30a9cc\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.6.0] E:\Android\GradleRepository\caches\8.11.1\transforms\32ae5f2688caef9d9455298cb45c4d8e\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.0] E:\Android\GradleRepository\caches\8.11.1\transforms\37aef209f208d9bae39069c9bd3ef004\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.6.0] E:\Android\GradleRepository\caches\8.11.1\transforms\1b1cce6fe57ed031e144fef98f25f153\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.6.0] E:\Android\GradleRepository\caches\8.11.1\transforms\46814855008d580cb8b9d494cfb3291a\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.6.0] E:\Android\GradleRepository\caches\8.11.1\transforms\ddfa8bcb13ee47dd39637472d4423b2c\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.0] E:\Android\GradleRepository\caches\8.11.1\transforms\157d222c15037b452d257ab8f12ad86e\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-preferences:1.0.0] E:\Android\GradleRepository\caches\8.11.1\transforms\64665e018abb5ae7edcacc785382e282\transformed\datastore-preferences-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.datastore:datastore:1.0.0] E:\Android\GradleRepository\caches\8.11.1\transforms\20eb62d65259b2879fbc4c7c1a816a2c\transformed\datastore-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] E:\Android\GradleRepository\caches\8.11.1\transforms\7afaa54254600ce80c639aa8b74ca91a\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.6.0] E:\Android\GradleRepository\caches\8.11.1\transforms\013038d6b3d27d0021e31dcaaabf2d3e\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.6.0] E:\Android\GradleRepository\caches\8.11.1\transforms\e8c8615c298e101787ad724d29bfb124\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] E:\Android\GradleRepository\caches\8.11.1\transforms\571345d4ae8095740a34b05d4901432e\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] E:\Android\GradleRepository\caches\8.11.1\transforms\30cd39dc3c741a325f18e979579d5822\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.exifinterface:exifinterface:1.3.6] E:\Android\GradleRepository\caches\8.11.1\transforms\9c4f88d544d5bcba5b12d1a564059b0d\transformed\exifinterface-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] E:\Android\GradleRepository\caches\8.11.1\transforms\325c01f3852cc7f7484824cf5be904b7\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\1f9356cc7725a92deea72a3b5f9be356\transformed\profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] E:\Android\GradleRepository\caches\8.11.1\transforms\117cbf39a27888a52fbdcd897aacbe9d\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] E:\Android\GradleRepository\caches\8.11.1\transforms\67fd7f5a7f58807342f7ed3cf1b6080b\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] E:\Android\GradleRepository\caches\8.11.1\transforms\63dd2af63f595bf56335561ee235f2b3\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] E:\Android\GradleRepository\caches\8.11.1\transforms\e1b06c4bf7aa3bfb49d4614b0cbceabc\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:15:1-38:12
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] E:\Android\GradleRepository\caches\8.11.1\transforms\aaa1dbd1c9bbb60e869a4dfb4b371a5b\transformed\transport-runtime-2.2.6\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.datatransport:transport-api:2.2.1] E:\Android\GradleRepository\caches\8.11.1\transforms\3f854b1f8b54392d781fb5da4cbb1735\transformed\transport-api-2.2.1\AndroidManifest.xml:15:1-22:12
MERGED from [com.google.firebase:firebase-components:16.1.0] E:\Android\GradleRepository\caches\8.11.1\transforms\63d073c0d12c3a5891d822cbdc2bacca\transformed\firebase-components-16.1.0\AndroidManifest.xml:15:1-23:12
MERGED from [com.google.firebase:firebase-encoders-json:17.1.0] E:\Android\GradleRepository\caches\8.11.1\transforms\7bc2732928b77e055d97d81424770056\transformed\firebase-encoders-json-17.1.0\AndroidManifest.xml:15:1-23:12
MERGED from [com.alphacephei:vosk-android:0.3.45] E:\Android\GradleRepository\caches\8.11.1\transforms\c269beacc7b5440a3039b7022bce8905\transformed\vosk-android-0.3.45\AndroidManifest.xml:2:1-9:12
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\53177004b6b735f0506f1ff457ffd742\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:17:1-56:12
MERGED from [com.google.android.odml:image:1.0.0-beta1] E:\Android\GradleRepository\caches\8.11.1\transforms\54413f5888ff2d62c093ed3ca74357b7\transformed\image-1.0.0-beta1\AndroidManifest.xml:2:1-9:12
	package
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:4:5-41
		INJECTED from E:\AIHealthButler\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from E:\AIHealthButler\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from E:\AIHealthButler\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] E:\Android\GradleRepository\caches\8.11.1\transforms\e1b06c4bf7aa3bfb49d4614b0cbceabc\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:26:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] E:\Android\GradleRepository\caches\8.11.1\transforms\e1b06c4bf7aa3bfb49d4614b0cbceabc\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:26:5-67
	android:name
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:8:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:9:5-79
MERGED from [androidx.media3:media3-exoplayer:1.2.1] E:\Android\GradleRepository\caches\8.11.1\transforms\967a26396f59d47749511e6680b66c88\transformed\media3-exoplayer-1.2.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-exoplayer:1.2.1] E:\Android\GradleRepository\caches\8.11.1\transforms\967a26396f59d47749511e6680b66c88\transformed\media3-exoplayer-1.2.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-common:1.2.1] E:\Android\GradleRepository\caches\8.11.1\transforms\1a76c4fb311d6a7106fd192b46b736dd\transformed\media3-common-1.2.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-common:1.2.1] E:\Android\GradleRepository\caches\8.11.1\transforms\1a76c4fb311d6a7106fd192b46b736dd\transformed\media3-common-1.2.1\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] E:\Android\GradleRepository\caches\8.11.1\transforms\e1b06c4bf7aa3bfb49d4614b0cbceabc\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] E:\Android\GradleRepository\caches\8.11.1\transforms\e1b06c4bf7aa3bfb49d4614b0cbceabc\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] E:\Android\GradleRepository\caches\8.11.1\transforms\aaa1dbd1c9bbb60e869a4dfb4b371a5b\transformed\transport-runtime-2.2.6\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] E:\Android\GradleRepository\caches\8.11.1\transforms\aaa1dbd1c9bbb60e869a4dfb4b371a5b\transformed\transport-runtime-2.2.6\AndroidManifest.xml:22:5-79
	android:name
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:9:22-76
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:10:5-76
	android:name
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:10:22-73
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:12:5-13:51
	android:maxSdkVersion
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:13:22-48
	android:name
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:12:22-77
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:14:5-80
	android:name
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:14:22-78
uses-permission#android.permission.CAMERA
ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:16:5-17:71
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\53177004b6b735f0506f1ff457ffd742\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:22:5-65
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\53177004b6b735f0506f1ff457ffd742\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:22:5-65
	tools:ignore
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:17:9-68
	android:name
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:16:22-62
uses-permission#android.permission.RECORD_AUDIO
ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:19:5-70
	android:name
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:19:22-68
uses-permission#android.permission.MODIFY_AUDIO_SETTINGS
ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:21:5-79
	android:name
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:21:22-77
uses-permission#android.permission.WAKE_LOCK
ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:22:5-67
	android:name
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:22:22-65
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:24:5-78
	android:name
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:24:22-76
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:25:5-80
	android:name
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:25:22-78
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:28:5-77
	android:name
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:28:22-74
uses-permission#android.permission.READ_MEDIA_IMAGES
ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:29:5-76
	android:name
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:29:22-73
application
ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:30:5-240:19
INJECTED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:30:5-240:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.0] E:\Android\GradleRepository\caches\8.11.1\transforms\85f7238b6a16c9bd9d8a581b064da59f\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.0] E:\Android\GradleRepository\caches\8.11.1\transforms\85f7238b6a16c9bd9d8a581b064da59f\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.camera:camera-camera2:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\c314f6fd8963a93598e7e827dca44cf2\transformed\camera-camera2-1.3.1\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-camera2:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\c314f6fd8963a93598e7e827dca44cf2\transformed\camera-camera2-1.3.1\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\3e370a08e3987525bf1b894c2d626a6f\transformed\camera-core-1.3.1\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\3e370a08e3987525bf1b894c2d626a6f\transformed\camera-core-1.3.1\AndroidManifest.xml:23:5-34:19
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\f3d7a0950d8edd522d3a2e68917e9fd0\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\f3d7a0950d8edd522d3a2e68917e9fd0\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.mlkit:vision-common:17.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\8c7116a2e8c72f5558ec46ba66165641\transformed\vision-common-17.3.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.mlkit:vision-common:17.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\8c7116a2e8c72f5558ec46ba66165641\transformed\vision-common-17.3.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.mlkit:common:18.9.0] E:\Android\GradleRepository\caches\8.11.1\transforms\856b4a7a015be168cc7fc8a4d2056f38\transformed\common-18.9.0\AndroidManifest.xml:8:5-24:19
MERGED from [com.google.mlkit:common:18.9.0] E:\Android\GradleRepository\caches\8.11.1\transforms\856b4a7a015be168cc7fc8a4d2056f38\transformed\common-18.9.0\AndroidManifest.xml:8:5-24:19
MERGED from [com.google.android.gms:play-services-base:18.1.0] E:\Android\GradleRepository\caches\8.11.1\transforms\ea1d6e38040e7b8436c098f4eca33857\transformed\play-services-base-18.1.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.1.0] E:\Android\GradleRepository\caches\8.11.1\transforms\ea1d6e38040e7b8436c098f4eca33857\transformed\play-services-base-18.1.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] E:\Android\GradleRepository\caches\8.11.1\transforms\0f69453f5e0116ca1e270fa5e55fd93b\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] E:\Android\GradleRepository\caches\8.11.1\transforms\0f69453f5e0116ca1e270fa5e55fd93b\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-basement:18.1.0] E:\Android\GradleRepository\caches\8.11.1\transforms\237eff3a41ee89ac3ef69d6430961f46\transformed\play-services-basement-18.1.0\AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.1.0] E:\Android\GradleRepository\caches\8.11.1\transforms\237eff3a41ee89ac3ef69d6430961f46\transformed\play-services-basement-18.1.0\AndroidManifest.xml:20:5-24:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.0] E:\Android\GradleRepository\caches\8.11.1\transforms\d876ee35e176acdeab4ede638c85e11d\transformed\ui-test-manifest-1.6.0\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.0] E:\Android\GradleRepository\caches\8.11.1\transforms\d876ee35e176acdeab4ede638c85e11d\transformed\ui-test-manifest-1.6.0\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.emoji2:emoji2:1.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\55d31107e6254ca36703c0e778ec1289\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\55d31107e6254ca36703c0e778ec1289\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] E:\Android\GradleRepository\caches\8.11.1\transforms\0fa4b7e7a7fef6254fbd677efdefa57e\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] E:\Android\GradleRepository\caches\8.11.1\transforms\0fa4b7e7a7fef6254fbd677efdefa57e\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.window:window:1.0.0] E:\Android\GradleRepository\caches\8.11.1\transforms\56ac8c8b02471706ed6c589469cdd72e\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.window:window:1.0.0] E:\Android\GradleRepository\caches\8.11.1\transforms\56ac8c8b02471706ed6c589469cdd72e\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.core:core:1.12.0] E:\Android\GradleRepository\caches\8.11.1\transforms\cd03543d47fe14dfe0d4dd3fa7052145\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.12.0] E:\Android\GradleRepository\caches\8.11.1\transforms\cd03543d47fe14dfe0d4dd3fa7052145\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] E:\Android\GradleRepository\caches\8.11.1\transforms\571345d4ae8095740a34b05d4901432e\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] E:\Android\GradleRepository\caches\8.11.1\transforms\571345d4ae8095740a34b05d4901432e\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\1f9356cc7725a92deea72a3b5f9be356\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\1f9356cc7725a92deea72a3b5f9be356\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] E:\Android\GradleRepository\caches\8.11.1\transforms\117cbf39a27888a52fbdcd897aacbe9d\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] E:\Android\GradleRepository\caches\8.11.1\transforms\117cbf39a27888a52fbdcd897aacbe9d\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] E:\Android\GradleRepository\caches\8.11.1\transforms\e1b06c4bf7aa3bfb49d4614b0cbceabc\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:28:5-36:19
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] E:\Android\GradleRepository\caches\8.11.1\transforms\e1b06c4bf7aa3bfb49d4614b0cbceabc\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:28:5-36:19
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] E:\Android\GradleRepository\caches\8.11.1\transforms\aaa1dbd1c9bbb60e869a4dfb4b371a5b\transformed\transport-runtime-2.2.6\AndroidManifest.xml:25:5-39:19
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] E:\Android\GradleRepository\caches\8.11.1\transforms\aaa1dbd1c9bbb60e869a4dfb4b371a5b\transformed\transport-runtime-2.2.6\AndroidManifest.xml:25:5-39:19
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\53177004b6b735f0506f1ff457ffd742\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:46:5-54:19
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\53177004b6b735f0506f1ff457ffd742\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:46:5-54:19
MERGED from [com.google.android.odml:image:1.0.0-beta1] E:\Android\GradleRepository\caches\8.11.1\transforms\54413f5888ff2d62c093ed3ca74357b7\transformed\image-1.0.0-beta1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.odml:image:1.0.0-beta1] E:\Android\GradleRepository\caches\8.11.1\transforms\54413f5888ff2d62c093ed3ca74357b7\transformed\image-1.0.0-beta1\AndroidManifest.xml:7:5-20
	android:extractNativeLibs
		INJECTED from E:\AIHealthButler\app\src\main\AndroidManifest.xml
	android:roundIcon
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:36:9-54
	android:icon
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:34:9-43
	android:networkSecurityConfig
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:39:9-69
	android:appComponentFactory
		ADDED from [androidx.core:core:1.12.0] E:\Android\GradleRepository\caches\8.11.1\transforms\cd03543d47fe14dfe0d4dd3fa7052145\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:37:9-35
	android:label
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:35:9-41
	android:fullBackupContent
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:33:9-54
	tools:targetApi
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:42:9-29
	android:allowBackup
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:31:9-35
	android:theme
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:38:9-52
	android:enableOnBackInvokedCallback
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:41:9-51
	android:dataExtractionRules
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:32:9-65
	android:usesCleartextTraffic
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:40:9-44
activity#com.example.aihealthbutler.SignInActivity
ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:67:9-76:20
	android:label
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:70:13-45
	android:exported
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:69:13-36
	android:theme
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:71:13-56
	android:name
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:68:13-43
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:72:13-75:29
action#android.intent.action.MAIN
ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:73:17-69
	android:name
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:73:25-66
category#android.intent.category.LAUNCHER
ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:74:17-77
	android:name
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:74:27-74
activity#com.example.aihealthbutler.ConversationInterface
ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:81:9-86:20
	android:label
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:84:13-45
	android:exported
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:83:13-36
	android:theme
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:85:13-56
	android:name
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:82:13-50
activity#com.example.aihealthbutler.MainActivity
ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:137:9-140:59
	android:exported
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:139:13-36
	android:theme
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:140:13-56
	android:name
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:138:13-41
activity#com.example.aihealthbutler.MedicationPlanActivity
ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:143:9-146:59
	android:exported
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:145:13-36
	android:theme
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:146:13-56
	android:name
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:144:13-51
activity#com.example.aihealthbutler.AddnewmedicationplanActivity
ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:149:9-152:59
	android:exported
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:151:13-36
	android:theme
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:152:13-56
	android:name
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:150:13-57
activity#com.example.aihealthbutler.ExerciseAdviceActivity
ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:155:9-158:59
	android:exported
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:157:13-36
	android:theme
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:158:13-56
	android:name
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:156:13-51
activity#com.example.aihealthbutler.DietaryAdviceActivity
ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:161:9-164:59
	android:exported
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:163:13-36
	android:theme
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:164:13-56
	android:name
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:162:13-50
activity#com.example.aihealthbutler.PersonalInformationActivity
ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:167:9-170:59
	android:exported
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:169:13-36
	android:theme
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:170:13-56
	android:name
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:168:13-56
activity#com.example.aihealthbutler.AddNewFamilyMember
ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:173:9-176:59
	android:exported
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:175:13-36
	android:theme
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:176:13-56
	android:name
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:174:13-47
activity#com.example.aihealthbutler.DocumentFolderActivity
ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:181:9-184:59
	android:exported
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:183:13-36
	android:theme
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:184:13-56
	android:name
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:182:13-51
activity#com.example.aihealthbutler.UploadMaterialsActivity
ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:187:9-190:59
	android:exported
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:189:13-36
	android:theme
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:190:13-56
	android:name
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:188:13-52
activity#com.example.aihealthbutler.HealthHistoryActivity
ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:193:9-196:52
	android:screenOrientation
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:196:13-49
	android:exported
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:195:13-37
	android:name
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:194:13-50
activity#com.example.aihealthbutler.PermissionsManagerActivity
ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:199:9-202:59
	android:exported
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:201:13-36
	android:theme
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:202:13-56
	android:name
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:200:13-55
activity#com.example.aihealthbutler.FamilyMemberSwitchActivity
ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:205:9-208:59
	android:exported
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:207:13-36
	android:theme
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:208:13-56
	android:name
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:206:13-55
activity#com.example.aihealthbutler.InviteQRCodeActivity
ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:211:9-214:59
	android:exported
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:213:13-36
	android:theme
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:214:13-56
	android:name
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:212:13-49
activity#com.example.aihealthbutler.QRScannerActivity
ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:217:9-220:59
	android:exported
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:219:13-36
	android:theme
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:220:13-56
	android:name
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:218:13-46
activity#com.example.aihealthbutler.AddMemberConfirmActivity
ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:223:9-226:59
	android:exported
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:225:13-36
	android:theme
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:226:13-56
	android:name
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:224:13-53
activity#com.example.aihealthbutler.RelationshipSettingActivity
ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:229:9-232:59
	android:exported
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:231:13-36
	android:theme
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:232:13-56
	android:name
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:230:13-56
activity#com.example.aihealthbutler.debug.NetworkDebugActivity
ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:235:9-238:59
	android:exported
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:237:13-37
	android:theme
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:238:13-56
	android:name
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:236:13-55
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:60:13-62:54
	android:resource
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:62:17-51
	android:name
		ADDED from E:\AIHealthButler\app\src\main\AndroidManifest.xml:61:17-67
uses-sdk
INJECTED from E:\AIHealthButler\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from E:\AIHealthButler\app\src\main\AndroidManifest.xml
INJECTED from E:\AIHealthButler\app\src\main\AndroidManifest.xml
MERGED from [androidx.compose.material3:material3-window-size-class-android:1.2.0] E:\Android\GradleRepository\caches\8.11.1\transforms\2f41c2bf5cd93f347fc35cf9bdb9d91d\transformed\material3-window-size-class-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material3:material3-window-size-class-android:1.2.0] E:\Android\GradleRepository\caches\8.11.1\transforms\2f41c2bf5cd93f347fc35cf9bdb9d91d\transformed\material3-window-size-class-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material3:material3-android:1.2.0] E:\Android\GradleRepository\caches\8.11.1\transforms\86b5dfd63cec3973e80a6022d540b3e9\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.2.0] E:\Android\GradleRepository\caches\8.11.1\transforms\86b5dfd63cec3973e80a6022d540b3e9\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [io.coil-kt:coil-compose:2.4.0] E:\Android\GradleRepository\caches\8.11.1\transforms\ab21ea38002e4144992f12c17e998733\transformed\coil-compose-2.4.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose:2.4.0] E:\Android\GradleRepository\caches\8.11.1\transforms\ab21ea38002e4144992f12c17e998733\transformed\coil-compose-2.4.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose-base:2.4.0] E:\Android\GradleRepository\caches\8.11.1\transforms\e73c396c31ea48760386989516db2619\transformed\coil-compose-base-2.4.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose-base:2.4.0] E:\Android\GradleRepository\caches\8.11.1\transforms\e73c396c31ea48760386989516db2619\transformed\coil-compose-base-2.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.0] E:\Android\GradleRepository\caches\8.11.1\transforms\85f7238b6a16c9bd9d8a581b064da59f\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.0] E:\Android\GradleRepository\caches\8.11.1\transforms\85f7238b6a16c9bd9d8a581b064da59f\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-android:1.6.0] E:\Android\GradleRepository\caches\8.11.1\transforms\21a835513d52cc852e2ae5ad4f6a8015\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.6.0] E:\Android\GradleRepository\caches\8.11.1\transforms\21a835513d52cc852e2ae5ad4f6a8015\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.6.0] E:\Android\GradleRepository\caches\8.11.1\transforms\98b438f8eae45236225ffffa180286a6\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.6.0] E:\Android\GradleRepository\caches\8.11.1\transforms\98b438f8eae45236225ffffa180286a6\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.6.0] E:\Android\GradleRepository\caches\8.11.1\transforms\9e816d3c7fd7d539922913409377f4ef\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.6.0] E:\Android\GradleRepository\caches\8.11.1\transforms\9e816d3c7fd7d539922913409377f4ef\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-video:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\d0248a544f037ae1e986415bab63cbe3\transformed\camera-video-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-video:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\d0248a544f037ae1e986415bab63cbe3\transformed\camera-video-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-lifecycle:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\aebfb8c2a99005efac30e07324da7ade\transformed\camera-lifecycle-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-lifecycle:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\aebfb8c2a99005efac30e07324da7ade\transformed\camera-lifecycle-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-camera2:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\c314f6fd8963a93598e7e827dca44cf2\transformed\camera-camera2-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-camera2:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\c314f6fd8963a93598e7e827dca44cf2\transformed\camera-camera2-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\3e370a08e3987525bf1b894c2d626a6f\transformed\camera-core-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\3e370a08e3987525bf1b894c2d626a6f\transformed\camera-core-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-view:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\0e64989f53416def46aa18b9e7aae563\transformed\camera-view-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-view:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\0e64989f53416def46aa18b9e7aae563\transformed\camera-view-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil:2.4.0] E:\Android\GradleRepository\caches\8.11.1\transforms\026a1795aa1152f40d5fcb9fca71aaa0\transformed\coil-2.4.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil:2.4.0] E:\Android\GradleRepository\caches\8.11.1\transforms\026a1795aa1152f40d5fcb9fca71aaa0\transformed\coil-2.4.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-base:2.4.0] E:\Android\GradleRepository\caches\8.11.1\transforms\89c55e4f46ae88f012b8f8e6dba3d043\transformed\coil-base-2.4.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-base:2.4.0] E:\Android\GradleRepository\caches\8.11.1\transforms\89c55e4f46ae88f012b8f8e6dba3d043\transformed\coil-base-2.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] E:\Android\GradleRepository\caches\8.11.1\transforms\47ecf685396a9221b9a09834661916ac\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] E:\Android\GradleRepository\caches\8.11.1\transforms\47ecf685396a9221b9a09834661916ac\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] E:\Android\GradleRepository\caches\8.11.1\transforms\98923f9ee7dfb9b0a26244e15c6569b0\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] E:\Android\GradleRepository\caches\8.11.1\transforms\98923f9ee7dfb9b0a26244e15c6569b0\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.mlkit:barcode-scanning:17.2.0] E:\Android\GradleRepository\caches\8.11.1\transforms\aa58d2b0e946be5f142c74110d1e6a5a\transformed\barcode-scanning-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:barcode-scanning:17.2.0] E:\Android\GradleRepository\caches\8.11.1\transforms\aa58d2b0e946be5f142c74110d1e6a5a\transformed\barcode-scanning-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\f3d7a0950d8edd522d3a2e68917e9fd0\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\f3d7a0950d8edd522d3a2e68917e9fd0\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:barcode-scanning-common:17.0.0] E:\Android\GradleRepository\caches\8.11.1\transforms\a9ad2c77aac517e1ef17ede1c543cfce\transformed\barcode-scanning-common-17.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:barcode-scanning-common:17.0.0] E:\Android\GradleRepository\caches\8.11.1\transforms\a9ad2c77aac517e1ef17ede1c543cfce\transformed\barcode-scanning-common-17.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:vision-common:17.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\8c7116a2e8c72f5558ec46ba66165641\transformed\vision-common-17.3.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:vision-common:17.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\8c7116a2e8c72f5558ec46ba66165641\transformed\vision-common-17.3.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:common:18.9.0] E:\Android\GradleRepository\caches\8.11.1\transforms\856b4a7a015be168cc7fc8a4d2056f38\transformed\common-18.9.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:common:18.9.0] E:\Android\GradleRepository\caches\8.11.1\transforms\856b4a7a015be168cc7fc8a4d2056f38\transformed\common-18.9.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-base:18.1.0] E:\Android\GradleRepository\caches\8.11.1\transforms\ea1d6e38040e7b8436c098f4eca33857\transformed\play-services-base-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-base:18.1.0] E:\Android\GradleRepository\caches\8.11.1\transforms\ea1d6e38040e7b8436c098f4eca33857\transformed\play-services-base-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.mlkit:vision-interfaces:16.2.0] E:\Android\GradleRepository\caches\8.11.1\transforms\e183da1785eac375f2b9348a7155748b\transformed\vision-interfaces-16.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:vision-interfaces:16.2.0] E:\Android\GradleRepository\caches\8.11.1\transforms\e183da1785eac375f2b9348a7155748b\transformed\vision-interfaces-16.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] E:\Android\GradleRepository\caches\8.11.1\transforms\0f69453f5e0116ca1e270fa5e55fd93b\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] E:\Android\GradleRepository\caches\8.11.1\transforms\0f69453f5e0116ca1e270fa5e55fd93b\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-basement:18.1.0] E:\Android\GradleRepository\caches\8.11.1\transforms\237eff3a41ee89ac3ef69d6430961f46\transformed\play-services-basement-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.1.0] E:\Android\GradleRepository\caches\8.11.1\transforms\237eff3a41ee89ac3ef69d6430961f46\transformed\play-services-basement-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.fragment:fragment:1.3.6] E:\Android\GradleRepository\caches\8.11.1\transforms\3c679ce1ad26295b627835c89519c1ab\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] E:\Android\GradleRepository\caches\8.11.1\transforms\3c679ce1ad26295b627835c89519c1ab\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.compose.animation:animation-android:1.6.0] E:\Android\GradleRepository\caches\8.11.1\transforms\7b0c59989fe9f7830317d13fe9fe3848\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.6.0] E:\Android\GradleRepository\caches\8.11.1\transforms\7b0c59989fe9f7830317d13fe9fe3848\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.0] E:\Android\GradleRepository\caches\8.11.1\transforms\9db548b2bbe19e65621ab49498ad5d00\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.0] E:\Android\GradleRepository\caches\8.11.1\transforms\9db548b2bbe19e65621ab49498ad5d00\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.0] E:\Android\GradleRepository\caches\8.11.1\transforms\cc030595ea9277977bffb5fe2942e414\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.0] E:\Android\GradleRepository\caches\8.11.1\transforms\cc030595ea9277977bffb5fe2942e414\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.0] E:\Android\GradleRepository\caches\8.11.1\transforms\eafe6cbe8efa2ee047ddd5a1b58b1258\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.0] E:\Android\GradleRepository\caches\8.11.1\transforms\eafe6cbe8efa2ee047ddd5a1b58b1258\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.6.0] E:\Android\GradleRepository\caches\8.11.1\transforms\5af9735ae5b86d8631afc671c6ddfbaa\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.6.0] E:\Android\GradleRepository\caches\8.11.1\transforms\5af9735ae5b86d8631afc671c6ddfbaa\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.0] E:\Android\GradleRepository\caches\8.11.1\transforms\d876ee35e176acdeab4ede638c85e11d\transformed\ui-test-manifest-1.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.0] E:\Android\GradleRepository\caches\8.11.1\transforms\d876ee35e176acdeab4ede638c85e11d\transformed\ui-test-manifest-1.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.2] E:\Android\GradleRepository\caches\8.11.1\transforms\1fea433c97f471a9137ce06f998eda68\transformed\activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.2] E:\Android\GradleRepository\caches\8.11.1\transforms\1fea433c97f471a9137ce06f998eda68\transformed\activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] E:\Android\GradleRepository\caches\8.11.1\transforms\82688ef9838c3451823a067ea0e59854\transformed\activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] E:\Android\GradleRepository\caches\8.11.1\transforms\82688ef9838c3451823a067ea0e59854\transformed\activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.8.2] E:\Android\GradleRepository\caches\8.11.1\transforms\410c4ce1fea79cc790f161d4d76790a0\transformed\activity-compose-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.8.2] E:\Android\GradleRepository\caches\8.11.1\transforms\410c4ce1fea79cc790f161d4d76790a0\transformed\activity-compose-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.loader:loader:1.0.0] E:\Android\GradleRepository\caches\8.11.1\transforms\87a477057b42ef90f12fce8fc68a7f8f\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] E:\Android\GradleRepository\caches\8.11.1\transforms\87a477057b42ef90f12fce8fc68a7f8f\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\26ac74328117b1bd14521c851a36bdd2\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\26ac74328117b1bd14521c851a36bdd2\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\55d31107e6254ca36703c0e778ec1289\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\55d31107e6254ca36703c0e778ec1289\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] E:\Android\GradleRepository\caches\8.11.1\transforms\0fa4b7e7a7fef6254fbd677efdefa57e\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] E:\Android\GradleRepository\caches\8.11.1\transforms\0fa4b7e7a7fef6254fbd677efdefa57e\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] E:\Android\GradleRepository\caches\8.11.1\transforms\e96073b33bb9c6136af0c75c0aa73fe6\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] E:\Android\GradleRepository\caches\8.11.1\transforms\e96073b33bb9c6136af0c75c0aa73fe6\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] E:\Android\GradleRepository\caches\8.11.1\transforms\cbf2104000376f0a577b8de602bc5818\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] E:\Android\GradleRepository\caches\8.11.1\transforms\cbf2104000376f0a577b8de602bc5818\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] E:\Android\GradleRepository\caches\8.11.1\transforms\d20f7d67e940eaf1fd21d12ddeb5bd7d\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] E:\Android\GradleRepository\caches\8.11.1\transforms\d20f7d67e940eaf1fd21d12ddeb5bd7d\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] E:\Android\GradleRepository\caches\8.11.1\transforms\43bea879fddc0a79466f9a9d6f3a0bd9\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] E:\Android\GradleRepository\caches\8.11.1\transforms\43bea879fddc0a79466f9a9d6f3a0bd9\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] E:\Android\GradleRepository\caches\8.11.1\transforms\8365898dd6622c2a175015ae80ab25e3\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] E:\Android\GradleRepository\caches\8.11.1\transforms\8365898dd6622c2a175015ae80ab25e3\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] E:\Android\GradleRepository\caches\8.11.1\transforms\d059301b8dfb6a0c2f94ff3d200123a6\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] E:\Android\GradleRepository\caches\8.11.1\transforms\d059301b8dfb6a0c2f94ff3d200123a6\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] E:\Android\GradleRepository\caches\8.11.1\transforms\d6a0b7cff17faf686184fbf83b9c11b9\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] E:\Android\GradleRepository\caches\8.11.1\transforms\d6a0b7cff17faf686184fbf83b9c11b9\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] E:\Android\GradleRepository\caches\8.11.1\transforms\94836782e804062b52bd9a05d3e2d56e\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] E:\Android\GradleRepository\caches\8.11.1\transforms\94836782e804062b52bd9a05d3e2d56e\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.2.1] E:\Android\GradleRepository\caches\8.11.1\transforms\967a26396f59d47749511e6680b66c88\transformed\media3-exoplayer-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.2.1] E:\Android\GradleRepository\caches\8.11.1\transforms\967a26396f59d47749511e6680b66c88\transformed\media3-exoplayer-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-extractor:1.2.1] E:\Android\GradleRepository\caches\8.11.1\transforms\2725db465e8b9b28fc341b348a3e7786\transformed\media3-extractor-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-extractor:1.2.1] E:\Android\GradleRepository\caches\8.11.1\transforms\2725db465e8b9b28fc341b348a3e7786\transformed\media3-extractor-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.2.1] E:\Android\GradleRepository\caches\8.11.1\transforms\32527f2999eabc806cadcf90305de024\transformed\media3-container-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.2.1] E:\Android\GradleRepository\caches\8.11.1\transforms\32527f2999eabc806cadcf90305de024\transformed\media3-container-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.2.1] E:\Android\GradleRepository\caches\8.11.1\transforms\5489a46b3e5d5cc00f637d06b62f8852\transformed\media3-datasource-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.2.1] E:\Android\GradleRepository\caches\8.11.1\transforms\5489a46b3e5d5cc00f637d06b62f8852\transformed\media3-datasource-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.2.1] E:\Android\GradleRepository\caches\8.11.1\transforms\5cc4dacbfb9646b932ebba880cdd3c6a\transformed\media3-decoder-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.2.1] E:\Android\GradleRepository\caches\8.11.1\transforms\5cc4dacbfb9646b932ebba880cdd3c6a\transformed\media3-decoder-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.2.1] E:\Android\GradleRepository\caches\8.11.1\transforms\e19550e485f474c20e5db05740a1de7b\transformed\media3-database-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.2.1] E:\Android\GradleRepository\caches\8.11.1\transforms\e19550e485f474c20e5db05740a1de7b\transformed\media3-database-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.2.1] E:\Android\GradleRepository\caches\8.11.1\transforms\1a76c4fb311d6a7106fd192b46b736dd\transformed\media3-common-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.2.1] E:\Android\GradleRepository\caches\8.11.1\transforms\1a76c4fb311d6a7106fd192b46b736dd\transformed\media3-common-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-ui:1.2.1] E:\Android\GradleRepository\caches\8.11.1\transforms\49597db2778997481be37e668c0dc570\transformed\media3-ui-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-ui:1.2.1] E:\Android\GradleRepository\caches\8.11.1\transforms\49597db2778997481be37e668c0dc570\transformed\media3-ui-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\1eda4a37a41c7fa43796aa98ac846637\transformed\recyclerview-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\1eda4a37a41c7fa43796aa98ac846637\transformed\recyclerview-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] E:\Android\GradleRepository\caches\8.11.1\transforms\d6fe2bb6ecd36d5f7434cb2927f0e07f\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] E:\Android\GradleRepository\caches\8.11.1\transforms\d6fe2bb6ecd36d5f7434cb2927f0e07f\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.12.0] E:\Android\GradleRepository\caches\8.11.1\transforms\d6e22c339ab62c836de58fc4ef784c6a\transformed\core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.12.0] E:\Android\GradleRepository\caches\8.11.1\transforms\d6e22c339ab62c836de58fc4ef784c6a\transformed\core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] E:\Android\GradleRepository\caches\8.11.1\transforms\b6a099da019093cf35d55d20186f2361\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] E:\Android\GradleRepository\caches\8.11.1\transforms\b6a099da019093cf35d55d20186f2361\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media:media:1.6.0] E:\Android\GradleRepository\caches\8.11.1\transforms\d6110d3a31ca85efccec738fee86e9c9\transformed\media-1.6.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.6.0] E:\Android\GradleRepository\caches\8.11.1\transforms\d6110d3a31ca85efccec738fee86e9c9\transformed\media-1.6.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] E:\Android\GradleRepository\caches\8.11.1\transforms\50685627f4ec947d1f399166a8d5cf8c\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] E:\Android\GradleRepository\caches\8.11.1\transforms\50685627f4ec947d1f399166a8d5cf8c\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] E:\Android\GradleRepository\caches\8.11.1\transforms\4b5757f1097306c9306b50e12d2bc729\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] E:\Android\GradleRepository\caches\8.11.1\transforms\4b5757f1097306c9306b50e12d2bc729\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] E:\Android\GradleRepository\caches\8.11.1\transforms\dc49ce241955db2bb69dfc9cee135fea\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] E:\Android\GradleRepository\caches\8.11.1\transforms\dc49ce241955db2bb69dfc9cee135fea\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] E:\Android\GradleRepository\caches\8.11.1\transforms\33a9f2af54abd32088741ddccfc68190\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] E:\Android\GradleRepository\caches\8.11.1\transforms\33a9f2af54abd32088741ddccfc68190\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window:1.0.0] E:\Android\GradleRepository\caches\8.11.1\transforms\56ac8c8b02471706ed6c589469cdd72e\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] E:\Android\GradleRepository\caches\8.11.1\transforms\56ac8c8b02471706ed6c589469cdd72e\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] E:\Android\GradleRepository\caches\8.11.1\transforms\dc6edea77ef75e810b10ee688fb62663\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] E:\Android\GradleRepository\caches\8.11.1\transforms\dc6edea77ef75e810b10ee688fb62663\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.12.0] E:\Android\GradleRepository\caches\8.11.1\transforms\cd03543d47fe14dfe0d4dd3fa7052145\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.12.0] E:\Android\GradleRepository\caches\8.11.1\transforms\cd03543d47fe14dfe0d4dd3fa7052145\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] E:\Android\GradleRepository\caches\8.11.1\transforms\466aae8efd789a52b7bee5036e9c7057\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] E:\Android\GradleRepository\caches\8.11.1\transforms\466aae8efd789a52b7bee5036e9c7057\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] E:\Android\GradleRepository\caches\8.11.1\transforms\970605b8de4735519bcd0279714bc005\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] E:\Android\GradleRepository\caches\8.11.1\transforms\970605b8de4735519bcd0279714bc005\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] E:\Android\GradleRepository\caches\8.11.1\transforms\4e045eda57cc785f31f67570accce1f4\transformed\lifecycle-viewmodel-compose-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] E:\Android\GradleRepository\caches\8.11.1\transforms\4e045eda57cc785f31f67570accce1f4\transformed\lifecycle-viewmodel-compose-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.30.1] E:\Android\GradleRepository\caches\8.11.1\transforms\5959171c2bbdaa790da3d06f13761028\transformed\accompanist-drawablepainter-0.30.1\AndroidManifest.xml:21:5-23:42
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.30.1] E:\Android\GradleRepository\caches\8.11.1\transforms\5959171c2bbdaa790da3d06f13761028\transformed\accompanist-drawablepainter-0.30.1\AndroidManifest.xml:21:5-23:42
MERGED from [androidx.compose.ui:ui-android:1.6.0] E:\Android\GradleRepository\caches\8.11.1\transforms\20b457db33980931945b459e8f30a9cc\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.6.0] E:\Android\GradleRepository\caches\8.11.1\transforms\20b457db33980931945b459e8f30a9cc\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.6.0] E:\Android\GradleRepository\caches\8.11.1\transforms\32ae5f2688caef9d9455298cb45c4d8e\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.6.0] E:\Android\GradleRepository\caches\8.11.1\transforms\32ae5f2688caef9d9455298cb45c4d8e\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.0] E:\Android\GradleRepository\caches\8.11.1\transforms\37aef209f208d9bae39069c9bd3ef004\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.0] E:\Android\GradleRepository\caches\8.11.1\transforms\37aef209f208d9bae39069c9bd3ef004\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.6.0] E:\Android\GradleRepository\caches\8.11.1\transforms\1b1cce6fe57ed031e144fef98f25f153\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.6.0] E:\Android\GradleRepository\caches\8.11.1\transforms\1b1cce6fe57ed031e144fef98f25f153\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.6.0] E:\Android\GradleRepository\caches\8.11.1\transforms\46814855008d580cb8b9d494cfb3291a\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.6.0] E:\Android\GradleRepository\caches\8.11.1\transforms\46814855008d580cb8b9d494cfb3291a\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.6.0] E:\Android\GradleRepository\caches\8.11.1\transforms\ddfa8bcb13ee47dd39637472d4423b2c\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.6.0] E:\Android\GradleRepository\caches\8.11.1\transforms\ddfa8bcb13ee47dd39637472d4423b2c\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.0] E:\Android\GradleRepository\caches\8.11.1\transforms\157d222c15037b452d257ab8f12ad86e\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.0] E:\Android\GradleRepository\caches\8.11.1\transforms\157d222c15037b452d257ab8f12ad86e\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-preferences:1.0.0] E:\Android\GradleRepository\caches\8.11.1\transforms\64665e018abb5ae7edcacc785382e282\transformed\datastore-preferences-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.datastore:datastore-preferences:1.0.0] E:\Android\GradleRepository\caches\8.11.1\transforms\64665e018abb5ae7edcacc785382e282\transformed\datastore-preferences-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.datastore:datastore:1.0.0] E:\Android\GradleRepository\caches\8.11.1\transforms\20eb62d65259b2879fbc4c7c1a816a2c\transformed\datastore-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.datastore:datastore:1.0.0] E:\Android\GradleRepository\caches\8.11.1\transforms\20eb62d65259b2879fbc4c7c1a816a2c\transformed\datastore-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.annotation:annotation-experimental:1.4.0] E:\Android\GradleRepository\caches\8.11.1\transforms\7afaa54254600ce80c639aa8b74ca91a\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] E:\Android\GradleRepository\caches\8.11.1\transforms\7afaa54254600ce80c639aa8b74ca91a\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.6.0] E:\Android\GradleRepository\caches\8.11.1\transforms\013038d6b3d27d0021e31dcaaabf2d3e\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.6.0] E:\Android\GradleRepository\caches\8.11.1\transforms\013038d6b3d27d0021e31dcaaabf2d3e\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.6.0] E:\Android\GradleRepository\caches\8.11.1\transforms\e8c8615c298e101787ad724d29bfb124\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.6.0] E:\Android\GradleRepository\caches\8.11.1\transforms\e8c8615c298e101787ad724d29bfb124\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] E:\Android\GradleRepository\caches\8.11.1\transforms\571345d4ae8095740a34b05d4901432e\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] E:\Android\GradleRepository\caches\8.11.1\transforms\571345d4ae8095740a34b05d4901432e\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] E:\Android\GradleRepository\caches\8.11.1\transforms\30cd39dc3c741a325f18e979579d5822\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] E:\Android\GradleRepository\caches\8.11.1\transforms\30cd39dc3c741a325f18e979579d5822\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.exifinterface:exifinterface:1.3.6] E:\Android\GradleRepository\caches\8.11.1\transforms\9c4f88d544d5bcba5b12d1a564059b0d\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] E:\Android\GradleRepository\caches\8.11.1\transforms\9c4f88d544d5bcba5b12d1a564059b0d\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] E:\Android\GradleRepository\caches\8.11.1\transforms\325c01f3852cc7f7484824cf5be904b7\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] E:\Android\GradleRepository\caches\8.11.1\transforms\325c01f3852cc7f7484824cf5be904b7\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\1f9356cc7725a92deea72a3b5f9be356\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\1f9356cc7725a92deea72a3b5f9be356\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] E:\Android\GradleRepository\caches\8.11.1\transforms\117cbf39a27888a52fbdcd897aacbe9d\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] E:\Android\GradleRepository\caches\8.11.1\transforms\117cbf39a27888a52fbdcd897aacbe9d\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] E:\Android\GradleRepository\caches\8.11.1\transforms\67fd7f5a7f58807342f7ed3cf1b6080b\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] E:\Android\GradleRepository\caches\8.11.1\transforms\67fd7f5a7f58807342f7ed3cf1b6080b\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] E:\Android\GradleRepository\caches\8.11.1\transforms\63dd2af63f595bf56335561ee235f2b3\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] E:\Android\GradleRepository\caches\8.11.1\transforms\63dd2af63f595bf56335561ee235f2b3\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] E:\Android\GradleRepository\caches\8.11.1\transforms\e1b06c4bf7aa3bfb49d4614b0cbceabc\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] E:\Android\GradleRepository\caches\8.11.1\transforms\e1b06c4bf7aa3bfb49d4614b0cbceabc\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] E:\Android\GradleRepository\caches\8.11.1\transforms\aaa1dbd1c9bbb60e869a4dfb4b371a5b\transformed\transport-runtime-2.2.6\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] E:\Android\GradleRepository\caches\8.11.1\transforms\aaa1dbd1c9bbb60e869a4dfb4b371a5b\transformed\transport-runtime-2.2.6\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:2.2.1] E:\Android\GradleRepository\caches\8.11.1\transforms\3f854b1f8b54392d781fb5da4cbb1735\transformed\transport-api-2.2.1\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:2.2.1] E:\Android\GradleRepository\caches\8.11.1\transforms\3f854b1f8b54392d781fb5da4cbb1735\transformed\transport-api-2.2.1\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-components:16.1.0] E:\Android\GradleRepository\caches\8.11.1\transforms\63d073c0d12c3a5891d822cbdc2bacca\transformed\firebase-components-16.1.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-components:16.1.0] E:\Android\GradleRepository\caches\8.11.1\transforms\63d073c0d12c3a5891d822cbdc2bacca\transformed\firebase-components-16.1.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-encoders-json:17.1.0] E:\Android\GradleRepository\caches\8.11.1\transforms\7bc2732928b77e055d97d81424770056\transformed\firebase-encoders-json-17.1.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-encoders-json:17.1.0] E:\Android\GradleRepository\caches\8.11.1\transforms\7bc2732928b77e055d97d81424770056\transformed\firebase-encoders-json-17.1.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.alphacephei:vosk-android:0.3.45] E:\Android\GradleRepository\caches\8.11.1\transforms\c269beacc7b5440a3039b7022bce8905\transformed\vosk-android-0.3.45\AndroidManifest.xml:5:5-7:41
MERGED from [com.alphacephei:vosk-android:0.3.45] E:\Android\GradleRepository\caches\8.11.1\transforms\c269beacc7b5440a3039b7022bce8905\transformed\vosk-android-0.3.45\AndroidManifest.xml:5:5-7:41
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\53177004b6b735f0506f1ff457ffd742\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:20:5-44
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\53177004b6b735f0506f1ff457ffd742\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.odml:image:1.0.0-beta1] E:\Android\GradleRepository\caches\8.11.1\transforms\54413f5888ff2d62c093ed3ca74357b7\transformed\image-1.0.0-beta1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.odml:image:1.0.0-beta1] E:\Android\GradleRepository\caches\8.11.1\transforms\54413f5888ff2d62c093ed3ca74357b7\transformed\image-1.0.0-beta1\AndroidManifest.xml:5:5-44
	android:targetSdkVersion
		INJECTED from E:\AIHealthButler\app\src\main\AndroidManifest.xml
	tools:ignore
		ADDED from [com.google.accompanist:accompanist-drawablepainter:0.30.1] E:\Android\GradleRepository\caches\8.11.1\transforms\5959171c2bbdaa790da3d06f13761028\transformed\accompanist-drawablepainter-0.30.1\AndroidManifest.xml:23:9-39
	android:minSdkVersion
		INJECTED from E:\AIHealthButler\app\src\main\AndroidManifest.xml
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.6.0] E:\Android\GradleRepository\caches\8.11.1\transforms\85f7238b6a16c9bd9d8a581b064da59f\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.6.0] E:\Android\GradleRepository\caches\8.11.1\transforms\85f7238b6a16c9bd9d8a581b064da59f\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.6.0] E:\Android\GradleRepository\caches\8.11.1\transforms\85f7238b6a16c9bd9d8a581b064da59f\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
service#androidx.camera.core.impl.MetadataHolderService
ADDED from [androidx.camera:camera-camera2:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\c314f6fd8963a93598e7e827dca44cf2\transformed\camera-camera2-1.3.1\AndroidManifest.xml:24:9-33:19
MERGED from [androidx.camera:camera-core:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\3e370a08e3987525bf1b894c2d626a6f\transformed\camera-core-1.3.1\AndroidManifest.xml:29:9-33:78
MERGED from [androidx.camera:camera-core:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\3e370a08e3987525bf1b894c2d626a6f\transformed\camera-core-1.3.1\AndroidManifest.xml:29:9-33:78
	tools:node
		ADDED from [androidx.camera:camera-camera2:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\c314f6fd8963a93598e7e827dca44cf2\transformed\camera-camera2-1.3.1\AndroidManifest.xml:29:13-31
	android:enabled
		ADDED from [androidx.camera:camera-camera2:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\c314f6fd8963a93598e7e827dca44cf2\transformed\camera-camera2-1.3.1\AndroidManifest.xml:26:13-36
	android:exported
		ADDED from [androidx.camera:camera-camera2:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\c314f6fd8963a93598e7e827dca44cf2\transformed\camera-camera2-1.3.1\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.camera:camera-camera2:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\c314f6fd8963a93598e7e827dca44cf2\transformed\camera-camera2-1.3.1\AndroidManifest.xml:28:13-75
	android:name
		ADDED from [androidx.camera:camera-camera2:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\c314f6fd8963a93598e7e827dca44cf2\transformed\camera-camera2-1.3.1\AndroidManifest.xml:25:13-75
meta-data#androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER
ADDED from [androidx.camera:camera-camera2:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\c314f6fd8963a93598e7e827dca44cf2\transformed\camera-camera2-1.3.1\AndroidManifest.xml:30:13-32:89
	android:value
		ADDED from [androidx.camera:camera-camera2:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\c314f6fd8963a93598e7e827dca44cf2\transformed\camera-camera2-1.3.1\AndroidManifest.xml:32:17-86
	android:name
		ADDED from [androidx.camera:camera-camera2:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\c314f6fd8963a93598e7e827dca44cf2\transformed\camera-camera2-1.3.1\AndroidManifest.xml:31:17-103
service#com.google.mlkit.common.internal.MlKitComponentDiscoveryService
ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\f3d7a0950d8edd522d3a2e68917e9fd0\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.mlkit:vision-common:17.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\8c7116a2e8c72f5558ec46ba66165641\transformed\vision-common-17.3.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.mlkit:vision-common:17.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\8c7116a2e8c72f5558ec46ba66165641\transformed\vision-common-17.3.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.mlkit:common:18.9.0] E:\Android\GradleRepository\caches\8.11.1\transforms\856b4a7a015be168cc7fc8a4d2056f38\transformed\common-18.9.0\AndroidManifest.xml:15:9-23:19
MERGED from [com.google.mlkit:common:18.9.0] E:\Android\GradleRepository\caches\8.11.1\transforms\856b4a7a015be168cc7fc8a4d2056f38\transformed\common-18.9.0\AndroidManifest.xml:15:9-23:19
	android:exported
		ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\f3d7a0950d8edd522d3a2e68917e9fd0\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:11:13-37
	tools:targetApi
		ADDED from [com.google.mlkit:common:18.9.0] E:\Android\GradleRepository\caches\8.11.1\transforms\856b4a7a015be168cc7fc8a4d2056f38\transformed\common-18.9.0\AndroidManifest.xml:19:13-32
	android:directBootAware
		ADDED from [com.google.mlkit:common:18.9.0] E:\Android\GradleRepository\caches\8.11.1\transforms\856b4a7a015be168cc7fc8a4d2056f38\transformed\common-18.9.0\AndroidManifest.xml:17:13-43
	android:name
		ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\f3d7a0950d8edd522d3a2e68917e9fd0\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:10:13-91
meta-data#com.google.firebase.components:com.google.mlkit.vision.barcode.internal.BarcodeRegistrar
ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\f3d7a0950d8edd522d3a2e68917e9fd0\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\f3d7a0950d8edd522d3a2e68917e9fd0\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\f3d7a0950d8edd522d3a2e68917e9fd0\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:13:17-120
meta-data#com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar
ADDED from [com.google.mlkit:vision-common:17.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\8c7116a2e8c72f5558ec46ba66165641\transformed\vision-common-17.3.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.mlkit:vision-common:17.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\8c7116a2e8c72f5558ec46ba66165641\transformed\vision-common-17.3.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.mlkit:vision-common:17.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\8c7116a2e8c72f5558ec46ba66165641\transformed\vision-common-17.3.0\AndroidManifest.xml:13:17-124
provider#com.google.mlkit.common.internal.MlKitInitProvider
ADDED from [com.google.mlkit:common:18.9.0] E:\Android\GradleRepository\caches\8.11.1\transforms\856b4a7a015be168cc7fc8a4d2056f38\transformed\common-18.9.0\AndroidManifest.xml:9:9-13:38
	android:authorities
		ADDED from [com.google.mlkit:common:18.9.0] E:\Android\GradleRepository\caches\8.11.1\transforms\856b4a7a015be168cc7fc8a4d2056f38\transformed\common-18.9.0\AndroidManifest.xml:11:13-69
	android:exported
		ADDED from [com.google.mlkit:common:18.9.0] E:\Android\GradleRepository\caches\8.11.1\transforms\856b4a7a015be168cc7fc8a4d2056f38\transformed\common-18.9.0\AndroidManifest.xml:12:13-37
	android:initOrder
		ADDED from [com.google.mlkit:common:18.9.0] E:\Android\GradleRepository\caches\8.11.1\transforms\856b4a7a015be168cc7fc8a4d2056f38\transformed\common-18.9.0\AndroidManifest.xml:13:13-35
	android:name
		ADDED from [com.google.mlkit:common:18.9.0] E:\Android\GradleRepository\caches\8.11.1\transforms\856b4a7a015be168cc7fc8a4d2056f38\transformed\common-18.9.0\AndroidManifest.xml:10:13-78
meta-data#com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar
ADDED from [com.google.mlkit:common:18.9.0] E:\Android\GradleRepository\caches\8.11.1\transforms\856b4a7a015be168cc7fc8a4d2056f38\transformed\common-18.9.0\AndroidManifest.xml:20:13-22:85
	android:value
		ADDED from [com.google.mlkit:common:18.9.0] E:\Android\GradleRepository\caches\8.11.1\transforms\856b4a7a015be168cc7fc8a4d2056f38\transformed\common-18.9.0\AndroidManifest.xml:22:17-82
	android:name
		ADDED from [com.google.mlkit:common:18.9.0] E:\Android\GradleRepository\caches\8.11.1\transforms\856b4a7a015be168cc7fc8a4d2056f38\transformed\common-18.9.0\AndroidManifest.xml:21:17-120
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.1.0] E:\Android\GradleRepository\caches\8.11.1\transforms\ea1d6e38040e7b8436c098f4eca33857\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.1.0] E:\Android\GradleRepository\caches\8.11.1\transforms\ea1d6e38040e7b8436c098f4eca33857\transformed\play-services-base-18.1.0\AndroidManifest.xml:22:19-43
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.1.0] E:\Android\GradleRepository\caches\8.11.1\transforms\ea1d6e38040e7b8436c098f4eca33857\transformed\play-services-base-18.1.0\AndroidManifest.xml:21:19-78
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.1.0] E:\Android\GradleRepository\caches\8.11.1\transforms\ea1d6e38040e7b8436c098f4eca33857\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:19-85
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.1.0] E:\Android\GradleRepository\caches\8.11.1\transforms\237eff3a41ee89ac3ef69d6430961f46\transformed\play-services-basement-18.1.0\AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.1.0] E:\Android\GradleRepository\caches\8.11.1\transforms\237eff3a41ee89ac3ef69d6430961f46\transformed\play-services-basement-18.1.0\AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.1.0] E:\Android\GradleRepository\caches\8.11.1\transforms\237eff3a41ee89ac3ef69d6430961f46\transformed\play-services-basement-18.1.0\AndroidManifest.xml:22:13-58
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.6.0] E:\Android\GradleRepository\caches\8.11.1\transforms\d876ee35e176acdeab4ede638c85e11d\transformed\ui-test-manifest-1.6.0\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.6.0] E:\Android\GradleRepository\caches\8.11.1\transforms\d876ee35e176acdeab4ede638c85e11d\transformed\ui-test-manifest-1.6.0\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.6.0] E:\Android\GradleRepository\caches\8.11.1\transforms\d876ee35e176acdeab4ede638c85e11d\transformed\ui-test-manifest-1.6.0\AndroidManifest.xml:24:13-63
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\55d31107e6254ca36703c0e778ec1289\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] E:\Android\GradleRepository\caches\8.11.1\transforms\0fa4b7e7a7fef6254fbd677efdefa57e\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] E:\Android\GradleRepository\caches\8.11.1\transforms\0fa4b7e7a7fef6254fbd677efdefa57e\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\1f9356cc7725a92deea72a3b5f9be356\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\1f9356cc7725a92deea72a3b5f9be356\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] E:\Android\GradleRepository\caches\8.11.1\transforms\117cbf39a27888a52fbdcd897aacbe9d\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] E:\Android\GradleRepository\caches\8.11.1\transforms\117cbf39a27888a52fbdcd897aacbe9d\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\55d31107e6254ca36703c0e778ec1289\transformed\emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\55d31107e6254ca36703c0e778ec1289\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\55d31107e6254ca36703c0e778ec1289\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\55d31107e6254ca36703c0e778ec1289\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\55d31107e6254ca36703c0e778ec1289\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\55d31107e6254ca36703c0e778ec1289\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\55d31107e6254ca36703c0e778ec1289\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] E:\Android\GradleRepository\caches\8.11.1\transforms\0fa4b7e7a7fef6254fbd677efdefa57e\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] E:\Android\GradleRepository\caches\8.11.1\transforms\0fa4b7e7a7fef6254fbd677efdefa57e\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] E:\Android\GradleRepository\caches\8.11.1\transforms\0fa4b7e7a7fef6254fbd677efdefa57e\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.0.0] E:\Android\GradleRepository\caches\8.11.1\transforms\56ac8c8b02471706ed6c589469cdd72e\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
	android:required
		ADDED from [androidx.window:window:1.0.0] E:\Android\GradleRepository\caches\8.11.1\transforms\56ac8c8b02471706ed6c589469cdd72e\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] E:\Android\GradleRepository\caches\8.11.1\transforms\56ac8c8b02471706ed6c589469cdd72e\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.0.0] E:\Android\GradleRepository\caches\8.11.1\transforms\56ac8c8b02471706ed6c589469cdd72e\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
	android:required
		ADDED from [androidx.window:window:1.0.0] E:\Android\GradleRepository\caches\8.11.1\transforms\56ac8c8b02471706ed6c589469cdd72e\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] E:\Android\GradleRepository\caches\8.11.1\transforms\56ac8c8b02471706ed6c589469cdd72e\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] E:\Android\GradleRepository\caches\8.11.1\transforms\cd03543d47fe14dfe0d4dd3fa7052145\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] E:\Android\GradleRepository\caches\8.11.1\transforms\cd03543d47fe14dfe0d4dd3fa7052145\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] E:\Android\GradleRepository\caches\8.11.1\transforms\cd03543d47fe14dfe0d4dd3fa7052145\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
permission#com.example.aihealthbutler.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] E:\Android\GradleRepository\caches\8.11.1\transforms\cd03543d47fe14dfe0d4dd3fa7052145\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] E:\Android\GradleRepository\caches\8.11.1\transforms\cd03543d47fe14dfe0d4dd3fa7052145\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] E:\Android\GradleRepository\caches\8.11.1\transforms\cd03543d47fe14dfe0d4dd3fa7052145\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] E:\Android\GradleRepository\caches\8.11.1\transforms\cd03543d47fe14dfe0d4dd3fa7052145\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] E:\Android\GradleRepository\caches\8.11.1\transforms\cd03543d47fe14dfe0d4dd3fa7052145\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
uses-permission#com.example.aihealthbutler.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] E:\Android\GradleRepository\caches\8.11.1\transforms\cd03543d47fe14dfe0d4dd3fa7052145\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] E:\Android\GradleRepository\caches\8.11.1\transforms\cd03543d47fe14dfe0d4dd3fa7052145\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\1f9356cc7725a92deea72a3b5f9be356\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\1f9356cc7725a92deea72a3b5f9be356\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\1f9356cc7725a92deea72a3b5f9be356\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\1f9356cc7725a92deea72a3b5f9be356\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\1f9356cc7725a92deea72a3b5f9be356\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\1f9356cc7725a92deea72a3b5f9be356\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\1f9356cc7725a92deea72a3b5f9be356\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\1f9356cc7725a92deea72a3b5f9be356\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\1f9356cc7725a92deea72a3b5f9be356\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\1f9356cc7725a92deea72a3b5f9be356\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\1f9356cc7725a92deea72a3b5f9be356\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\1f9356cc7725a92deea72a3b5f9be356\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\1f9356cc7725a92deea72a3b5f9be356\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\1f9356cc7725a92deea72a3b5f9be356\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\1f9356cc7725a92deea72a3b5f9be356\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\1f9356cc7725a92deea72a3b5f9be356\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\1f9356cc7725a92deea72a3b5f9be356\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\1f9356cc7725a92deea72a3b5f9be356\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\1f9356cc7725a92deea72a3b5f9be356\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\1f9356cc7725a92deea72a3b5f9be356\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] E:\Android\GradleRepository\caches\8.11.1\transforms\1f9356cc7725a92deea72a3b5f9be356\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] E:\Android\GradleRepository\caches\8.11.1\transforms\e1b06c4bf7aa3bfb49d4614b0cbceabc\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:29:9-35:19
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] E:\Android\GradleRepository\caches\8.11.1\transforms\aaa1dbd1c9bbb60e869a4dfb4b371a5b\transformed\transport-runtime-2.2.6\AndroidManifest.xml:36:9-38:40
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] E:\Android\GradleRepository\caches\8.11.1\transforms\aaa1dbd1c9bbb60e869a4dfb4b371a5b\transformed\transport-runtime-2.2.6\AndroidManifest.xml:36:9-38:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] E:\Android\GradleRepository\caches\8.11.1\transforms\e1b06c4bf7aa3bfb49d4614b0cbceabc\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:31:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] E:\Android\GradleRepository\caches\8.11.1\transforms\e1b06c4bf7aa3bfb49d4614b0cbceabc\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:30:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] E:\Android\GradleRepository\caches\8.11.1\transforms\e1b06c4bf7aa3bfb49d4614b0cbceabc\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:32:13-34:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] E:\Android\GradleRepository\caches\8.11.1\transforms\e1b06c4bf7aa3bfb49d4614b0cbceabc\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:34:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] E:\Android\GradleRepository\caches\8.11.1\transforms\e1b06c4bf7aa3bfb49d4614b0cbceabc\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:33:17-94
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:2.2.6] E:\Android\GradleRepository\caches\8.11.1\transforms\aaa1dbd1c9bbb60e869a4dfb4b371a5b\transformed\transport-runtime-2.2.6\AndroidManifest.xml:26:9-30:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:2.2.6] E:\Android\GradleRepository\caches\8.11.1\transforms\aaa1dbd1c9bbb60e869a4dfb4b371a5b\transformed\transport-runtime-2.2.6\AndroidManifest.xml:28:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:2.2.6] E:\Android\GradleRepository\caches\8.11.1\transforms\aaa1dbd1c9bbb60e869a4dfb4b371a5b\transformed\transport-runtime-2.2.6\AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:2.2.6] E:\Android\GradleRepository\caches\8.11.1\transforms\aaa1dbd1c9bbb60e869a4dfb4b371a5b\transformed\transport-runtime-2.2.6\AndroidManifest.xml:27:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:2.2.6] E:\Android\GradleRepository\caches\8.11.1\transforms\aaa1dbd1c9bbb60e869a4dfb4b371a5b\transformed\transport-runtime-2.2.6\AndroidManifest.xml:32:9-34:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:2.2.6] E:\Android\GradleRepository\caches\8.11.1\transforms\aaa1dbd1c9bbb60e869a4dfb4b371a5b\transformed\transport-runtime-2.2.6\AndroidManifest.xml:34:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:2.2.6] E:\Android\GradleRepository\caches\8.11.1\transforms\aaa1dbd1c9bbb60e869a4dfb4b371a5b\transformed\transport-runtime-2.2.6\AndroidManifest.xml:33:13-132
uses-feature#android.hardware.camera
ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\53177004b6b735f0506f1ff457ffd742\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:25:5-27:36
	android:required
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\53177004b6b735f0506f1ff457ffd742\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:27:9-33
	android:name
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\53177004b6b735f0506f1ff457ffd742\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:26:9-47
uses-feature#android.hardware.camera.front
ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\53177004b6b735f0506f1ff457ffd742\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:28:5-30:36
	android:required
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\53177004b6b735f0506f1ff457ffd742\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:30:9-33
	android:name
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\53177004b6b735f0506f1ff457ffd742\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:29:9-53
uses-feature#android.hardware.camera.autofocus
ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\53177004b6b735f0506f1ff457ffd742\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:33:5-35:36
	android:required
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\53177004b6b735f0506f1ff457ffd742\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:35:9-33
	android:name
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\53177004b6b735f0506f1ff457ffd742\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:34:9-57
uses-feature#android.hardware.camera.flash
ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\53177004b6b735f0506f1ff457ffd742\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:36:5-38:36
	android:required
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\53177004b6b735f0506f1ff457ffd742\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:38:9-33
	android:name
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\53177004b6b735f0506f1ff457ffd742\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:37:9-53
uses-feature#android.hardware.screen.landscape
ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\53177004b6b735f0506f1ff457ffd742\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:39:5-41:36
	android:required
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\53177004b6b735f0506f1ff457ffd742\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:41:9-33
	android:name
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\53177004b6b735f0506f1ff457ffd742\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:40:9-57
uses-feature#android.hardware.wifi
ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\53177004b6b735f0506f1ff457ffd742\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:42:5-44:36
	android:required
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\53177004b6b735f0506f1ff457ffd742\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:44:9-33
	android:name
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\53177004b6b735f0506f1ff457ffd742\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:43:9-45
activity#com.journeyapps.barcodescanner.CaptureActivity
ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\53177004b6b735f0506f1ff457ffd742\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:47:9-53:63
	android:screenOrientation
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\53177004b6b735f0506f1ff457ffd742\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:50:13-56
	android:clearTaskOnLaunch
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\53177004b6b735f0506f1ff457ffd742\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:49:13-45
	android:stateNotNeeded
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\53177004b6b735f0506f1ff457ffd742\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:51:13-42
	android:windowSoftInputMode
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\53177004b6b735f0506f1ff457ffd742\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:53:13-60
	android:theme
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\53177004b6b735f0506f1ff457ffd742\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:52:13-54
	android:name
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] E:\Android\GradleRepository\caches\8.11.1\transforms\53177004b6b735f0506f1ff457ffd742\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:48:13-74
