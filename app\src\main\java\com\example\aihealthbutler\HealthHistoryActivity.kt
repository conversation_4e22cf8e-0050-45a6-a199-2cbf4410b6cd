package com.example.aihealthbutler

import android.os.Bundle
import android.content.Intent
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.background
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.DateRange
import androidx.compose.material.icons.filled.Delete
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.foundation.layout.PaddingValues
import com.example.aihealthbutler.ui.theme.AIHealthButlerTheme
import com.example.aihealthbutler.ui.components.CustomDeleteDialog
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.clickable
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.aihealthbutler.api.*
import android.util.Log
import android.widget.Toast
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.*
import java.util.Calendar

// 日期格式转换辅助函数
fun formatDateForDisplay(dateString: String?): String {
    if (dateString.isNullOrEmpty()) return ""

    return try {
        // 尝试解析ISO格式的日期 (2024-03-19T16:00:00.000+00:00)
        val isoFormat = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", Locale.getDefault())
        val displayFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())

        val date = try {
            isoFormat.parse(dateString)
        } catch (e: Exception) {
            // 如果ISO格式解析失败，尝试简单的日期格式
            val simpleFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
            simpleFormat.parse(dateString)
        }

        date?.let { displayFormat.format(it) } ?: dateString
    } catch (e: Exception) {
        // 如果所有格式都解析失败，返回原字符串
        dateString
    }
}

class HealthHistoryViewModel(
    private val repository: Repository,
    private val sessionManager: SessionManager
) : ViewModel() {
    private val TAG = "HealthHistoryVM"
    
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading
    
    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error

    private val _successMessage = MutableStateFlow<String?>(null)
    val successMessage: StateFlow<String?> = _successMessage

    private val _healthInfo = MutableStateFlow<HealthInfoResponse?>(null)
    val healthInfo: StateFlow<HealthInfoResponse?> = _healthInfo
    
    private val _surgeryHistories = MutableStateFlow<List<SurgeryHistory>>(emptyList())
    val surgeryHistories: StateFlow<List<SurgeryHistory>> = _surgeryHistories
    
    init {
        loadHealthInfo()
    }

    fun clearSuccessMessage() {
        _successMessage.value = null
    }
    
    private fun loadHealthInfo() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                _error.value = null
                
                // 获取token和defaultId
                val token = sessionManager.token.firstOrNull() ?: throw Exception("未登录")
                val defaultId = sessionManager.defaultId.firstOrNull() ?: throw Exception("未设置默认成员")

                Log.d(TAG, "请求健康信息 - defaultId: $defaultId")
                Log.d(TAG, "请求URL: https://qcx.yuneyang.top/api/health/$defaultId")

                // 获取健康信息
                val result = repository.getHealthInfo(token, defaultId)
                
                result.fold(
                    onSuccess = { response ->
                        if (response.code == 200) {
                            _healthInfo.value = response
                            response.data?.let { healthInfo ->
                                _surgeryHistories.value = healthInfo.surgeryHistories ?: emptyList()
                            }
                            _error.value = null
                        } else {
                            _error.value = response.message ?: "获取健康信息失败"
                        }
                    },
                    onFailure = { e ->
                        val errorMessage = when {
                            e.message?.contains("404") == true -> "暂无健康信息记录，请先创建健康档案"
                            e.message?.contains("网络") == true -> "网络连接异常，请检查网络设置"
                            else -> "获取健康信息失败，请稍后重试"
                        }
                        _error.value = errorMessage
                        Log.e(TAG, "获取健康信息失败", e)
                    }
                )
            } catch (e: Exception) {
                val errorMessage = when {
                    e.message?.contains("404") == true -> "暂无健康信息记录，请先创建健康档案"
                    e.message?.contains("网络") == true -> "网络连接异常，请检查网络设置"
                    else -> "获取健康信息失败，请稍后重试"
                }
                _error.value = errorMessage
                Log.e(TAG, "获取健康信息异常", e)
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    fun updateHealthInfo(
        medicalCondition: String,
        allergyHistory: String,
        vaccinationHistory: String,
        smokingStatus: String,
        drinkingStatus: String,
        familyMedicalHistory: String,
        menstrualStatus: String,
        pregnancyStatus: String
    ) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                _error.value = null
                
                // 获取token和defaultId
                val token = sessionManager.token.firstOrNull() ?: throw Exception("未登录")
                val defaultId = sessionManager.defaultId.firstOrNull() ?: throw Exception("未设置默认成员")

                Log.d(TAG, "更新健康信息 - token: ${token.take(20)}..., defaultId: $defaultId")

                // 获取当前健康信息
                val currentHealthInfo = _healthInfo.value?.data
                Log.d(TAG, "当前健康信息: $currentHealthInfo")
                
                if (currentHealthInfo != null) {
                    // 如果已有健康信息，则更新
                    val request = UpdateHealthRequest(
                        healthId = currentHealthInfo.healthId ?: 0,
                        defaultId = currentHealthInfo.defaultId ?: defaultId,
                        medicalCondition = medicalCondition,
                        allergyHistory = allergyHistory,
                        vaccinationHistory = vaccinationHistory,
                        smokingStatus = smokingStatus,
                        drinkingStatus = drinkingStatus,
                        familyMedicalHistory = familyMedicalHistory,
                        menstrualStatus = menstrualStatus,
                        pregnancyStatus = pregnancyStatus
                    )

                    Log.d(TAG, "更新健康信息请求: $request")

                    // 更新健康信息
                    val result = repository.updateHealthInfo(token, request)
                
                result.fold(
                    onSuccess = { response ->
                        if (response.code == 200) {
                            loadHealthInfo() // 重新加载健康信息
                            _error.value = null
                            _successMessage.value = "健康信息更新成功"
                        } else {
                            _error.value = response.message ?: "更新健康信息失败"
                        }
                    },
                    onFailure = { e ->
                        when {
                            e is retrofit2.HttpException -> {
                                when (e.code()) {
                                    500 -> {
                                        _error.value = "服务器内部错误，请检查数据格式"
                                        Log.e(TAG, "更新健康信息服务器错误(500)", e)
                                    }
                                    400 -> {
                                        _error.value = "请求参数错误，请检查输入数据"
                                        Log.e(TAG, "更新健康信息参数错误(400)", e)
                                    }
                                    401 -> {
                                        _error.value = "登录已过期，请重新登录"
                                        Log.e(TAG, "更新健康信息认证失败(401)", e)
                                    }
                                    else -> {
                                        _error.value = "更新健康信息失败: HTTP ${e.code()}"
                                        Log.e(TAG, "更新健康信息HTTP错误: ${e.code()}", e)
                                    }
                                }
                            }
                            else -> {
                                _error.value = "更新健康信息失败: ${e.message}"
                                Log.e(TAG, "更新健康信息失败", e)
                            }
                        }
                    }
                )
                } else {
                    // 如果没有健康信息，则创建新的
                    val request = CreateHealthRequest(
                        defaultId = defaultId,
                        medicalCondition = medicalCondition,
                        allergyHistory = allergyHistory,
                        vaccinationHistory = vaccinationHistory,
                        smokingStatus = smokingStatus,
                        drinkingStatus = drinkingStatus,
                        familyMedicalHistory = familyMedicalHistory,
                        menstrualStatus = menstrualStatus,
                        pregnancyStatus = pregnancyStatus
                    )

                    Log.d(TAG, "创建健康信息请求: $request")

                    // 创建健康信息
                    val result = repository.createHealthInfo(token, request)
                    
                    result.fold(
                        onSuccess = { response ->
                            if (response.code == 200) {
                                loadHealthInfo() // 重新加载健康信息
                                _error.value = null
                                _successMessage.value = "健康信息创建成功"
                            } else {
                                _error.value = response.message ?: "创建健康信息失败"
                            }
                        },
                        onFailure = { e ->
                            when {
                                e is retrofit2.HttpException -> {
                                    when (e.code()) {
                                        500 -> {
                                            _error.value = "服务器内部错误，请检查数据格式"
                                            Log.e(TAG, "创建健康信息服务器错误(500)", e)
                                        }
                                        400 -> {
                                            _error.value = "请求参数错误，请检查输入数据"
                                            Log.e(TAG, "创建健康信息参数错误(400)", e)
                                        }
                                        401 -> {
                                            _error.value = "登录已过期，请重新登录"
                                            Log.e(TAG, "创建健康信息认证失败(401)", e)
                                        }
                                        else -> {
                                            _error.value = "创建健康信息失败: HTTP ${e.code()}"
                                            Log.e(TAG, "创建健康信息HTTP错误: ${e.code()}", e)
                                        }
                                    }
                                }
                                else -> {
                                    _error.value = "创建健康信息失败: ${e.message}"
                                    Log.e(TAG, "创建健康信息失败", e)
                                }
                            }
                        }
                    )
                }
            } catch (e: Exception) {
                _error.value = "更新健康信息异常: ${e.message}"
                Log.e(TAG, "更新健康信息异常", e)
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    fun ensureHealthInfoExists(callback: (Int) -> Unit) {
        viewModelScope.launch {
            try {
                val currentHealthInfo = _healthInfo.value?.data
                if (currentHealthInfo != null && (currentHealthInfo.healthId ?: 0) > 0) {
                    callback(currentHealthInfo.healthId ?: 0)
                    return@launch
                }

                // 如果没有健康信息，创建一个基础的
                val token = sessionManager.token.firstOrNull() ?: throw Exception("未登录")
                val defaultId = sessionManager.defaultId.firstOrNull() ?: throw Exception("未设置默认成员")

                val request = CreateHealthRequest(
                    defaultId = defaultId,
                    medicalCondition = "无",
                    allergyHistory = "无",
                    vaccinationHistory = "无",
                    smokingStatus = "从不",
                    drinkingStatus = "从不",
                    familyMedicalHistory = "无",
                    menstrualStatus = "规律",
                    pregnancyStatus = "否"
                )

                val result = repository.createHealthInfo(token, request)
                result.fold(
                    onSuccess = { response ->
                        if (response.code == 200) {
                            loadHealthInfo() // 重新加载健康信息
                            // 等待加载完成后获取healthId
                            val newHealthInfo = _healthInfo.value?.data
                            if (newHealthInfo != null) {
                                callback(newHealthInfo.healthId ?: 0)
                            }
                        } else {
                            _error.value = "创建健康信息失败: ${response.message}"
                        }
                    },
                    onFailure = { e ->
                        _error.value = "创建健康信息失败: ${e.message}"
                        Log.e(TAG, "创建健康信息失败", e)
                    }
                )
            } catch (e: Exception) {
                _error.value = "创建健康信息异常: ${e.message}"
                Log.e(TAG, "创建健康信息异常", e)
            }
        }
    }

    fun createSurgeryHistory(request: CreateSurgeryRequest) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                _error.value = null

                // 获取token
                val token = sessionManager.token.firstOrNull() ?: throw Exception("未登录")

                // 确保请求参数有效
                if (request.healthId <= 0) {
                    _error.value = "无效的健康记录ID，请先创建健康信息"
                    _isLoading.value = false
                    return@launch
                }

                if (request.surgeryName.isBlank()) {
                    _error.value = "手术名称不能为空"
                    _isLoading.value = false
                    return@launch
                }

                if (request.surgeryDate.isBlank()) {
                    _error.value = "手术日期不能为空"
                    _isLoading.value = false
                    return@launch
                }

                // 验证日期格式
                try {
                    val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
                    dateFormat.isLenient = false
                    dateFormat.parse(request.surgeryDate)
                } catch (e: Exception) {
                    _error.value = "日期格式错误，请使用 yyyy-MM-dd 格式"
                    _isLoading.value = false
                    return@launch
                }

                Log.d(TAG, "创建手术史请求: $request")
                
                // 创建手术史记录
                val result = repository.createSurgeryHistory(token, request)
                
                result.fold(
                    onSuccess = { response ->
                        if (response.code == 200) {
                            loadHealthInfo() // 重新加载健康信息
                            _error.value = null
                        } else {
                            _error.value = response.message ?: "创建手术史记录失败"
                        }
                    },
                    onFailure = { e ->
                        // 处理HTTP错误
                        when {
                            e is retrofit2.HttpException -> {
                                when (e.code()) {
                                    500 -> {
                                        _error.value = "服务器内部错误，请检查数据格式或稍后再试"
                                        Log.e(TAG, "服务器内部错误(500)", e)
                                    }
                                    400 -> {
                                        _error.value = "请求参数错误，请检查输入数据"
                                        Log.e(TAG, "请求参数错误(400)", e)
                                    }
                                    401 -> {
                                        _error.value = "登录已过期，请重新登录"
                                        Log.e(TAG, "认证失败(401)", e)
                                    }
                                    else -> {
                                        _error.value = "创建手术史记录失败: HTTP ${e.code()}"
                                        Log.e(TAG, "HTTP错误: ${e.code()}", e)
                                    }
                                }
                            }
                            else -> {
                                _error.value = "创建手术史记录失败: ${e.message}"
                                Log.e(TAG, "创建手术史记录失败", e)
                            }
                        }
                    }
                )
            } catch (e: Exception) {
                _error.value = "创建手术史记录异常: ${e.message}"
                Log.e(TAG, "创建手术史记录异常", e)
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    fun updateSurgeryHistory(request: UpdateSurgeryRequest) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                _error.value = null
                
                // 获取token
                val token = sessionManager.token.firstOrNull() ?: throw Exception("未登录")
                
                // 更新手术史记录
                val result = repository.updateSurgeryHistory(token, request)
                
                result.fold(
                    onSuccess = { response ->
                        if (response.code == 200) {
                            loadHealthInfo() // 重新加载健康信息
                            _error.value = null
                        } else {
                            _error.value = response.message ?: "更新手术史记录失败"
                        }
                    },
                    onFailure = { e ->
                        _error.value = "更新手术史记录失败: ${e.message}"
                        Log.e(TAG, "更新手术史记录失败", e)
                    }
                )
            } catch (e: Exception) {
                _error.value = "更新手术史记录异常: ${e.message}"
                Log.e(TAG, "更新手术史记录异常", e)
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    fun deleteSurgeryHistory(surgeryId: Int) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                _error.value = null
                
                // 获取token
                val token = sessionManager.token.firstOrNull() ?: throw Exception("未登录")
                
                // 删除手术史记录
                val result = repository.deleteSurgeryHistory(token, surgeryId)
                
                result.fold(
                    onSuccess = { response ->
                        if (response.code == 200) {
                            loadHealthInfo() // 重新加载健康信息
                            _error.value = null
                        } else {
                            _error.value = response.message ?: "删除手术史记录失败"
                        }
                    },
                    onFailure = { e ->
                        _error.value = "删除手术史记录失败: ${e.message}"
                        Log.e(TAG, "删除手术史记录失败", e)
                    }
                )
            } catch (e: Exception) {
                _error.value = "删除手术史记录异常: ${e.message}"
                Log.e(TAG, "删除手术史记录异常", e)
            } finally {
                _isLoading.value = false
            }
        }
    }
}

class HealthHistoryActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            AIHealthButlerTheme {
                val sessionManager = remember { SessionManager(applicationContext) }
                val repository = remember { Repository() }
                val viewModel = remember { HealthHistoryViewModel(repository, sessionManager) }
                
                HealthHistoryScreen(
                    viewModel = viewModel,
                    onBack = {
                        // 返回主页面
                        startActivity(Intent(this, MainActivity::class.java))
                        finish()
                    }
                )
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HealthHistoryScreen(
    viewModel: HealthHistoryViewModel,
    onBack: () -> Unit
) {
    val context = LocalContext.current
    val scrollState = rememberScrollState()
    
    // 收集ViewModel中的状态
    val healthInfo by viewModel.healthInfo.collectAsState()
    val isLoading by viewModel.isLoading.collectAsState()
    val error by viewModel.error.collectAsState()
    val surgeryHistories by viewModel.surgeryHistories.collectAsState()
    
    // 添加所有输入框的状态
    var diseaseState by remember { mutableStateOf("") }
    var diagnosisDateState by remember { mutableStateOf("") }
    var allergyState by remember { mutableStateOf("") }
    var vaccineState by remember { mutableStateOf("") }
    var familyHistoryState by remember { mutableStateOf("") }
    var menstrualFlowState by remember { mutableStateOf("") }
    var menstrualPainState by remember { mutableStateOf("") }
    var menstrualCycleState by remember { mutableStateOf("") }
    var pregnancyState by remember { mutableStateOf("") }
    var periodState by remember { mutableStateOf("") }
    
    // 添加有/无状态变量声明
    var hasDiseaseState by remember { mutableStateOf(false) }
    var hasAllergyState by remember { mutableStateOf(false) }
    var hasVaccineState by remember { mutableStateOf(false) }
    var hasFamilyHistoryState by remember { mutableStateOf(false) }
    var hasSmokingState by remember { mutableStateOf(false) }
    var hasAlcoholState by remember { mutableStateOf(false) }
    var hasPregnancyState by remember { mutableStateOf(false) }
    var hasMenstrualState by remember { mutableStateOf(false) }
    
    // 监听ViewModel中的数据变化
    LaunchedEffect(healthInfo?.data, surgeryHistories) {
        healthInfo?.data?.let { info ->
            diseaseState = info.medicalCondition ?: ""
            allergyState = info.allergyHistory ?: ""
            vaccineState = info.vaccinationHistory ?: ""
            familyHistoryState = info.familyMedicalHistory ?: ""
            periodState = info.menstrualStatus ?: ""
            pregnancyState = info.pregnancyStatus ?: ""
            
            // 更新有/无状态
            hasDiseaseState = info.medicalCondition?.isNotEmpty() == true
            hasAllergyState = info.allergyHistory?.isNotEmpty() == true
            hasVaccineState = info.vaccinationHistory?.isNotEmpty() == true
            hasFamilyHistoryState = info.familyMedicalHistory?.isNotEmpty() == true
            hasSmokingState = info.smokingStatus == "吸烟"
            hasAlcoholState = info.drinkingStatus == "饮酒"
            hasPregnancyState = info.pregnancyStatus?.isNotEmpty() == true
            
            // 拆分月经信息
            val periodParts = info.menstrualStatus?.split("|") ?: listOf()
            if (periodParts.size >= 3) {
                menstrualFlowState = periodParts[0]
                menstrualPainState = periodParts[1]
                menstrualCycleState = periodParts[2]
                hasMenstrualState = true
            }
        }
        
        // 更新手术日期
        diagnosisDateState = surgeryHistories.firstOrNull()?.surgeryDate ?: ""
    }
    
    // 处理错误提示
    LaunchedEffect(error) {
        error?.let { errorMsg ->
            Toast.makeText(context, errorMsg, Toast.LENGTH_SHORT).show()
        }
    }

    // 处理成功消息
    val successMessage by viewModel.successMessage.collectAsState()
    LaunchedEffect(successMessage) {
        successMessage?.let { successMsg ->
            Toast.makeText(context, successMsg, Toast.LENGTH_SHORT).show()
            // 清除成功消息
            viewModel.clearSuccessMessage()
        }
    }
    
    // 日期选择器状态
    var showDatePicker by remember { mutableStateOf(false) }
    val calendar = remember { Calendar.getInstance() }
    val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
    
    // 当前选择的年月
    var currentYear by remember { mutableStateOf(calendar.get(Calendar.YEAR)) }
    var currentMonth by remember { mutableStateOf(calendar.get(Calendar.MONTH)) }
    
    // 计算获取当前月的天数
    val daysInMonth = remember(currentYear, currentMonth) {
        val cal = Calendar.getInstance()
        cal.set(currentYear, currentMonth, 1)
        cal.getActualMaximum(Calendar.DAY_OF_MONTH)
    }
    
    // 获取当前月第一天是星期几
    val firstDayOfMonth = remember(currentYear, currentMonth) {
        val cal = Calendar.getInstance()
        cal.set(currentYear, currentMonth, 1)
        cal.get(Calendar.DAY_OF_WEEK) - 1 // 调整为从0开始
    }

    // 手术史相关状态
    var showAddSurgeryDialog by remember { mutableStateOf(false) }
    var showEditSurgeryDialog by remember { mutableStateOf(false) }
    var selectedSurgery by remember { mutableStateOf<SurgeryHistory?>(null) }
    
    Scaffold(
        topBar = {
            CenterAlignedTopAppBar(
                title = {
                    Text(
                        text = "健康史",
                        fontSize = 20.sp,
                        fontWeight = FontWeight.Bold
                    )
                },
                navigationIcon = {
                    IconButton(onClick = onBack) {
                        Icon(
                            painter = painterResource(id = R.drawable.left_arrow),
                            contentDescription = "返回",
                            modifier = Modifier.size(24.dp)
                        )
                    }
                },
                colors = TopAppBarDefaults.centerAlignedTopAppBarColors(
                    containerColor = Color.White
                )
            )
        }
    ) { innerPadding ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(innerPadding)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .background(Color.White)
                    .padding(horizontal = 16.dp)
                    .verticalScroll(scrollState)
            ) {
                // 既往史部分
                Text(
                    text = "既往史",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier.padding(vertical = 16.dp)
                )
                
                ExpandableHistoryItem(
                    icon = R.drawable.ic_medical_history,
                    title = "既往患病情况",
                    isExpanded = hasDiseaseState,
                    onExpandChange = { hasDiseaseState = !hasDiseaseState },
                    itemType = "toggle",
                    expandedContent = {
                        Column(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(start = 20.dp, top = 5.dp, end = 10.dp)
                        ) {
                            OutlinedTextField(
                                value = diseaseState,
                                onValueChange = { 
                                    diseaseState = it 
                                    // 自动更新状态
                                    if (it.isNotEmpty()) {
                                        hasDiseaseState = true
                                    }
                                },
                                modifier = Modifier.fillMaxWidth(),
                                label = { Text("既往病名称") },
                                colors = OutlinedTextFieldDefaults.colors(
                                    unfocusedBorderColor = Color(0xFFE0E0E0),
                                    focusedBorderColor = Color(0xFF22C1C3)
                                )
                            )
                            
                            Spacer(modifier = Modifier.height(8.dp))
                            
                            OutlinedTextField(
                                value = diagnosisDateState,
                                onValueChange = { 
                                    diagnosisDateState = it
                                    // 自动更新状态
                                    if (it.isNotEmpty()) {
                                        hasDiseaseState = true
                                    }
                                },
                                modifier = Modifier.fillMaxWidth(),
                                label = { Text("确诊时间") },
                                trailingIcon = {
                                    Icon(
                                        painter = painterResource(id = R.drawable.ic_calendar),
                                        contentDescription = "选择日期",
                                        modifier = Modifier
                                            .size(20.dp)
                                            .clickable { showDatePicker = true }
                                    )
                                },
                                colors = OutlinedTextFieldDefaults.colors(
                                    unfocusedBorderColor = Color(0xFFE0E0E0),
                                    focusedBorderColor = Color(0xFF22C1C3)
                                ),
                                readOnly = true
                            )
                        }
                    }
                )
                
                ExpandableHistoryItem(
                    icon = R.drawable.ic_allergy,
                    title = "食物、药物过敏情况",
                    isExpanded = hasAllergyState,
                    onExpandChange = { hasAllergyState = !hasAllergyState },
                    itemType = "toggle",
                    expandedContent = {
                        SimpleInputContent(
                            value = allergyState,
                            onValueChange = { 
                                allergyState = it
                                // 自动更新状态
                                if (it.isNotEmpty()) {
                                    hasAllergyState = true
                                }
                            },
                            label = "过敏原名称"
                        )
                    }
                )
                
                ExpandableHistoryItem(
                    icon = R.drawable.ic_vaccine,
                    title = "预防接种情况",
                    isExpanded = hasVaccineState,
                    onExpandChange = { hasVaccineState = !hasVaccineState },
                    itemType = "toggle",
                    expandedContent = {
                        SimpleInputContent(
                            value = vaccineState,
                            onValueChange = { 
                                vaccineState = it
                                // 自动更新状态
                                if (it.isNotEmpty()) {
                                    hasVaccineState = true
                                }
                            },
                            label = "疫苗名称"
                        )
                    }
                )

                // 个人史部分
                Text(
                    text = "个人史",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier.padding(vertical = 16.dp)
                )
                
                ExpandableHistoryItem(
                    icon = R.drawable.ic_smoking,
                    title = "吸烟情况",
                    isExpanded = hasSmokingState,
                    onExpandChange = { hasSmokingState = !hasSmokingState },
                    itemType = "toggle"
                )
                
                ExpandableHistoryItem(
                    icon = R.drawable.ic_alcohol,
                    title = "饮酒情况",
                    isExpanded = hasAlcoholState,
                    onExpandChange = { hasAlcoholState = !hasAlcoholState },
                    itemType = "toggle"
                )

                // 家族史部分
                Text(
                    text = "家族史",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier.padding(vertical = 16.dp)
                )
                
                ExpandableHistoryItem(
                    icon = R.drawable.ic_family,
                    title = "家族患病情况",
                    isExpanded = hasFamilyHistoryState,
                    onExpandChange = { hasFamilyHistoryState = !hasFamilyHistoryState },
                    itemType = "toggle",
                    expandedContent = {
                        SimpleInputContent(
                            value = familyHistoryState,
                            onValueChange = { 
                                familyHistoryState = it
                                // 自动更新状态
                                if (it.isNotEmpty()) {
                                    hasFamilyHistoryState = true
                                }
                            },
                            label = "家族病史"
                        )
                    }
                )

                // 月经史部分
                Text(
                    text = "月经史",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier.padding(vertical = 16.dp)
                )
                
                ExpandableHistoryItem(
                    icon = R.drawable.ic_menstrual,
                    title = "月经情况",
                    isExpanded = hasMenstrualState,
                    onExpandChange = { hasMenstrualState = !hasMenstrualState },
                    itemType = "toggle",
                    expandedContent = {
                        Column(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(start = 20.dp, top = 5.dp, end = 10.dp)
                        ) {
                            OutlinedTextField(
                                value = menstrualFlowState,
                                onValueChange = { 
                                    menstrualFlowState = it
                                    // 自动更新状态
                                    if (it.isNotEmpty()) {
                                        hasMenstrualState = true
                                    }
                                },
                                modifier = Modifier.fillMaxWidth(),
                                label = { Text("月经量") },
                                colors = OutlinedTextFieldDefaults.colors(
                                    unfocusedBorderColor = Color(0xFFE0E0E0),
                                    focusedBorderColor = Color(0xFF22C1C3)
                                )
                            )
                            
                            Spacer(modifier = Modifier.height(8.dp))
                            
                            OutlinedTextField(
                                value = menstrualPainState,
                                onValueChange = { 
                                    menstrualPainState = it
                                    // 自动更新状态
                                    if (it.isNotEmpty()) {
                                        hasMenstrualState = true
                                    }
                                },
                                modifier = Modifier.fillMaxWidth(),
                                label = { Text("痛经情况") },
                                colors = OutlinedTextFieldDefaults.colors(
                                    unfocusedBorderColor = Color(0xFFE0E0E0),
                                    focusedBorderColor = Color(0xFF22C1C3)
                                )
                            )
                            
                            Spacer(modifier = Modifier.height(8.dp))
                            
                            OutlinedTextField(
                                value = menstrualCycleState,
                                onValueChange = { 
                                    menstrualCycleState = it
                                    // 自动更新状态
                                    if (it.isNotEmpty()) {
                                        hasMenstrualState = true
                                    }
                                },
                                modifier = Modifier.fillMaxWidth(),
                                label = { Text("月经周期") },
                                colors = OutlinedTextFieldDefaults.colors(
                                    unfocusedBorderColor = Color(0xFFE0E0E0),
                                    focusedBorderColor = Color(0xFF22C1C3)
                                )
                            )
                        }
                    }
                )

                // 孕产信息部分
                Text(
                    text = "孕产信息",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier.padding(vertical = 16.dp)
                )
                
                ExpandableHistoryItem(
                    icon = R.drawable.ic_pregnancy,
                    title = "当前状态",
                    isExpanded = hasPregnancyState,
                    onExpandChange = { hasPregnancyState = !hasPregnancyState },
                    itemType = "toggle",
                    expandedContent = {
                        SimpleInputContent(
                            value = pregnancyState,
                            onValueChange = { 
                                pregnancyState = it
                                // 自动更新状态
                                if (it.isNotEmpty()) {
                                    hasPregnancyState = true
                                }
                            },
                            label = "请描述当前怀孕状态"
                        )
                    }
                )

                // 手术史部分
                Text(
                    text = "手术史",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier.padding(vertical = 16.dp)
                )
                
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 16.dp),
                    shape = RoundedCornerShape(12.dp),
                    colors = CardDefaults.cardColors(containerColor = Color.White),
                    border = BorderStroke(1.dp, Color(0xFFE0E0E0))
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp)
                    ) {
                        // 添加手术史按钮
                        Button(
                            onClick = { showAddSurgeryDialog = true },
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(48.dp),
                            colors = ButtonDefaults.buttonColors(
                                containerColor = Color(0xFF22C1C3)
                            ),
                            shape = RoundedCornerShape(8.dp)
                        ) {
                            Icon(
                                painter = painterResource(id = R.drawable.ic_add),
                                contentDescription = "添加",
                                tint = Color.White,
                                modifier = Modifier.size(20.dp)
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text("添加手术史记录", color = Color.White)
                        }
                        
                        // 手术史列表
                        if (surgeryHistories.isEmpty()) {
                            Text(
                                text = "暂无手术史记录",
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(vertical = 16.dp),
                                textAlign = TextAlign.Center,
                                color = Color.Gray
                            )
                        } else {
                            surgeryHistories.forEach { surgery ->
                                SurgeryHistoryItem(
                                    surgery = surgery,
                                    onEdit = {
                                        selectedSurgery = surgery
                                        showEditSurgeryDialog = true
                                    },
                                    onDelete = {
                                        surgery.surgeryId?.let { id ->
                                            viewModel.deleteSurgeryHistory(id)
                                        }
                                    }
                                )
                            }
                        }
                    }
                }

                Spacer(modifier = Modifier.weight(1f))
                // 提示文字
                Text(
                    text = "完善您的健康史信息，以获得专属健康建议",
                    fontSize = 14.sp,
                    color = Color(0xFF666666),
                    modifier = Modifier
                        .padding(horizontal = 10.dp, vertical = 16.dp)
                        .fillMaxWidth(),
                    textAlign = TextAlign.Center
                )
                // 提交按钮
                Button(
                    onClick = {
                        // 处理月经信息，合并为一个字符串
                        val combinedPeriod = if (hasMenstrualState && (menstrualFlowState.isNotEmpty() || menstrualPainState.isNotEmpty() || menstrualCycleState.isNotEmpty())) {
                            "${menstrualFlowState}|${menstrualPainState}|${menstrualCycleState}"
                        } else {
                            "有且正常"
                        }
                        
                        // 准备提交的数据
                        val medicalConditionValue = if (hasDiseaseState && diseaseState.isNotBlank()) diseaseState else "无"
                        val allergyHistoryValue = if (hasAllergyState && allergyState.isNotBlank()) allergyState else "无"
                        val vaccinationHistoryValue = if (hasVaccineState && vaccineState.isNotBlank()) vaccineState else "无"
                        val smokingStatusValue = if (hasSmokingState) "吸烟" else "从不"
                        val drinkingStatusValue = if (hasAlcoholState) "饮酒" else "从不"
                        val familyMedicalHistoryValue = if (hasFamilyHistoryState && familyHistoryState.isNotBlank()) familyHistoryState else "无"
                        val pregnancyStatusValue = if (hasPregnancyState && pregnancyState.isNotBlank()) pregnancyState else "否"

                        Log.d("HealthHistoryUI", "提交健康信息:")
                        Log.d("HealthHistoryUI", "  疾病史: $medicalConditionValue")
                        Log.d("HealthHistoryUI", "  过敏史: $allergyHistoryValue")
                        Log.d("HealthHistoryUI", "  疫苗史: $vaccinationHistoryValue")
                        Log.d("HealthHistoryUI", "  吸烟状态: $smokingStatusValue")
                        Log.d("HealthHistoryUI", "  饮酒状态: $drinkingStatusValue")
                        Log.d("HealthHistoryUI", "  家族病史: $familyMedicalHistoryValue")
                        Log.d("HealthHistoryUI", "  月经状态: $combinedPeriod")
                        Log.d("HealthHistoryUI", "  怀孕状态: $pregnancyStatusValue")

                        // 调用ViewModel更新数据
                        viewModel.updateHealthInfo(
                            medicalCondition = medicalConditionValue,
                            allergyHistory = allergyHistoryValue,
                            vaccinationHistory = vaccinationHistoryValue,
                            smokingStatus = smokingStatusValue,
                            drinkingStatus = drinkingStatusValue,
                            familyMedicalHistory = familyMedicalHistoryValue,
                            menstrualStatus = combinedPeriod,
                            pregnancyStatus = pregnancyStatusValue
                        )
                    },
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 24.dp)
                        .height(50.dp),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = Color(0xFF22C1C3)
                    ),
                    enabled = !isLoading
                ) {
                    if (isLoading) {
                        CircularProgressIndicator(
                            color = Color.White,
                            strokeWidth = 2.dp,
                            modifier = Modifier.size(24.dp)
                        )
                    } else {
                        Text(
                            text = "提交",
                            fontSize = 18.sp,
                            color = Color.White
                        )
                    }
                }
            }
            
            // 日期选择器对话框
            if (showDatePicker) {
                Dialog(
                    onDismissRequest = { showDatePicker = false },
                    properties = DialogProperties(dismissOnClickOutside = true)
                ) {
                    Card(
                        modifier = Modifier
                            .fillMaxWidth(0.95f)
                            .padding(horizontal = 0.dp, vertical = 8.dp),
                        shape = RoundedCornerShape(16.dp),
                        colors = CardDefaults.cardColors(containerColor = Color.White),
                        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
                    ) {
                        Column(
                            modifier = Modifier
                                .padding(horizontal = 16.dp, vertical = 16.dp)
                        ) {
                            // 月份和年份导航
                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(horizontal = 8.dp),
                                verticalAlignment = Alignment.CenterVertically,
                                horizontalArrangement = Arrangement.SpaceBetween
                            ) {
                                IconButton(
                                    onClick = { currentYear-- },
                                    modifier = Modifier.size(40.dp)
                            ) {
                                Text(
                                    text = "<<",
                                        fontSize = 18.sp,
                                        color = Color(0xFF22C1C3),
                                        fontWeight = FontWeight.Bold
                                    )
                                }
                                
                                IconButton(
                                    onClick = {
                                            if (currentMonth == 0) {
                                                currentMonth = 11
                                                currentYear--
                                            } else {
                                                currentMonth--
                                            }
                                    },
                                    modifier = Modifier.size(40.dp)
                                ) {
                                    Text(
                                        text = "<",
                                        fontSize = 18.sp,
                                        color = Color(0xFF22C1C3),
                                        fontWeight = FontWeight.Bold
                                    )
                                }
                                
                                Text(
                                    text = "${currentYear}年${currentMonth + 1}月",
                                    fontSize = 18.sp,
                                    fontWeight = FontWeight.Bold,
                                    color = Color(0xFF303030)
                                )
                                
                                IconButton(
                                    onClick = {
                                            if (currentMonth == 11) {
                                                currentMonth = 0
                                                currentYear++
                                            } else {
                                                currentMonth++
                                            }
                                    },
                                    modifier = Modifier.size(40.dp)
                                ) {
                                    Text(
                                        text = ">",
                                        fontSize = 18.sp,
                                        color = Color(0xFF22C1C3),
                                        fontWeight = FontWeight.Bold
                                    )
                                }
                                
                                IconButton(
                                    onClick = { currentYear++ },
                                    modifier = Modifier.size(40.dp)
                                ) {
                                Text(
                                    text = ">>",
                                        fontSize = 18.sp,
                                        color = Color(0xFF22C1C3),
                                        fontWeight = FontWeight.Bold
                                )
                                }
                            }
                            
                            Spacer(modifier = Modifier.height(12.dp))
                            
                            // 星期标题
                            Row(modifier = Modifier.fillMaxWidth()) {
                                listOf("日", "一", "二", "三", "四", "五", "六").forEach { day ->
                                    Box(
                                        modifier = Modifier.weight(1f),
                                        contentAlignment = Alignment.Center
                                    ) {
                                        Text(
                                            text = day,
                                            fontSize = 16.sp,
                                            fontWeight = FontWeight.Medium,
                                            color = Color(0xFF505050)
                                        )
                                    }
                                }
                            }
                            
                            Divider(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(vertical = 6.dp),
                                color = Color(0xFFE0E0E0),
                                thickness = 1.dp
                            )
                            
                            // 日期网格
                            val rows = (daysInMonth + firstDayOfMonth + 6) / 7
                            for (row in 0 until rows) {
                                Row(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(vertical = 4.dp)
                                ) {
                                    for (col in 0 until 7) {
                                        val day = row * 7 + col - firstDayOfMonth + 1
                                        Box(
                                            modifier = Modifier
                                                .weight(1f)
                                                .padding(2.dp)
                                                .height(32.dp),
                                            contentAlignment = Alignment.Center
                                        ) {
                                            if (day in 1..daysInMonth) {
                                                val isSelected = try {
                                                    val selectedCalendar = Calendar.getInstance().apply {
                                                        time = if (diagnosisDateState.isEmpty()) Date() else dateFormat.parse(diagnosisDateState) ?: Date()
                                                    }
                                                    day == selectedCalendar.get(Calendar.DAY_OF_MONTH) &&
                                                    currentMonth == selectedCalendar.get(Calendar.MONTH) &&
                                                    currentYear == selectedCalendar.get(Calendar.YEAR)
                                                } catch (e: Exception) {
                                                    false
                                                }
                                                
                                                Box(
                                                    modifier = Modifier
                                                        .size(36.dp)
                                                        .background(
                                                            if (isSelected) Color(0xFF22C1C3) else Color.Transparent,
                                                            shape = androidx.compose.foundation.shape.CircleShape
                                                        )
                                                        .clickable {
                                                            calendar.set(currentYear, currentMonth, day)
                                                            diagnosisDateState = dateFormat.format(calendar.time)
                                                            showDatePicker = false
                                                        },
                                                    contentAlignment = Alignment.Center
                                                ) {
                                                    Text(
                                                        text = day.toString(),
                                                        color = if (isSelected) Color.White else Color(0xFF303030),
                                                        fontSize = 16.sp,
                                                        fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Normal
                                                    )
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            
                            // 底部按钮
                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(top = 16.dp),
                                horizontalArrangement = Arrangement.End
                            ) {
                                TextButton(
                                    onClick = { showDatePicker = false }
                                ) {
                                    Text(
                                        text = "取消",
                                        color = Color.Gray,
                                        fontSize = 16.sp
                                    )
                                }
                                
                                Spacer(modifier = Modifier.width(8.dp))
                                
                                Button(
                                    onClick = {
                                        // 使用当前日期
                                        diagnosisDateState = dateFormat.format(Calendar.getInstance().time)
                                        showDatePicker = false
                                    },
                                    colors = ButtonDefaults.buttonColors(containerColor = Color(0xFF22C1C3))
                                ) {
                                    Text(
                                        text = "确定",
                                        color = Color.White,
                                        fontSize = 16.sp
                                    )
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    
    // 添加手术史对话框
    if (showAddSurgeryDialog) {
        SurgeryDialog(
            surgery = null,
            onDismiss = { showAddSurgeryDialog = false },
            onConfirm = { name, date, description ->
                viewModel.ensureHealthInfoExists { healthId ->
                    viewModel.createSurgeryHistory(
                        CreateSurgeryRequest(
                            healthId = healthId,
                            surgeryName = name,
                            surgeryDate = date,
                            surgeryDescription = description
                        )
                    )
                }
                showAddSurgeryDialog = false
            }
        )
    }
    
    // 编辑手术史对话框
    if (showEditSurgeryDialog && selectedSurgery != null) {
        SurgeryDialog(
            surgery = selectedSurgery,
            onDismiss = { 
                showEditSurgeryDialog = false
                selectedSurgery = null
            },
            onConfirm = { name, date, description ->
                selectedSurgery?.let { surgery ->
                    surgery.surgeryId?.let { surgeryId ->
                        surgery.healthId?.let { healthId ->
                            viewModel.updateSurgeryHistory(
                                UpdateSurgeryRequest(
                                    surgeryId = surgeryId,
                                    healthId = healthId,
                                    surgeryName = name,
                                    surgeryDate = date,
                                    surgeryDescription = description
                                )
                            )
                        }
                    }
                }
                showEditSurgeryDialog = false
                selectedSurgery = null
            }
        )
    }
}

@Composable
fun ExpandableHistoryItem(
    icon: Int,
    title: String,
    isExpanded: Boolean,
    onExpandChange: () -> Unit,
    itemType: String = "toggle",
    expandedContent: @Composable () -> Unit = {}
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .background(
                color = Color.White,
                shape = RoundedCornerShape(12.dp)
            )
            .padding(vertical = 8.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 12.dp)
                .clickable(
                    enabled = itemType == "text_input",
                    onClick = { if (itemType == "text_input") onExpandChange() }
                ),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            // 左侧图标和标题
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    painter = painterResource(id = icon),
                    contentDescription = null,
                    modifier = Modifier.size(24.dp),
                    tint = Color(0xFF666666)
                )
                Spacer(modifier = Modifier.width(12.dp))
                Text(
                    text = title,
                    fontSize = 16.sp,
                    color = Color(0xFF333333)
                )
            }
            
            // 右侧内容
            when (itemType) {
                "toggle" -> {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        // 有按钮
                        Button(
                            onClick = { if (!isExpanded) onExpandChange() },
                            modifier = Modifier
                                .width(48.dp)
                                .height(32.dp),
                            colors = ButtonDefaults.buttonColors(
                                containerColor = if (isExpanded) Color(0xFF22C1C3) else Color.White,
                                contentColor = if (isExpanded) Color.White else Color(0xFF666666)
                            ),
                            border = BorderStroke(
                                width = 1.dp,
                                color = if (isExpanded) Color(0xFF22C1C3) else Color(0xFFE0E0E0)
                            ),
                            shape = RoundedCornerShape(16.dp),
                            contentPadding = PaddingValues(0.dp)
                        ) {
                            Text(
                                text = "有",
                                fontSize = 14.sp
                            )
                        }
                        
                        // 无按钮
                        Button(
                            onClick = { if (isExpanded) onExpandChange() },
                            modifier = Modifier
                                .width(48.dp)
                                .height(32.dp),
                            colors = ButtonDefaults.buttonColors(
                                containerColor = if (!isExpanded) Color(0xFF22C1C3) else Color.White,
                                contentColor = if (!isExpanded) Color.White else Color(0xFF666666)
                            ),
                            border = BorderStroke(
                                width = 1.dp,
                                color = if (!isExpanded) Color(0xFF22C1C3) else Color(0xFFE0E0E0)
                            ),
                            shape = RoundedCornerShape(16.dp),
                            contentPadding = PaddingValues(0.dp)
                        ) {
                            Text(
                                text = "无",
                                fontSize = 14.sp
                            )
                        }
                    }
                }
                "text_input" -> {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        Icon(
                            painter = painterResource(id = if (isExpanded) R.drawable.right_arrow else R.drawable.right_arrow),
                            contentDescription = null,
                            modifier = Modifier.size(20.dp),
                            tint = Color(0xFF666666)
                        )
                    }
                }
                else -> {
                    // 其他类型显示箭头图标
                    Icon(
                        painter = painterResource(id = if (isExpanded) R.drawable.right_arrow else R.drawable.right_arrow),
                        contentDescription = null,
                        modifier = Modifier.size(24.dp),
                        tint = Color(0xFF666666)
                    )
                }
            }
        }

        // 展开的详情内容
        if (isExpanded) {
            expandedContent()
        }
    }
}

@Composable
private fun SimpleInputContent(
    value: String,
    onValueChange: (String) -> Unit,
    label: String
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 8.dp)
    ) {
        OutlinedTextField(
            value = value,
            onValueChange = { newValue ->
                onValueChange(newValue)
                // 这里无法直接更新各个状态，在调用处处理
            },
            modifier = Modifier.fillMaxWidth(),
            label = { Text(label) },
            colors = OutlinedTextFieldDefaults.colors(
                unfocusedBorderColor = Color(0xFFE0E0E0),
                focusedBorderColor = Color(0xFF22C1C3)
            ),
            shape = RoundedCornerShape(8.dp)
        )
    }
}

@Composable
fun SurgeryHistoryItem(
    surgery: SurgeryHistory,
    onEdit: () -> Unit,
    onDelete: () -> Unit
) {
    var showDeleteConfirm by remember { mutableStateOf(false) }
    
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp),
        shape = RoundedCornerShape(8.dp),
        colors = CardDefaults.cardColors(containerColor = Color(0xFFF5F5F5))
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = surgery.surgeryName ?: "未知手术",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium
                )
                
                Row {
                    IconButton(
                        onClick = onEdit,
                        modifier = Modifier.size(24.dp)
                    ) {
                        Icon(
                            painter = painterResource(id = R.drawable.ic_edit),
                            contentDescription = "编辑",
                            tint = Color(0xFF22C1C3)
                        )
                    }
                    
                    Spacer(modifier = Modifier.width(8.dp))
                    
                    IconButton(
                        onClick = { showDeleteConfirm = true },
                        modifier = Modifier.size(24.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Delete,
                            contentDescription = "删除",
                            tint = Color.Red
                        )
                    }
                }
            }
            
            Text(
                text = "手术日期：${surgery.surgeryDate ?: "未知"}",
                fontSize = 14.sp,
                color = Color.Gray,
                modifier = Modifier.padding(top = 8.dp)
            )
            
            Text(
                text = surgery.surgeryDescription ?: "无描述",
                fontSize = 14.sp,
                color = Color.Gray,
                modifier = Modifier.padding(top = 4.dp)
            )
        }
    }
    
    if (showDeleteConfirm) {
        CustomDeleteDialog(
            title = "确认删除",
            message = "确定要删除这条手术史记录吗？",
            onDismiss = { showDeleteConfirm = false },
            onConfirm = {
                onDelete()
                showDeleteConfirm = false
            }
        )
    }
}

@Composable
fun SurgeryDialog(
    surgery: SurgeryHistory?,
    onDismiss: () -> Unit,
    onConfirm: (name: String, date: String, description: String) -> Unit
) {
    var surgeryName by remember { mutableStateOf(surgery?.surgeryName ?: "") }
    var surgeryDate by remember { mutableStateOf(formatDateForDisplay(surgery?.surgeryDate)) }
    var surgeryDescription by remember { mutableStateOf(surgery?.surgeryDescription ?: "") }
    var dateError by remember { mutableStateOf("") }
    var showDatePicker by remember { mutableStateOf(false) }

    // 当前日期用于日期选择器
    val calendar = Calendar.getInstance()
    var selectedYear by remember { mutableStateOf(calendar.get(Calendar.YEAR)) }
    var selectedMonth by remember { mutableStateOf(calendar.get(Calendar.MONTH)) }
    var selectedDay by remember { mutableStateOf(calendar.get(Calendar.DAY_OF_MONTH)) }

    // 验证并格式化日期
    fun validateAndFormatDate(date: String): Boolean {
        if (date.isBlank()) {
            dateError = "请选择手术日期"
            return false
        }
        return try {
            val pattern = "yyyy-MM-dd"
            val formatter = SimpleDateFormat(pattern, Locale.getDefault())
            formatter.isLenient = false
            formatter.parse(date)
            dateError = ""
            true
        } catch (e: Exception) {
            dateError = "日期格式错误"
            false
        }
    }

    // 格式化日期为 yyyy-MM-dd
    fun formatDate(year: Int, month: Int, day: Int): String {
        return String.format("%04d-%02d-%02d", year, month + 1, day)
    }

    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(
            dismissOnBackPress = true,
            dismissOnClickOutside = true
        )
    ) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(containerColor = Color.White),
            elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(24.dp)
            ) {
                // 标题
                Text(
                    text = if (surgery == null) "添加手术史" else "编辑手术史",
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF333333),
                    modifier = Modifier.padding(bottom = 24.dp)
                )

                // 手术名称输入
                Text(
                    text = "手术名称",
                    fontSize = 14.sp,
                    color = Color(0xFF666666),
                    modifier = Modifier.padding(bottom = 8.dp)
                )
                OutlinedTextField(
                    value = surgeryName,
                    onValueChange = { surgeryName = it },
                    placeholder = { Text("请输入手术名称", color = Color(0xFFCCCCCC)) },
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 16.dp),
                    shape = RoundedCornerShape(8.dp),
                    colors = OutlinedTextFieldDefaults.colors(
                        unfocusedBorderColor = Color(0xFFE0E0E0),
                        focusedBorderColor = Color(0xFF22C1C3),
                        unfocusedContainerColor = Color.White,
                        focusedContainerColor = Color.White
                    ),
                    isError = surgeryName.isBlank(),
                    singleLine = true
                )

                // 手术日期选择
                Text(
                    text = "手术日期",
                    fontSize = 14.sp,
                    color = Color(0xFF666666),
                    modifier = Modifier.padding(bottom = 8.dp)
                )
                Box(modifier = Modifier.fillMaxWidth()) {
                    OutlinedTextField(
                        value = surgeryDate,
                        onValueChange = { },
                        placeholder = { Text("请选择手术日期", color = Color(0xFFCCCCCC)) },
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(bottom = if (dateError.isNotEmpty()) 4.dp else 16.dp),
                        shape = RoundedCornerShape(8.dp),
                        colors = OutlinedTextFieldDefaults.colors(
                            unfocusedBorderColor = if (dateError.isNotEmpty()) Color.Red else Color(0xFFE0E0E0),
                            focusedBorderColor = if (dateError.isNotEmpty()) Color.Red else Color(0xFF22C1C3),
                            unfocusedContainerColor = Color.White,
                            focusedContainerColor = Color.White
                        ),
                        readOnly = true,
                        singleLine = true,
                        trailingIcon = {
                            Icon(
                                imageVector = Icons.Default.DateRange,
                                contentDescription = "选择日期",
                                tint = Color(0xFF666666),
                                modifier = Modifier.size(20.dp)
                            )
                        }
                    )

                    // 点击区域
                    Box(
                        modifier = Modifier
                            .matchParentSize()
                            .clickable { showDatePicker = true }
                    )
                }

                // 错误提示
                if (dateError.isNotEmpty()) {
                    Text(
                        text = dateError,
                        color = Color.Red,
                        fontSize = 12.sp,
                        modifier = Modifier.padding(bottom = 16.dp)
                    )
                }

                // 手术说明输入
                Text(
                    text = "手术说明",
                    fontSize = 14.sp,
                    color = Color(0xFF666666),
                    modifier = Modifier.padding(bottom = 8.dp)
                )
                OutlinedTextField(
                    value = surgeryDescription,
                    onValueChange = { surgeryDescription = it },
                    placeholder = { Text("请输入手术说明（可选）", color = Color(0xFFCCCCCC)) },
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 24.dp)
                        .height(100.dp),
                    shape = RoundedCornerShape(8.dp),
                    colors = OutlinedTextFieldDefaults.colors(
                        unfocusedBorderColor = Color(0xFFE0E0E0),
                        focusedBorderColor = Color(0xFF22C1C3),
                        unfocusedContainerColor = Color.White,
                        focusedContainerColor = Color.White
                    ),
                    maxLines = 3
                )

                // 按钮区域
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    Button(
                        onClick = onDismiss,
                        modifier = Modifier
                            .weight(1f)
                            .height(48.dp),
                        shape = RoundedCornerShape(8.dp),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFFF5F5F5),
                            contentColor = Color(0xFF666666)
                        )
                    ) {
                        Text(
                            text = "取消",
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Medium
                        )
                    }

                    Button(
                        onClick = {
                            if (surgeryName.isNotBlank() && validateAndFormatDate(surgeryDate)) {
                                onConfirm(surgeryName.trim(), surgeryDate, surgeryDescription.trim())
                            }
                        },
                        enabled = surgeryName.isNotBlank() && surgeryDate.isNotBlank() && dateError.isEmpty(),
                        modifier = Modifier
                            .weight(1f)
                            .height(48.dp),
                        shape = RoundedCornerShape(8.dp),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFF22C1C3),
                            contentColor = Color.White,
                            disabledContainerColor = Color(0xFFCCCCCC)
                        )
                    ) {
                        Text(
                            text = "确定",
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Medium
                        )
                    }
                }
            }
        }
    }

    // 日期选择器对话框
    if (showDatePicker) {
        DatePickerDialog(
            selectedYear = selectedYear,
            selectedMonth = selectedMonth,
            selectedDay = selectedDay,
            onDateSelected = { year, month, day ->
                selectedYear = year
                selectedMonth = month
                selectedDay = day
                surgeryDate = formatDate(year, month, day)
                validateAndFormatDate(surgeryDate)
                showDatePicker = false
            },
            onDismiss = { showDatePicker = false }
        )
    }
}

@Composable
fun DatePickerDialog(
    selectedYear: Int,
    selectedMonth: Int,
    selectedDay: Int,
    onDateSelected: (year: Int, month: Int, day: Int) -> Unit,
    onDismiss: () -> Unit
) {
    var currentYear by remember { mutableStateOf(selectedYear) }
    var currentMonth by remember { mutableStateOf(selectedMonth) }
    var currentDay by remember { mutableStateOf(selectedDay) }

    val calendar = Calendar.getInstance()
    val maxYear = calendar.get(Calendar.YEAR)
    val minYear = 1900

    // 获取当前月份的天数
    fun getDaysInMonth(year: Int, month: Int): Int {
        val cal = Calendar.getInstance()
        cal.set(year, month, 1)
        return cal.getActualMaximum(Calendar.DAY_OF_MONTH)
    }

    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(dismissOnClickOutside = true)
    ) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(containerColor = Color.White),
            elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(24.dp)
            ) {
                Text(
                    text = "选择手术日期",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF333333),
                    modifier = Modifier.padding(bottom = 24.dp)
                )

                // 年份选择
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "年份：",
                        fontSize = 16.sp,
                        color = Color(0xFF666666),
                        modifier = Modifier.width(60.dp)
                    )

                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        IconButton(
                            onClick = { if (currentYear > minYear) currentYear-- }
                        ) {
                            Text("<", fontSize = 18.sp, color = Color(0xFF22C1C3))
                        }

                        Text(
                            text = currentYear.toString(),
                            fontSize = 16.sp,
                            modifier = Modifier.width(60.dp),
                            textAlign = TextAlign.Center
                        )

                        IconButton(
                            onClick = { if (currentYear < maxYear) currentYear++ }
                        ) {
                            Text(">", fontSize = 18.sp, color = Color(0xFF22C1C3))
                        }
                    }
                }

                // 月份选择
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "月份：",
                        fontSize = 16.sp,
                        color = Color(0xFF666666),
                        modifier = Modifier.width(60.dp)
                    )

                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        IconButton(
                            onClick = { if (currentMonth > 0) currentMonth-- }
                        ) {
                            Text("<", fontSize = 18.sp, color = Color(0xFF22C1C3))
                        }

                        Text(
                            text = "${currentMonth + 1}月",
                            fontSize = 16.sp,
                            modifier = Modifier.width(60.dp),
                            textAlign = TextAlign.Center
                        )

                        IconButton(
                            onClick = { if (currentMonth < 11) currentMonth++ }
                        ) {
                            Text(">", fontSize = 18.sp, color = Color(0xFF22C1C3))
                        }
                    }
                }

                // 日期选择
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "日期：",
                        fontSize = 16.sp,
                        color = Color(0xFF666666),
                        modifier = Modifier.width(60.dp)
                    )

                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        IconButton(
                            onClick = { if (currentDay > 1) currentDay-- }
                        ) {
                            Text("<", fontSize = 18.sp, color = Color(0xFF22C1C3))
                        }

                        Text(
                            text = "${currentDay}日",
                            fontSize = 16.sp,
                            modifier = Modifier.width(60.dp),
                            textAlign = TextAlign.Center
                        )

                        IconButton(
                            onClick = {
                                val maxDays = getDaysInMonth(currentYear, currentMonth)
                                if (currentDay < maxDays) currentDay++
                            }
                        ) {
                            Text(">", fontSize = 18.sp, color = Color(0xFF22C1C3))
                        }
                    }
                }

                Spacer(modifier = Modifier.height(24.dp))

                // 按钮
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    Button(
                        onClick = onDismiss,
                        modifier = Modifier
                            .weight(1f)
                            .height(48.dp),
                        shape = RoundedCornerShape(8.dp),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFFF5F5F5),
                            contentColor = Color(0xFF666666)
                        )
                    ) {
                        Text("取消", fontSize = 16.sp)
                    }

                    Button(
                        onClick = {
                            // 确保日期不超过当月最大天数
                            val maxDays = getDaysInMonth(currentYear, currentMonth)
                            val validDay = if (currentDay > maxDays) maxDays else currentDay
                            onDateSelected(currentYear, currentMonth, validDay)
                        },
                        modifier = Modifier
                            .weight(1f)
                            .height(48.dp),
                        shape = RoundedCornerShape(8.dp),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFF22C1C3)
                        )
                    ) {
                        Text("确定", fontSize = 16.sp)
                    }
                }
            }
        }
    }
}

