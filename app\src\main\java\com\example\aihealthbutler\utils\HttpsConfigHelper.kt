package com.example.aihealthbutler.utils

import android.util.Log
import okhttp3.OkHttpClient
import java.security.cert.X509Certificate
import javax.net.ssl.SSLContext
import javax.net.ssl.TrustManager
import javax.net.ssl.X509TrustManager

/**
 * HTTPS配置辅助工具
 * 用于统一配置SSL信任和证书验证
 */
object HttpsConfigHelper {
    
    private const val TAG = "HttpsConfigHelper"
    
    /**
     * 为OkHttpClient.Builder配置HTTPS SSL信任
     * 注意：这种配置会信任所有证书，仅用于开发和测试环境
     * 生产环境应该使用正确的证书验证
     */
    fun configureHttpsClient(builder: OkHttpClient.Builder): OkHttpClient.Builder {
        return builder.apply {
            try {
                Log.d(TAG, "配置HTTPS SSL信任...")
                
                // 创建信任所有证书的TrustManager
                val trustAllCerts = arrayOf<TrustManager>(object : X509TrustManager {
                    override fun checkClientTrusted(chain: Array<X509Certificate>, authType: String) {
                        // 信任所有客户端证书
                    }
                    
                    override fun checkServerTrusted(chain: Array<X509Certificate>, authType: String) {
                        // 信任所有服务器证书
                        Log.d(TAG, "服务器证书验证: authType=$authType, 证书数量=${chain.size}")
                    }
                    
                    override fun getAcceptedIssuers(): Array<X509Certificate> {
                        return arrayOf()
                    }
                })
                
                // 初始化SSL上下文
                val sslContext = SSLContext.getInstance("TLS")
                sslContext.init(null, trustAllCerts, java.security.SecureRandom())
                
                // 配置SSL Socket Factory
                sslSocketFactory(sslContext.socketFactory, trustAllCerts[0] as X509TrustManager)
                
                // 配置主机名验证器（信任所有主机名）
                hostnameVerifier { hostname, session ->
                    Log.d(TAG, "主机名验证: hostname=$hostname")
                    true // 信任所有主机名
                }
                
                Log.d(TAG, "HTTPS SSL配置完成")
                
            } catch (e: Exception) {
                Log.e(TAG, "HTTPS SSL配置失败", e)
                // 配置失败时继续使用默认配置
            }
        }
    }
    
    /**
     * 创建配置了HTTPS的OkHttpClient
     */
    fun createHttpsClient(
        connectTimeoutSeconds: Long = 30,
        readTimeoutSeconds: Long = 60,
        writeTimeoutSeconds: Long = 60
    ): OkHttpClient {
        return configureHttpsClient(
            OkHttpClient.Builder()
                .connectTimeout(connectTimeoutSeconds, java.util.concurrent.TimeUnit.SECONDS)
                .readTimeout(readTimeoutSeconds, java.util.concurrent.TimeUnit.SECONDS)
                .writeTimeout(writeTimeoutSeconds, java.util.concurrent.TimeUnit.SECONDS)
                .retryOnConnectionFailure(true)
        ).build()
    }
    
    /**
     * 创建用于测试的简单HTTPS客户端
     */
    fun createTestHttpsClient(timeoutSeconds: Long = 10): OkHttpClient {
        return configureHttpsClient(
            OkHttpClient.Builder()
                .connectTimeout(timeoutSeconds, java.util.concurrent.TimeUnit.SECONDS)
                .readTimeout(timeoutSeconds, java.util.concurrent.TimeUnit.SECONDS)
        ).build()
    }
    
    /**
     * 检查URL是否为HTTPS
     */
    fun isHttpsUrl(url: String): Boolean {
        return url.startsWith("https://", ignoreCase = true)
    }
    
    /**
     * 将HTTP URL转换为HTTPS URL
     */
    fun toHttpsUrl(url: String): String {
        return if (url.startsWith("http://", ignoreCase = true)) {
            url.replaceFirst("http://", "https://", ignoreCase = true)
        } else {
            url
        }
    }
    
    /**
     * 获取HTTPS配置信息
     */
    fun getHttpsConfigInfo(): String {
        return buildString {
            appendLine("=== HTTPS配置信息 ===")
            appendLine("SSL协议: TLS")
            appendLine("证书验证: 信任所有证书（开发模式）")
            appendLine("主机名验证: 信任所有主机名")
            appendLine("连接重试: 启用")
            appendLine()
            appendLine("⚠️ 注意事项:")
            appendLine("• 当前配置信任所有SSL证书")
            appendLine("• 仅适用于开发和测试环境")
            appendLine("• 生产环境应使用正确的证书验证")
            appendLine("• 确保服务器支持HTTPS协议")
        }
    }
    
    /**
     * 验证HTTPS配置
     */
    suspend fun validateHttpsConfig(url: String): HttpsValidationResult {
        return try {
            Log.d(TAG, "验证HTTPS配置: $url")
            
            if (!isHttpsUrl(url)) {
                return HttpsValidationResult(
                    success = false,
                    message = "URL不是HTTPS协议: $url"
                )
            }
            
            val client = createTestHttpsClient(5)
            val request = okhttp3.Request.Builder()
                .url(url)
                .head() // 使用HEAD请求减少数据传输
                .build()
            
            val response = client.newCall(request).execute()
            val success = response.isSuccessful || response.code in 400..499
            
            response.close()
            
            HttpsValidationResult(
                success = success,
                message = if (success) "HTTPS连接验证成功" else "HTTPS连接验证失败: ${response.code}",
                responseCode = response.code
            )
            
        } catch (e: Exception) {
            Log.e(TAG, "HTTPS配置验证失败", e)
            HttpsValidationResult(
                success = false,
                message = "HTTPS验证异常: ${e.message}",
                exception = e
            )
        }
    }
}

/**
 * HTTPS验证结果
 */
data class HttpsValidationResult(
    val success: Boolean,
    val message: String,
    val responseCode: Int = 0,
    val exception: Exception? = null
) {
    fun getDetailedInfo(): String {
        return buildString {
            appendLine("HTTPS验证结果:")
            appendLine("状态: ${if (success) "✓ 成功" else "✗ 失败"}")
            appendLine("消息: $message")
            if (responseCode > 0) {
                appendLine("响应码: $responseCode")
            }
            exception?.let {
                appendLine("异常: ${it.javaClass.simpleName}")
                appendLine("详情: ${it.message}")
            }
        }
    }
}
