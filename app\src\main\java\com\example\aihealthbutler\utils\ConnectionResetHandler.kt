package com.example.aihealthbutler.utils

import android.util.Log
import kotlinx.coroutines.delay
import java.io.IOException
import java.net.ConnectException
import java.net.SocketException
import java.net.SocketTimeoutException

/**
 * 连接重置处理工具
 * 专门处理网络连接被重置的问题
 */
object ConnectionResetHandler {
    
    private const val TAG = "ConnectionResetHandler"
    
    /**
     * 重试配置
     */
    data class RetryConfig(
        val maxRetries: Int = 3,
        val baseDelayMs: Long = 1000L,
        val maxDelayMs: Long = 10000L,
        val backoffMultiplier: Double = 2.0
    )
    
    /**
     * 执行带重试的操作
     */
    suspend fun <T> executeWithRetry(
        operation: String,
        config: RetryConfig = RetryConfig(),
        block: suspend () -> T
    ): T {
        var lastException: Exception? = null
        
        repeat(config.maxRetries) { attempt ->
            try {
                Log.d(TAG, "Executing $operation, attempt: ${attempt + 1}/${config.maxRetries}")
                return block()
            } catch (e: Exception) {
                lastException = e
                
                val shouldRetry = isRetryableException(e)
                Log.w(TAG, "$operation failed on attempt ${attempt + 1}: ${e.message}, retryable: $shouldRetry")
                
                if (!shouldRetry || attempt == config.maxRetries - 1) {
                    Log.e(TAG, "$operation failed after ${attempt + 1} attempts")
                    throw wrapException(e, operation)
                }
                
                // 计算延迟时间（指数退避）
                val delayMs = calculateDelay(attempt, config)
                Log.d(TAG, "Retrying $operation in ${delayMs}ms...")
                delay(delayMs)
            }
        }
        
        throw lastException ?: IOException("Operation failed after ${config.maxRetries} attempts")
    }
    
    /**
     * 判断异常是否可重试
     */
    private fun isRetryableException(exception: Exception): Boolean {
        return when (exception) {
            is SocketException -> {
                val message = exception.message?.lowercase() ?: ""
                when {
                    message.contains("connection reset") -> true
                    message.contains("broken pipe") -> true
                    message.contains("connection refused") -> true
                    message.contains("network is unreachable") -> true
                    else -> false
                }
            }
            is ConnectException -> true
            is SocketTimeoutException -> true
            is IOException -> {
                val message = exception.message?.lowercase() ?: ""
                when {
                    message.contains("connection reset") -> true
                    message.contains("timeout") -> true
                    message.contains("connection closed") -> true
                    message.contains("unexpected end of stream") -> true
                    else -> false
                }
            }
            else -> false
        }
    }
    
    /**
     * 计算重试延迟时间（指数退避）
     */
    private fun calculateDelay(attempt: Int, config: RetryConfig): Long {
        val delay = (config.baseDelayMs * Math.pow(config.backoffMultiplier, attempt.toDouble())).toLong()
        return minOf(delay, config.maxDelayMs)
    }
    
    /**
     * 包装异常，提供更友好的错误信息
     */
    private fun wrapException(exception: Exception, operation: String): Exception {
        val friendlyMessage = when (exception) {
            is SocketException -> {
                when {
                    exception.message?.contains("Connection reset") == true -> 
                        "网络连接被重置，请检查网络稳定性后重试"
                    exception.message?.contains("Connection refused") == true -> 
                        "服务器拒绝连接，请检查服务器状态"
                    else -> "网络连接异常：${exception.message}"
                }
            }
            is ConnectException -> "无法连接到服务器，请检查网络连接和服务器状态"
            is SocketTimeoutException -> "连接超时，请检查网络速度或稍后重试"
            is IOException -> {
                when {
                    exception.message?.contains("timeout") == true -> "请求超时，请稍后重试"
                    exception.message?.contains("connection closed") == true -> "连接被关闭，请重试"
                    else -> "网络请求失败：${exception.message}"
                }
            }
            else -> "操作失败：${exception.message}"
        }
        
        return IOException("$operation - $friendlyMessage", exception)
    }
    
    /**
     * 获取异常的详细诊断信息
     */
    fun getDiagnosticInfo(exception: Exception): String {
        return buildString {
            appendLine("=== 连接异常诊断 ===")
            appendLine("异常类型: ${exception.javaClass.simpleName}")
            appendLine("异常消息: ${exception.message}")
            appendLine("是否可重试: ${isRetryableException(exception)}")
            
            when (exception) {
                is SocketException -> {
                    appendLine("\n🔍 Socket异常分析:")
                    val message = exception.message?.lowercase() ?: ""
                    when {
                        message.contains("connection reset") -> {
                            appendLine("• 连接被重置 - 通常由服务器主动断开连接引起")
                            appendLine("• 可能原因：服务器负载过高、网络不稳定、防火墙干预")
                            appendLine("• 建议：重试请求、检查网络稳定性")
                        }
                        message.contains("connection refused") -> {
                            appendLine("• 连接被拒绝 - 服务器未监听指定端口")
                            appendLine("• 可能原因：服务器未启动、端口错误、防火墙阻止")
                            appendLine("• 建议：检查服务器状态、验证端口配置")
                        }
                        message.contains("broken pipe") -> {
                            appendLine("• 管道破裂 - 连接在数据传输过程中断开")
                            appendLine("• 可能原因：网络中断、服务器异常关闭")
                            appendLine("• 建议：重试请求、检查网络连接")
                        }
                    }
                }
                is ConnectException -> {
                    appendLine("\n🔍 连接异常分析:")
                    appendLine("• 无法建立连接到目标服务器")
                    appendLine("• 可能原因：网络不通、服务器离线、DNS解析失败")
                    appendLine("• 建议：检查网络连接、验证服务器地址、测试DNS解析")
                }
                is SocketTimeoutException -> {
                    appendLine("\n🔍 超时异常分析:")
                    appendLine("• 连接或读取操作超时")
                    appendLine("• 可能原因：网络延迟高、服务器响应慢")
                    appendLine("• 建议：增加超时时间、检查网络速度、稍后重试")
                }
            }
            
            appendLine("\n📋 通用解决建议:")
            appendLine("1. 检查网络连接状态")
            appendLine("2. 尝试切换网络（WiFi/移动数据）")
            appendLine("3. 重启应用或设备")
            appendLine("4. 联系技术支持")
        }
    }
    
    /**
     * 快速重试策略（用于轻量级操作）
     */
    suspend fun <T> quickRetry(
        operation: String,
        maxRetries: Int = 2,
        delayMs: Long = 500L,
        block: suspend () -> T
    ): T {
        return executeWithRetry(
            operation = operation,
            config = RetryConfig(
                maxRetries = maxRetries,
                baseDelayMs = delayMs,
                maxDelayMs = delayMs * 2,
                backoffMultiplier = 1.5
            ),
            block = block
        )
    }
    
    /**
     * 流式操作重试策略（用于长连接操作）
     */
    suspend fun <T> streamRetry(
        operation: String,
        block: suspend () -> T
    ): T {
        return executeWithRetry(
            operation = operation,
            config = RetryConfig(
                maxRetries = 5,
                baseDelayMs = 1000L,
                maxDelayMs = 8000L,
                backoffMultiplier = 1.8
            ),
            block = block
        )
    }
}
