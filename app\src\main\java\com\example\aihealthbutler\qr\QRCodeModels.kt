package com.example.aihealthbutler.qr

import com.google.gson.annotations.SerializedName

/**
 * 二维码邀请数据模型
 */
data class QRInviteData(
    @SerializedName("type")
    val type: String = "user_invite",

    @SerializedName("userId")
    val userId: String,

    @SerializedName("account")
    val account: String,

    @SerializedName("name")
    val name: String,

    @SerializedName("avatar")
    val avatar: String? = null,

    @SerializedName("timestamp")
    val timestamp: Long = System.currentTimeMillis(),

    @SerializedName("signature")
    val signature: String? = null
) : java.io.Serializable

/**
 * 二维码生成配置
 */
data class QRCodeConfig(
    val width: Int = 512,
    val height: Int = 512,
    val margin: Int = 1,
    val backgroundColor: Int = 0xFFFFFFFF.toInt(), // 白色
    val foregroundColor: Int = 0xFF000000.toInt()  // 黑色
)

/**
 * 二维码生成结果
 */
sealed class QRCodeResult {
    data class Success(val bitmap: android.graphics.Bitmap) : QRCodeResult()
    data class Error(val message: String, val exception: Exception? = null) : QRCodeResult()
}

/**
 * 二维码验证结果
 */
sealed class QRValidationResult {
    object Valid : QRValidationResult()
    data class Invalid(val reason: String) : QRValidationResult()
    data class Expired(val expiredTime: Long) : QRValidationResult()
}
