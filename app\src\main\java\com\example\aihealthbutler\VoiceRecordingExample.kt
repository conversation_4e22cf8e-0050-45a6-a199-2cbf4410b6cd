package com.example.aihealthbutler

import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.delay
import kotlin.random.Random
import com.example.aihealthbutler.speech.SpeechToTextState
import com.example.aihealthbutler.speech.SpeechToTextResult

/**
 * 语音录制界面使用示例
 */
@Composable
fun VoiceRecordingExample() {
    var isRecording by remember { mutableStateOf(false) }
    var recordingDuration by remember { mutableStateOf(0L) }
    var volumeLevel by remember { mutableStateOf(0.5f) }

    // 语音转文字状态
    var speechToTextState by remember { mutableStateOf(SpeechToTextState.IDLE) }
    var speechToTextResult by remember { mutableStateOf(SpeechToTextResult()) }
    var speechToTextError by remember { mutableStateOf("") }

    // 模拟录音时长计时器
    LaunchedEffect(isRecording) {
        if (isRecording) {
            while (isRecording) {
                delay(1000)
                recordingDuration++
            }
        } else {
            recordingDuration = 0
        }
    }

    // 模拟音量变化
    LaunchedEffect(isRecording) {
        if (isRecording) {
            while (isRecording) {
                delay(200)
                volumeLevel = Random.nextFloat() * 0.8f + 0.2f
            }
        }
    }

    // 模拟语音转文字过程
    LaunchedEffect(speechToTextState) {
        if (speechToTextState == SpeechToTextState.LISTENING) {
            delay(1000)
            speechToTextState = SpeechToTextState.PROCESSING
            delay(2000)
            speechToTextResult = SpeechToTextResult(
                text = "这是一段模拟的语音转文字结果，用于演示功能效果。",
                confidence = 0.95f,
                isPartial = false
            )
            speechToTextState = SpeechToTextState.SUCCESS
        }
    }

    Box(modifier = Modifier.fillMaxSize()) {
        // 主界面内容
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            Text("语音录制界面演示")
            
            Spacer(modifier = Modifier.height(32.dp))
            
            Button(
                onClick = { isRecording = !isRecording }
            ) {
                Text(if (isRecording) "停止录音" else "开始录音")
            }
        }

        // 微信风格语音录制覆盖层（带语音转文字功能）
        EnhancedVoiceRecordingOverlay(
            isRecording = isRecording,
            recordingDuration = recordingDuration,
            volumeLevel = volumeLevel,
            speechToTextState = speechToTextState,
            speechToTextResult = speechToTextResult,
            speechToTextError = speechToTextError,
            onCancel = {
                isRecording = false
                speechToTextState = SpeechToTextState.IDLE
                println("录音已取消")
            },
            onConvertToText = {
                isRecording = false
                // 模拟语音转文字过程
                speechToTextState = SpeechToTextState.LISTENING
                println("开始转换为文字")
            },
            onComplete = {
                isRecording = false
                println("录音完成")
            },
            onDismissSpeechResult = {
                speechToTextState = SpeechToTextState.IDLE
                speechToTextResult = SpeechToTextResult()
                speechToTextError = ""
                println("关闭语音转文字结果")
            },
            onSendSpeechMessage = { text ->
                speechToTextState = SpeechToTextState.IDLE
                speechToTextResult = SpeechToTextResult()
                println("发送语音转文字消息: $text")
            }
        )
    }
}
