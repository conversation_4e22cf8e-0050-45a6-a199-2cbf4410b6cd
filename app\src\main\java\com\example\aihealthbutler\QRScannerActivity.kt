package com.example.aihealthbutler

import android.Manifest
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Bundle
import android.util.Log
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.ui.res.painterResource
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.content.ContextCompat
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.lifecycle.viewmodel.compose.viewModel
import com.example.aihealthbutler.api.Repository
import com.example.aihealthbutler.api.SessionManager
import com.example.aihealthbutler.camera.CameraPreview
import com.example.aihealthbutler.qr.QRCodeGenerator
import com.example.aihealthbutler.qr.QRInviteData
import com.example.aihealthbutler.qr.QRValidationResult
import com.example.aihealthbutler.ui.theme.AIHealthButlerTheme
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch

/**
 * 二维码扫描页面
 */
class QRScannerActivity : ComponentActivity() {
    
    private val requestPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted: Boolean ->
        if (isGranted) {
            // 权限已授予，可以开始扫码
        } else {
            Toast.makeText(this, "需要相机权限才能扫码", Toast.LENGTH_SHORT).show()
            finish()
        }
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        
        // 检查相机权限
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.CAMERA) 
            != PackageManager.PERMISSION_GRANTED) {
            requestPermissionLauncher.launch(Manifest.permission.CAMERA)
        }
        
        setContent {
            AIHealthButlerTheme {
                val sessionManager = remember { SessionManager(applicationContext) }
                val repository = remember { Repository() }
                val viewModel = remember { QRScannerViewModel(repository, sessionManager) }
                
                QRScannerScreen(
                    viewModel = viewModel,
                    onBack = { finish() },
                    onScanSuccess = { qrData ->
                        // 跳转到确认页面
                        val intent = Intent(this@QRScannerActivity, AddMemberConfirmActivity::class.java)
                        intent.putExtra("qr_data", qrData)
                        startActivity(intent)
                        finish()
                    }
                )
            }
        }
    }
}

/**
 * 扫码ViewModel
 */
class QRScannerViewModel(
    private val repository: Repository,
    private val sessionManager: SessionManager
) : ViewModel() {
    private val TAG = "QRScannerViewModel"

    private val _scanState = MutableStateFlow<QRScanState>(QRScanState.Scanning)
    val scanState: StateFlow<QRScanState> = _scanState

    private val _flashEnabled = MutableStateFlow(false)
    val flashEnabled: StateFlow<Boolean> = _flashEnabled

    private var lastScannedData: QRInviteData? = null
    
    /**
     * 处理扫码结果
     */
    fun handleScanResult(content: String): QRInviteData? {
        return try {
            _scanState.value = QRScanState.Processing

            // 验证二维码
            when (val validationResult = QRCodeGenerator.validateQRInviteData(content)) {
                is QRValidationResult.Valid -> {
                    val qrData = QRCodeGenerator.parseQRInviteData(content)
                    if (qrData != null) {
                        lastScannedData = qrData
                        _scanState.value = QRScanState.Success
                        qrData
                    } else {
                        _scanState.value = QRScanState.Error("二维码解析失败")
                        null
                    }
                }
                is QRValidationResult.Invalid -> {
                    _scanState.value = QRScanState.Error(validationResult.reason)
                    null
                }
                is QRValidationResult.Expired -> {
                    _scanState.value = QRScanState.Error("二维码已过期，请重新生成")
                    null
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "处理扫码结果失败", e)
            _scanState.value = QRScanState.Error("扫码失败: ${e.message}")
            null
        }
    }
    
    /**
     * 切换闪光灯
     */
    fun toggleFlash() {
        _flashEnabled.value = !_flashEnabled.value
    }
    
    /**
     * 重置扫码状态
     */
    fun resetScanState() {
        _scanState.value = QRScanState.Scanning
        lastScannedData = null
    }

    /**
     * 获取最后扫描的数据
     */
    fun getLastScannedData(): QRInviteData? = lastScannedData
}

/**
 * 扫码状态
 */
sealed class QRScanState {
    object Scanning : QRScanState()
    object Processing : QRScanState()
    object Success : QRScanState()
    data class Error(val message: String) : QRScanState()
}

/**
 * 扫码界面UI
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun QRScannerScreen(
    viewModel: QRScannerViewModel,
    onBack: () -> Unit,
    onScanSuccess: (QRInviteData) -> Unit
) {
    val scanState by viewModel.scanState.collectAsState()
    val flashEnabled by viewModel.flashEnabled.collectAsState()
    val context = LocalContext.current
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("扫一扫") },
                navigationIcon = {
                    IconButton(onClick = onBack) {
                        Icon(
                            painter = painterResource(id = R.drawable.left_arrow),
                            contentDescription = "返回",
                            modifier = Modifier.size(24.dp)
                        )
                    }
                },
                actions = {
                    IconButton(onClick = { viewModel.toggleFlash() }) {
                        Icon(
                            painter = painterResource(
                                id = if (flashEnabled) R.drawable.ic_lightbulbon else R.drawable.ic_lightbulboff
                            ),
                            contentDescription = if (flashEnabled) "关闭闪光灯" else "打开闪光灯",
                            modifier = Modifier.size(20.dp)
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = Color.White,
                    titleContentColor = Color.Black,
                    navigationIconContentColor = Color.Black,
                    actionIconContentColor = Color.Black
                )
            )
        },
        containerColor = Color.Black
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            // 相机预览
            CameraPreview(
                modifier = Modifier.fillMaxSize(),
                onQRCodeDetected = { qrContent ->
                    Log.d("QRScannerActivity", "检测到二维码: $qrContent")
                    viewModel.handleScanResult(qrContent)
                },
                flashEnabled = flashEnabled
            )

            // 扫码框覆盖层
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                // 扫码框
                ScanFrame()

                // 状态提示
                ScanStatusOverlay(
                    scanState = scanState,
                    onRetry = { viewModel.resetScanState() }
                )
            }
            
            // 底部说明和测试按钮
            Column(
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .padding(32.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                ScanInstructions()


            }
        }
    }
    
    // 处理扫码成功
    LaunchedEffect(scanState) {
        if (scanState is QRScanState.Success) {
            // 从ViewModel获取扫码结果
            viewModel.getLastScannedData()?.let { qrData ->
                onScanSuccess(qrData)
            }
        }
    }
}

/**
 * 扫码框
 */
@Composable
private fun ScanFrame() {
    Box(
        modifier = Modifier.size(250.dp),
        contentAlignment = Alignment.Center
    ) {
        // 扫码框边框
        Box(
            modifier = Modifier
                .size(200.dp)
                .border(2.dp, Color.White, RoundedCornerShape(12.dp))
        )

        // 四个角的装饰
        val cornerSize = 20.dp
        val cornerThickness = 4.dp

        // 左上角
        Box(
            modifier = Modifier
                .align(Alignment.TopStart)
                .offset((-10).dp, (-10).dp)
        ) {
            Box(
                modifier = Modifier
                    .size(cornerSize, cornerThickness)
                    .background(Color(0xFF22C1C3), RoundedCornerShape(2.dp))
            )
            Box(
                modifier = Modifier
                    .size(cornerThickness, cornerSize)
                    .background(Color(0xFF22C1C3), RoundedCornerShape(2.dp))
            )
        }

        // 右上角
        Box(
            modifier = Modifier
                .align(Alignment.TopEnd)
                .offset(10.dp, (-10).dp)
        ) {
            Box(
                modifier = Modifier
                    .size(cornerSize, cornerThickness)
                    .background(Color(0xFF22C1C3), RoundedCornerShape(2.dp))
            )
            Box(
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .size(cornerThickness, cornerSize)
                    .background(Color(0xFF22C1C3), RoundedCornerShape(2.dp))
            )
        }

        // 左下角
        Box(
            modifier = Modifier
                .align(Alignment.BottomStart)
                .offset((-10).dp, 10.dp)
        ) {
            Box(
                modifier = Modifier
                    .align(Alignment.BottomStart)
                    .size(cornerSize, cornerThickness)
                    .background(Color(0xFF22C1C3), RoundedCornerShape(2.dp))
            )
            Box(
                modifier = Modifier
                    .size(cornerThickness, cornerSize)
                    .background(Color(0xFF22C1C3), RoundedCornerShape(2.dp))
            )
        }

        // 右下角
        Box(
            modifier = Modifier
                .align(Alignment.BottomEnd)
                .offset(10.dp, 10.dp)
        ) {
            Box(
                modifier = Modifier
                    .align(Alignment.BottomEnd)
                    .size(cornerSize, cornerThickness)
                    .background(Color(0xFF22C1C3), RoundedCornerShape(2.dp))
            )
            Box(
                modifier = Modifier
                    .align(Alignment.BottomEnd)
                    .size(cornerThickness, cornerSize)
                    .background(Color(0xFF22C1C3), RoundedCornerShape(2.dp))
            )
        }
    }
}

/**
 * 扫码状态覆盖层
 */
@Composable
private fun ScanStatusOverlay(
    scanState: QRScanState,
    onRetry: () -> Unit
) {
    when (scanState) {
        is QRScanState.Processing -> {
            Card(
                modifier = Modifier.padding(16.dp),
                colors = CardDefaults.cardColors(containerColor = Color.White.copy(alpha = 0.9f))
            ) {
                Row(
                    modifier = Modifier.padding(16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(24.dp),
                        color = Color(0xFF22C1C3)
                    )
                    Spacer(modifier = Modifier.width(12.dp))
                    Text("正在识别二维码...")
                }
            }
        }
        is QRScanState.Error -> {
            Card(
                modifier = Modifier.padding(16.dp),
                colors = CardDefaults.cardColors(containerColor = Color.White.copy(alpha = 0.9f))
            ) {
                Column(
                    modifier = Modifier.padding(16.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = scanState.message,
                        color = Color.Red,
                        textAlign = TextAlign.Center
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Button(
                        onClick = onRetry,
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFF22C1C3)
                        )
                    ) {
                        Text("重试")
                    }
                }
            }
        }
        else -> {
            // 正常扫码状态，不显示覆盖层
        }
    }
}

/**
 * 扫码说明
 */
@Composable
private fun ScanInstructions() {
    Card(
        colors = CardDefaults.cardColors(containerColor = Color.White.copy(alpha = 0.9f)),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = "将二维码放入框内",
                fontSize = 16.sp,
                fontWeight = FontWeight.Bold,
                color = Color.Black
            )
            Spacer(modifier = Modifier.height(4.dp))
            Text(
                text = "即可自动扫描",
                fontSize = 14.sp,
                color = Color.Gray
            )
        }
    }
}
