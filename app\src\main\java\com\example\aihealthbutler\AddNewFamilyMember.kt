package com.example.aihealthbutler

import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.aihealthbutler.ui.theme.AIHealthButlerTheme
import com.example.aihealthbutler.ui.components.GenderSelectionDialog
import androidx.compose.material3.AlertDialog
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import androidx.compose.foundation.clickable
import java.text.SimpleDateFormat
import java.util.*
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.draw.clip
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.border
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.lifecycle.viewmodel.compose.viewModel
import com.example.aihealthbutler.api.Repository
import com.example.aihealthbutler.api.SessionManager
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.launch
import android.util.Log
import android.widget.Toast
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import com.example.aihealthbutler.api.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items

class AddNewFamilyMemberViewModel(
    private val repository: Repository,
    private val sessionManager: SessionManager
) : ViewModel() {
    private val TAG = "AddNewFamilyMemberVM"
    
    private val _savingState = MutableStateFlow<SavingState>(SavingState.Idle)
    val savingState: StateFlow<SavingState> = _savingState
    
    // 添加检查登录状态的方法
    private suspend fun checkLoginStatus(): Boolean {
        val token = sessionManager.token.firstOrNull()
        val account = sessionManager.account.value
        
        Log.d(TAG, "检查登录状态 - token: ${token?.take(10) ?: "null"}, account: $account")
        
        if (token.isNullOrEmpty() || account.isNullOrEmpty()) {
            _savingState.value = SavingState.Error("请先登录")
            Log.e(TAG, "未登录状态，token: ${token?.take(10) ?: "null"}, account: $account")
            return false
        }
        return true
    }
    
    fun saveFamilyMember(
        nickname: String,
        gender: String,
        birthdate: String,
        heightValue: Int,
        weightValue: Float,
        relationship: String
    ) {
        if (nickname.isEmpty() || birthdate.isEmpty() || relationship.isEmpty()) {
            _savingState.value = SavingState.Error("请填写所有必填项")
            return
        }
        
        viewModelScope.launch {
            try {
                _savingState.value = SavingState.Loading
                Log.d(TAG, "开始保存家庭成员: $nickname, $gender, $birthdate, 关系: $relationship")
                
                // 检查登录状态
                if (!checkLoginStatus()) {
                    _savingState.value = SavingState.NeedLogin("请先登录")
                    return@launch
                }
                
                // 1. 获取当前账号和token
                val account = sessionManager.account.value ?: throw Exception("未登录")
                val token = sessionManager.token.firstOrNull() ?: throw Exception("未登录")
                
                Log.d(TAG, "获取到账号: $account, token长度: ${token.length}")
                
                // 2. 添加账号下家庭成员
                val addMemberRequest = AddMemberRequest(account = account)
                Log.d(TAG, "调用添加成员API: $addMemberRequest")
                
                val addMemberResult = repository.addMember(token, addMemberRequest)
                
                if (addMemberResult.isFailure) {
                    throw Exception(addMemberResult.exceptionOrNull()?.message ?: "添加家庭成员失败")
                }
                
                val memberData = addMemberResult.getOrNull()?.data
                if (memberData == null || memberData.default_id.isNullOrEmpty()) {
                    Log.e(TAG, "添加家庭成员失败: default_id 为空，返回数据: $memberData")
                    throw Exception("添加家庭成员失败: 未返回有效的 default_id")
                }
                val defaultId = memberData.default_id
                Log.d(TAG, "添加成员成功，获取到defaultId: $defaultId")
                
                // 3. 更新个人信息 - 先设置个人信息
                val personInfoRequest = PersonInfoRequest(
                    name = nickname,
                    sex = when(gender) {
                        "男" -> 1
                        "女" -> 0
                        else -> 1
                    },
                    birthday = birthdate,
                    height = heightValue,
                    weight = weightValue
                )
                
                Log.d(TAG, "调用更新个人信息API: $personInfoRequest")
                
                val updateResult = repository.updatePersonInfo(token, defaultId, personInfoRequest)

                if (updateResult.isFailure) {
                    throw Exception(updateResult.exceptionOrNull()?.message ?: "更新个人信息失败")
                }
                
                Log.d(TAG, "更新个人信息成功")
                
                // 4. 设置家庭关系 - 使用新接口
                val currentDefaultId = sessionManager.defaultId.firstOrNull() ?: throw Exception("获取当前用户ID失败")
                val newMemberDefaultId = defaultId ?: throw Exception("获取新成员ID失败")

                val setRelationshipRequest = SetFamilyRelationshipRequest(
                    fromMemberId = newMemberDefaultId,
                    toMemberId = currentDefaultId,
                    relationship = relationship
                )
                Log.d(TAG, "调用设置家庭关系API: $setRelationshipRequest")
                Log.d(TAG, "关系设置: ${newMemberDefaultId}是${currentDefaultId}的${relationship}")

                val setRelationshipResult = repository.setFamilyRelationship(token, setRelationshipRequest)

                if (setRelationshipResult.isFailure) {
                    val error = setRelationshipResult.exceptionOrNull()
                    Log.e(TAG, "设置家庭关系失败", error)
                    throw Exception(error?.message ?: "设置家庭关系失败")
                }

                val relationshipResponse = setRelationshipResult.getOrNull()
                Log.d(TAG, "设置家庭关系成功: $relationshipResponse")
                
                _savingState.value = SavingState.Success("添加家庭成员成功")
                
            } catch (e: Exception) {
                Log.e(TAG, "保存家庭成员失败", e)
                _savingState.value = SavingState.Error(e.message ?: "保存失败")
            }
        }
    }
    
    fun createFamilyRelation(account: String) {
        viewModelScope.launch {
            try {
                _savingState.value = SavingState.Loading
                _savingState.value = SavingState.Error("未实现")
            } catch (e: Exception) {
                Log.e("AddFamilyVM", "创建家庭关系时发生错误", e)
                _savingState.value = SavingState.Error("创建家庭关系异常: ${e.message}")
            }
        }
    }
    
    // 添加需要登录状态
    sealed class SavingState {
        object Idle : SavingState()
        object Loading : SavingState()
        data class Success(val message: String) : SavingState()
        data class Error(val message: String) : SavingState()
        data class NeedLogin(val message: String) : SavingState()
    }
}

class AddNewFamilyMember : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            AIHealthButlerTheme {
                val sessionManager = remember { SessionManager(applicationContext) }
                val repository = remember { Repository() }
                val viewModel = remember { AddNewFamilyMemberViewModel(repository, sessionManager) }
                
                AddNewFamilyMemberScreen(viewModel)
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AddNewFamilyMemberScreen(viewModel: AddNewFamilyMemberViewModel = viewModel()) {
    val context = LocalContext.current
    var relationship by remember { mutableStateOf("") }
    var nickname by remember { mutableStateOf("") }
    var gender by remember { mutableStateOf("") }
    var birthdate by remember { mutableStateOf("") }
    var height by remember { mutableStateOf("") }
    var weight by remember { mutableStateOf("") }

    // 关系选择对话框状态
    var showRelationshipDialog by remember { mutableStateOf(false) }
    // 性别选择对话框状态
    var showGenderDialog by remember { mutableStateOf(false) }
    // 日期选择对话框状态
    var showDatePickerDialog by remember { mutableStateOf(false) }

    // 添加年月日选择器的状态变量
    var selectedYear by remember { mutableStateOf(Calendar.getInstance().get(Calendar.YEAR)) }
    var selectedMonth by remember { mutableStateOf(Calendar.getInstance().get(Calendar.MONTH) + 1) }
    var selectedDay by remember { mutableStateOf(Calendar.getInstance().get(Calendar.DAY_OF_MONTH)) }

    // 身高体重错误信息状态
    var heightError by remember { mutableStateOf("") }
    var weightError by remember { mutableStateOf("") }

    // 保存状态
    val savingState by viewModel.savingState.collectAsState()
    
    // 处理保存状态
    LaunchedEffect(savingState) {
        when (savingState) {
            is AddNewFamilyMemberViewModel.SavingState.Success -> {
                Toast.makeText(context, "家庭成员添加成功", Toast.LENGTH_SHORT).show()
                (context as? ComponentActivity)?.finish()
            }
            is AddNewFamilyMemberViewModel.SavingState.Error -> {
                val errorMsg = (savingState as AddNewFamilyMemberViewModel.SavingState.Error).message
                Toast.makeText(context, errorMsg, Toast.LENGTH_SHORT).show()
            }
            is AddNewFamilyMemberViewModel.SavingState.NeedLogin -> {
                Toast.makeText(context, "请先登录", Toast.LENGTH_SHORT).show()
                // 跳转到登录页面
                context.startActivity(Intent(context, SignInActivity::class.java))
            }
            else -> {}
        }
    }

    // 加载状态
    var isLoading by remember { mutableStateOf(false) }
    LaunchedEffect(savingState) {
        isLoading = savingState is AddNewFamilyMemberViewModel.SavingState.Loading
    }

    val mainColor = Color(0xFF2EB0AC)
    val backgroundColor = Color(0xFFFFFFFF)
    
    val scrollState = rememberScrollState()

    Scaffold(
        containerColor = backgroundColor,
        topBar = {
            Column {
                CenterAlignedTopAppBar(
                    title = {
                        Text(
                            "新增家庭成员",
                            fontSize = 18.sp,
                            fontWeight = FontWeight.Bold,
                        )
                    },
                    navigationIcon = {
                        IconButton(
                            onClick = { (context as? ComponentActivity)?.finish() }
                        ) {
                            Icon(
                                painter = painterResource(id = R.drawable.left_arrow),
                                contentDescription = "返回",
                                modifier = Modifier.size(20.dp)
                            )
                        }
                    },
                    colors = TopAppBarDefaults.centerAlignedTopAppBarColors(
                        containerColor = Color.White
                    )
                )
                // 添加灰色分割线
                Divider(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 6.dp),
                    color = Color(0xFFE0E0E0),
                    thickness = 1.dp
                )
            }
        }
    ) { innerPadding ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(innerPadding)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(horizontal = 24.dp)
                    .background(backgroundColor)
            ) {
                Column(
                    modifier = Modifier
                        .weight(1f)
                        .verticalScroll(scrollState)
                ) {
                    // 与本人的家庭关系
                    Text(
                        text = "与本人的家庭关系",
                        color = Color.Gray,
                        fontSize = 15.sp,
                        modifier = Modifier.padding(top = 16.dp, bottom = 8.dp)
                    )
                    Box(modifier = Modifier.fillMaxWidth()) {
                        OutlinedTextField(
                            value = relationship,
                            onValueChange = { },
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(52.dp),
                            shape = RoundedCornerShape(8.dp),
                            colors = OutlinedTextFieldDefaults.colors(
                                unfocusedBorderColor = Color.LightGray,
                                focusedBorderColor = mainColor,
                                unfocusedContainerColor = Color.White,
                                focusedContainerColor = Color.White
                            ),
                            trailingIcon = {
                                Icon(
                                    painter = painterResource(id = R.drawable.ic_dropdown),
                                    contentDescription = "选择关系",
                                    tint = Color.Gray,
                                    modifier = Modifier.size(18.dp)
                                )
                            },
                            readOnly = true,
                            singleLine = true
                        )
                        
                        // 添加透明可点击层
                        Box(
                            modifier = Modifier
                                .matchParentSize()
                                .clickable(
                                    indication = null,
                                    interactionSource = remember { MutableInteractionSource() }
                                ) { 
                                    showRelationshipDialog = true
                                }
                        )
                    }

                    // 昵称
                    Text(
                        text = "昵称",
                        color = Color.Gray,
                        fontSize = 15.sp,
                        modifier = Modifier.padding(top = 16.dp, bottom = 8.dp)
                    )
                    OutlinedTextField(
                        value = nickname,
                        onValueChange = { nickname = it },
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(52.dp),
                        shape = RoundedCornerShape(8.dp),
                        colors = OutlinedTextFieldDefaults.colors(
                            unfocusedBorderColor = Color.LightGray,
                            focusedBorderColor = mainColor,
                            unfocusedContainerColor = Color.White,
                            focusedContainerColor = Color.White
                        ),
                        singleLine = true
                    )

                    // 性别
                    Text(
                        text = "性别",
                        color = Color.Gray,
                        fontSize = 15.sp,
                        modifier = Modifier.padding(top = 16.dp, bottom = 8.dp)
                    )
                    Box(modifier = Modifier.fillMaxWidth()) {
                        OutlinedTextField(
                            value = gender,
                            onValueChange = { },
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(52.dp),
                            shape = RoundedCornerShape(8.dp),
                            colors = OutlinedTextFieldDefaults.colors(
                                unfocusedBorderColor = Color.LightGray,
                                focusedBorderColor = mainColor,
                                unfocusedContainerColor = Color.White,
                                focusedContainerColor = Color.White
                            ),
                            trailingIcon = {
                                Icon(
                                    painter = painterResource(id = R.drawable.ic_dropdown),
                                    contentDescription = "选择性别",
                                    tint = Color.Gray,
                                    modifier = Modifier.size(18.dp)
                                )
                            },
                            readOnly = true,
                            singleLine = true
                        )
                        
                        // 添加透明可点击层
                        Box(
                            modifier = Modifier
                                .matchParentSize()
                                .clickable(
                                    indication = null,
                                    interactionSource = remember { MutableInteractionSource() }
                                ) { 
                                    showGenderDialog = true
                                }
                        )
                    }

                    // 出生日期
                    Text(
                        text = "出生日期",
                        color = Color.Gray,
                        fontSize = 15.sp,
                        modifier = Modifier.padding(top = 16.dp, bottom = 8.dp)
                    )
                    Box(modifier = Modifier.fillMaxWidth()) {
                        OutlinedTextField(
                            value = birthdate,
                            onValueChange = { },
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(52.dp),
                            shape = RoundedCornerShape(8.dp),
                            colors = OutlinedTextFieldDefaults.colors(
                                unfocusedBorderColor = Color.LightGray,
                                focusedBorderColor = mainColor,
                                unfocusedContainerColor = Color.White,
                                focusedContainerColor = Color.White
                            ),
                            trailingIcon = {
                                Icon(
                                    painter = painterResource(id = R.drawable.ic_calendar_today),
                                    contentDescription = "选择日期",
                                    tint = Color.Gray,
                                    modifier = Modifier.size(22.dp)
                                )
                            },
                            readOnly = true,
                            singleLine = true
                        )
                        
                        // 添加透明可点击层
                        Box(
                            modifier = Modifier
                                .matchParentSize()
                                .clickable(
                                    indication = null,
                                    interactionSource = remember { MutableInteractionSource() }
                                ) { 
                                    // 显示年月日选择对话框
                                    showDatePickerDialog = true
                                }
                        )
                    }

                    // 身高
                    Text(
                        text = "身高",
                        color = Color.Gray,
                        fontSize = 15.sp,
                        modifier = Modifier.padding(top = 16.dp, bottom = 8.dp)
                    )
                    OutlinedTextField(
                        value = height,
                        onValueChange = {
                            // 只允许输入数字
                            if (it.isEmpty() || it.all { char -> char.isDigit() }) {
                                height = it
                                heightError = if (it.isEmpty() || it.toIntOrNull() ?: 0 <= 0) {
                                    "身高必须大于0"
                                } else {
                                    ""
                                }
                            }
                        },
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(52.dp),
                        shape = RoundedCornerShape(8.dp),
                        colors = OutlinedTextFieldDefaults.colors(
                            unfocusedBorderColor = Color.LightGray,
                            focusedBorderColor = mainColor,
                            unfocusedContainerColor = Color.White,
                            focusedContainerColor = Color.White
                        ),
                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                        trailingIcon = {
                            Text(
                                text = "cm",
                                color = Color.Gray,
                                fontSize = 15.sp,
                                modifier = Modifier.padding(end = 16.dp)
                            )
                        },
                        singleLine = true,
                        isError = heightError.isNotEmpty()
                    )
                    if (heightError.isNotEmpty()) {
                        Text(
                            text = heightError,
                            color = MaterialTheme.colorScheme.error,
                            fontSize = 12.sp,
                            modifier = Modifier.padding(start = 8.dp)
                        )
                    }

                    // 体重
                    Text(
                        text = "体重",
                        color = Color.Gray,
                        fontSize = 15.sp,
                        modifier = Modifier.padding(top = 16.dp, bottom = 8.dp)
                    )
                    OutlinedTextField(
                        value = weight,
                        onValueChange = {
                            // 允许输入数字和小数点
                            val isValid = it.isEmpty() || it.all { char -> char.isDigit() || char == '.' } && it.count { it == '.' } <= 1
                            if (isValid) {
                                weight = it
                                weightError = if (it.isEmpty() || it.toDoubleOrNull() ?: 0.0 <= 0.0) {
                                    "体重必须大于0"
                                } else {
                                    ""
                                }
                            }
                        },
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(52.dp),
                        shape = RoundedCornerShape(8.dp),
                        colors = OutlinedTextFieldDefaults.colors(
                            unfocusedBorderColor = Color.LightGray,
                            focusedBorderColor = mainColor,
                            unfocusedContainerColor = Color.White,
                            focusedContainerColor = Color.White
                        ),
                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                        trailingIcon = {
                            Text(
                                text = "kg",
                                color = Color.Gray,
                                fontSize = 15.sp,
                                modifier = Modifier.padding(end = 16.dp)
                            )
                        },
                        singleLine = true,
                        isError = weightError.isNotEmpty()
                    )
                    if (weightError.isNotEmpty()) {
                        Text(
                            text = weightError,
                            color = MaterialTheme.colorScheme.error,
                            fontSize = 12.sp,
                            modifier = Modifier.padding(start = 8.dp)
                        )
                    }
                    
                    // 额外的空间，确保内容可以滚动到底部按钮之上
                    Spacer(modifier = Modifier.height(100.dp))
                }

                // 保存按钮 - 固定在底部
                Button(
                    onClick = {
                        Log.d("AddNewFamilyMember", "保存按钮点击")
                        
                        // 验证输入
                        if (relationship.isEmpty()) {
                            Toast.makeText(context, "请选择家庭关系", Toast.LENGTH_SHORT).show()
                            return@Button
                        }
                        
                        if (nickname.isEmpty()) {
                            Toast.makeText(context, "请输入昵称", Toast.LENGTH_SHORT).show()
                            return@Button
                        }
                        
                        if (gender.isEmpty()) {
                            Toast.makeText(context, "请选择性别", Toast.LENGTH_SHORT).show()
                            return@Button
                        }
                        
                        if (birthdate.isEmpty()) {
                            Toast.makeText(context, "请选择出生日期", Toast.LENGTH_SHORT).show()
                            return@Button
                        }
                        
                        if (height.isEmpty() || (height.toIntOrNull() ?: 0) <= 0) {
                            heightError = "身高必须大于0"
                            Toast.makeText(context, heightError, Toast.LENGTH_SHORT).show()
                            return@Button
                        }

                        if (weight.isEmpty() || (weight.toDoubleOrNull() ?: 0.0) <= 0.0) {
                            weightError = "体重必须大于0"
                            Toast.makeText(context, weightError, Toast.LENGTH_SHORT).show()
                            return@Button
                        }

                        // 保存家庭成员信息
                        Log.d("AddNewFamilyMember", "开始保存家庭成员信息")
                        Toast.makeText(context, "正在保存...", Toast.LENGTH_SHORT).show()
                        
                        viewModel.saveFamilyMember(
                            nickname,
                            gender,
                            birthdate,
                            height.toIntOrNull() ?: 0,
                            weight.toFloatOrNull() ?: 0.0f,
                            relationship
                        )
                    },
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(50.dp)
                        .padding(bottom = 8.dp),
                    shape = RoundedCornerShape(8.dp),
                    colors = ButtonDefaults.buttonColors(containerColor = mainColor),
                    enabled = !isLoading
                ) {
                    if (isLoading) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(24.dp),
                            color = Color.White,
                            strokeWidth = 2.dp
                        )
                    } else {
                        Text(
                            text = "保存",
                            fontSize = 16.sp,
                            color = Color.White
                        )
                    }
                }

                Spacer(modifier = Modifier.height(24.dp))
            }
        }
    }

    // 关系选择对话框
    if (showRelationshipDialog) {
        Dialog(
            onDismissRequest = { showRelationshipDialog = false },
            properties = DialogProperties()
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(Color.White, RoundedCornerShape(8.dp))
                    .padding(16.dp)
            ) {
                Text(
                    "选择关系",
                    fontWeight = FontWeight.Bold,
                    fontSize = 16.sp,
                    modifier = Modifier.padding(bottom = 16.dp)
                )
                
                Column(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    // 添加各种家庭关系选项
                    val relationships = listOf("父亲", "母亲", "配偶", "子女", "兄弟", "姐妹", "爷爷/外公", "奶奶/外婆", "其他")
                    relationships.forEachIndexed { index, rel ->
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    relationship = rel
                                    showRelationshipDialog = false
                                }
                                .padding(vertical = 12.dp),
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.Center
                        ) {
                            Text(rel, fontSize = 15.sp)
                        }
                        
                        if (index < relationships.size - 1) {
                            Divider(color = Color.LightGray)
                        }
                    }
                }
            }
        }
    }

    // 性别选择对话框
    if (showGenderDialog) {
        GenderSelectionDialog(
            onDismiss = { showGenderDialog = false },
            onGenderSelected = { selectedGender ->
                gender = selectedGender
                showGenderDialog = false
            }
        )
    }

    // 日期选择对话框
    if (showDatePickerDialog) {
        Dialog(
            onDismissRequest = { showDatePickerDialog = false },
            properties = DialogProperties(
                dismissOnBackPress = true,
                dismissOnClickOutside = true,
                usePlatformDefaultWidth = false // 使对话框可以更宽
            )
        ) {
            Card(
                modifier = Modifier
                    .fillMaxWidth(1f) // 设置为屏幕宽度的100%
                    .padding(16.dp),
                shape = RoundedCornerShape(16.dp),
                colors = CardDefaults.cardColors(containerColor = Color.White), // 确保背景为白色
                elevation = CardDefaults.cardElevation(defaultElevation = 6.dp)
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(20.dp) // 增加内边距
                ) {
                    Text(
                        "选择出生日期",
                        fontWeight = FontWeight.Bold,
                        fontSize = 18.sp,
                        modifier = Modifier.padding(bottom = 16.dp)
                    )
                    
                    // 年份选择
                    Text("年份", 
                        fontSize = 16.sp, 
                        color = Color.Gray, 
                        fontWeight = FontWeight.Medium,
                        modifier = Modifier.padding(top = 8.dp, bottom = 4.dp)
                    )
                    LazyRow(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 8.dp)
                    ) {
                        // 显示从1950年到当前年份的选项
                        val currentYear = Calendar.getInstance().get(Calendar.YEAR)
                        val years = (1950..currentYear).toList().reversed()
                        
                        items(years) { year ->
                            Box(
                                modifier = Modifier
                                    .padding(end = 8.dp) // 减小间距
                                    .clip(RoundedCornerShape(8.dp))
                                    .background(if (selectedYear == year) mainColor else Color.LightGray.copy(alpha = 0.2f))
                                    .clickable { selectedYear = year }
                                    .padding(horizontal = 12.dp, vertical = 8.dp), // 减小内边距
                                contentAlignment = Alignment.Center
                            ) {
                                Text(
                                    text = year.toString(),
                                    color = if (selectedYear == year) Color.White else Color.Black,
                                    fontWeight = if (selectedYear == year) FontWeight.Bold else FontWeight.Normal,
                                    fontSize = 14.sp // 缩小字体大小
                                )
                            }
                        }
                    }
                    
                    // 月份选择
                    Text("月份", 
                        fontSize = 16.sp, 
                        color = Color.Gray, 
                        fontWeight = FontWeight.Medium,
                        modifier = Modifier.padding(top = 16.dp, bottom = 4.dp)
                    )
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 8.dp),
                        horizontalArrangement = Arrangement.SpaceEvenly
                    ) {
                        for (month in 1..6) {
                            Box(
                                modifier = Modifier
                                    .weight(1f)
                                    .padding(horizontal = 3.dp) // 减小间距
                                    .clip(RoundedCornerShape(8.dp))
                                    .background(if (selectedMonth == month) mainColor else Color.LightGray.copy(alpha = 0.2f))
                                    .clickable { selectedMonth = month }
                                    .padding(vertical = 10.dp), // 减小内边距
                                contentAlignment = Alignment.Center
                            ) {
                                Text(
                                    text = month.toString(),
                                    color = if (selectedMonth == month) Color.White else Color.Black,
                                    fontSize = 14.sp, // 缩小字体大小
                                    fontWeight = if (selectedMonth == month) FontWeight.Bold else FontWeight.Normal
                                )
                            }
                        }
                    }
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 8.dp),
                        horizontalArrangement = Arrangement.SpaceEvenly
                    ) {
                        for (month in 7..12) {
                            Box(
                                modifier = Modifier
                                    .weight(1f)
                                    .padding(horizontal = 3.dp)
                                    .clip(RoundedCornerShape(8.dp))
                                    .background(if (selectedMonth == month) mainColor else Color.LightGray.copy(alpha = 0.2f))
                                    .clickable { selectedMonth = month }
                                    .padding(vertical = 10.dp),
                                contentAlignment = Alignment.Center
                            ) {
                                Text(
                                    text = month.toString(),
                                    color = if (selectedMonth == month) Color.White else Color.Black,
                                    fontSize = 14.sp,
                                    fontWeight = if (selectedMonth == month) FontWeight.Bold else FontWeight.Normal
                                )
                            }
                        }
                    }
                    
                    // 日期选择
                    Text("日期", 
                        fontSize = 16.sp, 
                        color = Color.Gray, 
                        fontWeight = FontWeight.Medium,
                        modifier = Modifier.padding(top = 16.dp, bottom = 4.dp)
                    )
                    
                    // 计算当前选择的年月有多少天
                    val calendar = Calendar.getInstance()
                    calendar.set(selectedYear, selectedMonth - 1, 1)
                    val daysInMonth = calendar.getActualMaximum(Calendar.DAY_OF_MONTH)
                    
                    LazyVerticalGrid(
                        columns = GridCells.Fixed(7),
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(200.dp)
                            .padding(vertical = 8.dp)
                    ) {
                        items(daysInMonth) { index ->
                            val day = index + 1
                            Box(
                                modifier = Modifier
                                    .padding(3.dp) // 减小间距
                                    .aspectRatio(1f)
                                    .clip(CircleShape)
                                    .background(if (selectedDay == day) mainColor else Color.LightGray.copy(alpha = 0.2f))
                                    .clickable { selectedDay = day },
                                contentAlignment = Alignment.Center // 确保文本居中
                            ) {
                                Text(
                                    text = day.toString(),
                                    color = if (selectedDay == day) Color.White else Color.Black,
                                    fontSize = 14.sp,
                                    fontWeight = if (selectedDay == day) FontWeight.Bold else FontWeight.Normal,
                                    textAlign = TextAlign.Center // 确保文本居中
                                )
                            }
                        }
                    }
                    
                    // 确认按钮
                    Button(
                        onClick = {
                            // 更新birthdate
                            val calendar = Calendar.getInstance()
                            calendar.set(selectedYear, selectedMonth - 1, selectedDay)
                            val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
                            birthdate = dateFormat.format(calendar.time)
                            
                            showDatePickerDialog = false
                        },
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(top = 20.dp)
                            .height(48.dp), // 增加按钮高度
                        shape = RoundedCornerShape(8.dp),
                        colors = ButtonDefaults.buttonColors(containerColor = mainColor)
                    ) {
                        Text("确认", fontSize = 16.sp)
                    }
                }
            }
        }
    }
}


