package com.example.aihealthbutler

import android.Manifest
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.provider.Settings
import android.util.Log
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.core.content.ContextCompat
import com.example.aihealthbutler.ui.theme.AIHealthButlerTheme
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.aihealthbutler.api.SessionManager
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import okhttp3.OkHttpClient
import okhttp3.RequestBody.Companion.asRequestBody
import java.io.File
import java.util.concurrent.TimeUnit
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import retrofit2.http.Multipart
import retrofit2.http.POST
import retrofit2.http.Part
import retrofit2.http.Header
import coil.compose.AsyncImage
import coil.request.ImageRequest
import com.google.gson.JsonDeserializationContext
import com.google.gson.JsonDeserializer
import com.google.gson.JsonElement
import java.lang.reflect.Type
import com.google.gson.JsonParseException
import androidx.core.net.toUri
import com.example.aihealthbutler.api.FolderItem
import com.example.aihealthbutler.api.FolderUploadData
import com.example.aihealthbutler.api.FolderResponseDeserializer
import retrofit2.http.Body
import com.example.aihealthbutler.api.Repository
import com.example.aihealthbutler.api.MedicationPlanRequest
import com.example.aihealthbutler.api.CreateFolderRequest
import com.example.aihealthbutler.api.PersonInfoData
import com.example.aihealthbutler.api.StandardFolderResponse
import kotlinx.coroutines.flow.firstOrNull
import android.app.Activity
import android.os.Environment
import android.content.SharedPreferences
import android.content.Context
import com.example.aihealthbutler.utils.ImageCacheManager

// 辅助函数：修复图片URL
private fun fixImageUrl(url: String?): String? {
    if (url == null || url.isBlank()) return null

    return when {
        // 如果是content URI，直接返回（Android可以处理）
        url.startsWith("content://") -> url
        // 如果已经是正确的HTTP/HTTPS URL，直接返回
        url.startsWith("http://") || url.startsWith("https://") -> url
        // 如果是HTTP/HTTPS URL，修复错误的格式
        url.contains("https://aie-picture.https://") -> url.replace("https://aie-picture.https://", "https://")
        // 如果是Android应用内部存储路径，转换为file URI
        url.startsWith("/data/user/0/com.example.aihealthbutler/") -> "file://$url"
        // 如果是Windows文件路径，转换为HTTPS URL（假设有图片服务器）
        url.contains(":\\") -> {
            // 提取文件名
            val fileName = url.substringAfterLast("\\")
            "https://qcx.yuneyang.top/api/images/$fileName"
        }
        // 如果是Linux文件路径，转换为file URI
        url.startsWith("/") -> "file://$url"
        // 其他情况，假设是文件名，构造HTTPS URL
        else -> "https://qcx.yuneyang.top/api/images/$url"
    }
}

// 创建Retrofit实例
object UploadRetrofitClient {
    private const val BASE_URL = "https://qcx.yuneyang.top/api/"

    // 创建OkHttpClient，增加超时设置
    private val okHttpClient = OkHttpClient.Builder()
        .connectTimeout(30, TimeUnit.SECONDS)
        .readTimeout(30, TimeUnit.SECONDS)
        .writeTimeout(30, TimeUnit.SECONDS)
        .build()

    val instance: Retrofit by lazy {
        Retrofit.Builder()
            .baseUrl(BASE_URL)
            .client(okHttpClient)
            .addConverterFactory(GsonConverterFactory.create())
            .build()
    }
    
    val uploadApi: UploadApi by lazy {
        instance.create(UploadApi::class.java)
    }
}

// 定义一个专门用于上传的API接口
interface UploadApi {
    @Multipart
    @POST("folder")
    suspend fun uploadFile(
        @Header("Authorization") token: String,
        @Part image: MultipartBody.Part
    ): UploadResponse
}

// 上传响应数据模型
data class UploadResponse(
    val code: Int,
    val message: String,
    val data: UploadData
)

data class UploadData(
    val folder_id: Int,
    val url: String
)

// ViewModel
class UploadViewModel(private val sessionManager: SessionManager, private val context: Context) : ViewModel() {
    private val _isUploading = MutableStateFlow(false)
    val isUploading: StateFlow<Boolean> = _isUploading.asStateFlow()
    
    private val _uploadError = MutableStateFlow<String?>(null)
    val uploadError: StateFlow<String?> = _uploadError.asStateFlow()
    
    private val _uploadSuccess = MutableStateFlow(false)
    val uploadSuccess: StateFlow<Boolean> = _uploadSuccess.asStateFlow()
    
    private val _imageUrl = MutableStateFlow<String?>(null)
    val imageUrl: StateFlow<String?> = _imageUrl.asStateFlow()

    private val _userInfo = MutableStateFlow<PersonInfoData?>(null)
    val userInfo: StateFlow<PersonInfoData?> = _userInfo.asStateFlow()

    private val repository = Repository()

    init {
        loadUserInfo()
    }
    
    // 添加folderId属性用于编辑模式
    private var currentFolderId: Int? = null

    fun setFolderId(folderId: String?) {
        currentFolderId = folderId?.toIntOrNull()
        Log.d("UploadViewModel", "设置文件夹ID: $currentFolderId")
    }

    fun uploadImage(imageUri: Uri) {
        viewModelScope.launch {
            try {
                _isUploading.value = true
                _uploadError.value = null
                _uploadSuccess.value = false

                // 获取token
                val token = sessionManager.token.firstOrNull() ?: throw Exception("未登录")

                Log.d("UploadViewModel", "开始上传图片: $imageUri, 文件夹ID: $currentFolderId")

                // 尝试两种方法：1. 保存到本地并使用真实路径 2. 使用虚拟路径
                var uploadSuccess = false

                // 方法1：保存图片到本地并使用真实路径
                val realPath = saveImageToLocalPath(imageUri)
                if (realPath != null) {
                    Log.d("UploadViewModel", "保存到本地路径: $realPath")
                    val request = CreateFolderRequest(picture = realPath)

                    // 根据是否有folderId决定是创建还是更新
                    val result = if (currentFolderId != null) {
                        Log.d("UploadViewModel", "更新资料夹: $currentFolderId")
                        repository.updateFolder(token, currentFolderId!!, request)
                    } else {
                        Log.d("UploadViewModel", "创建新资料夹")
                        repository.createFolder(token, request)
                    }

                    result.fold(
                        onSuccess = { response ->
                            if (response.code == 200 && response.data != null) {
                                _uploadSuccess.value = true
                                // 保存本地图片URI而不是服务器路径
                                _imageUrl.value = imageUri.toString()

                                // 如果是更新操作，保存到缓存
                                if (currentFolderId != null) {
                                    ImageCacheManager.saveImageUri(context, currentFolderId!!, imageUri.toString())
                                }

                                Log.d("UploadViewModel", "真实路径操作成功: ${response.data}")
                                Log.d("UploadViewModel", "保存本地URI: ${imageUri}")
                                uploadSuccess = true
                            } else {
                                Log.w("UploadViewModel", "真实路径操作失败: ${response.message}")
                            }
                        },
                        onFailure = { e ->
                            Log.w("UploadViewModel", "真实路径操作异常: ${e.message}")
                        }
                    )
                }

                // 方法2：如果真实路径上传失败，尝试虚拟路径
                if (!uploadSuccess) {
                    Log.d("UploadViewModel", "尝试虚拟路径操作")
                    val virtualPath = convertUriToServerPath(imageUri)
                    Log.d("UploadViewModel", "虚拟路径: $virtualPath")

                    val request = CreateFolderRequest(picture = virtualPath)

                    // 根据是否有folderId决定是创建还是更新
                    val result = if (currentFolderId != null) {
                        Log.d("UploadViewModel", "更新资料夹: $currentFolderId")
                        repository.updateFolder(token, currentFolderId!!, request)
                    } else {
                        Log.d("UploadViewModel", "创建新资料夹")
                        repository.createFolder(token, request)
                    }

                    result.fold(
                        onSuccess = { response ->
                            if (response.code == 200 && response.data != null) {
                                _uploadSuccess.value = true
                                // 保存本地图片URI而不是服务器路径
                                _imageUrl.value = imageUri.toString()

                                // 如果是更新操作，保存到缓存
                                if (currentFolderId != null) {
                                    ImageCacheManager.saveImageUri(context, currentFolderId!!, imageUri.toString())
                                }

                                Log.d("UploadViewModel", "虚拟路径操作成功: ${response.data}")
                                Log.d("UploadViewModel", "保存本地URI: ${imageUri}")
                                uploadSuccess = true
                            } else {
                                _uploadError.value = response.message ?: "操作失败"
                                Log.e("UploadViewModel", "虚拟路径操作失败: ${response.message}")
                            }
                        },
                        onFailure = { e ->
                            _uploadError.value = "操作失败: ${e.message}"
                            Log.e("UploadViewModel", "虚拟路径操作异常", e)
                        }
                    )
                }


            } catch (e: Exception) {
                _uploadError.value = "操作异常: ${e.message}"
                Log.e("UploadViewModel", "操作异常", e)
            } finally {
                _isUploading.value = false
            }
        }
    }



    
    fun setImageUrl(url: String?) {
        _imageUrl.value = url
    }

    // 将Android URI转换为服务器期望的路径格式
    private fun convertUriToServerPath(imageUri: Uri): String {
        return try {
            // 根据Postman文档，服务器期望的是类似 "D:\\Users\\HP\\Desktop\\aie\\image\\img.png" 的路径
            // 我们需要创建一个服务器能理解的路径格式

            val timestamp = System.currentTimeMillis()
            val fileName = "uploaded_image_$timestamp.jpg"

            // 尝试不同的路径格式，看服务器接受哪种
            when {
                // Windows风格路径（根据Postman示例）
                imageUri.toString().contains("picker") -> {
                    "D:\\Users\\HP\\Desktop\\aie\\image\\$fileName"
                }
                // Linux风格路径
                imageUri.toString().contains("media") -> {
                    "/storage/images/$fileName"
                }
                // 简单文件名
                else -> {
                    fileName
                }
            }
        } catch (e: Exception) {
            Log.e("UploadViewModel", "路径转换失败", e)
            "uploaded_image_${System.currentTimeMillis()}.jpg"
        }
    }

    // 保存图片到本地并返回路径
    private suspend fun saveImageToLocalPath(imageUri: Uri): String? = try {
        val context = sessionManager.getContext()
        val inputStream = context.contentResolver.openInputStream(imageUri)
        val fileName = "uploaded_image_${System.currentTimeMillis()}.jpg"

        // 使用应用内部存储，不需要额外权限
        val file = File(context.filesDir, fileName)

        inputStream?.use { input ->
            file.outputStream().use { output ->
                input.copyTo(output)
            }
        }

        Log.d("UploadViewModel", "图片保存成功: ${file.absolutePath}")
        file.absolutePath
    } catch (e: Exception) {
        Log.e("UploadViewModel", "保存图片失败", e)
        null
    }

    // 加载用户信息
    private fun loadUserInfo() {
        viewModelScope.launch {
            try {
                val token = sessionManager.token.firstOrNull() ?: return@launch
                val defaultId = sessionManager.defaultId.firstOrNull() ?: return@launch

                val result = repository.getPersonInfo(token, defaultId)
                result.fold(
                    onSuccess = { response ->
                        if (response.code == 200 && response.data != null) {
                            _userInfo.value = response.data
                            Log.d("UploadViewModel", "加载用户信息成功: ${response.data.name}")
                        } else {
                            Log.w("UploadViewModel", "加载用户信息失败: ${response.message}")
                        }
                    },
                    onFailure = { e ->
                        Log.e("UploadViewModel", "加载用户信息失败", e)
                    }
                )
            } catch (e: Exception) {
                Log.e("UploadViewModel", "加载用户信息异常", e)
            }
        }
    }

    // 将URI转换为文件路径
    private fun convertUriToPath(uri: Uri): String? {
        return try {
            when (uri.scheme) {
                "file" -> {
                    // 文件URI，直接返回路径
                    uri.path
                }
                "content" -> {
                    // 内容URI，尝试获取实际路径
                    // 对于Android 10+，由于作用域存储限制，可能无法获取真实路径
                    // 在这种情况下，我们使用一个模拟路径格式
                    val fileName = "image_${System.currentTimeMillis()}.jpg"
                    "/storage/emulated/0/Pictures/$fileName"
                }
                else -> {
                    // 其他类型，使用URI字符串
                    uri.toString()
                }
            }
        } catch (e: Exception) {
            Log.e("UploadViewModel", "URI转换失败", e)
            null
        }
    }
}

class UploadMaterialsActivity : ComponentActivity() {
    // 选择的图片URI和URL
    private val selectedImageUri = mutableStateOf<Uri?>(null)
    private val folderId = mutableStateOf<String?>(null)
    private val imageUrl = mutableStateOf<String?>(null)
    
    // 注册相册选择活动结果
    private val pickImage = registerForActivityResult(ActivityResultContracts.GetContent()) { uri ->
        uri?.let {
            // 保存选择的图片URI
            selectedImageUri.value = it
            imageUrl.value = null  // 清空之前的URL，因为选择了新图片
            
            // 显示成功消息
            Toast.makeText(this, "图片选择成功", Toast.LENGTH_SHORT).show()
            
            // 移除立即上传的逻辑
            //viewModel.uploadImage(it, this)立即开始上传
        }
    }
    
    // 权限请求
    private val requestPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        if (permissions.all { it.value }) {
            // 所有权限都已获取
            pickFromGallery()
        } else {
            // 显示权限设置对话框
            showPermissionSettingsDialog = true
        }
    }
    
    // 显示权限设置对话框标志
    private var showPermissionSettingsDialog = false
    
    // 会话管理器和ViewModel
    private lateinit var sessionManager: SessionManager
    private lateinit var viewModel: UploadViewModel

    // 请求权限
    fun checkAndRequestPermissions() {
        // 根据Android版本请求适当的权限
        val permissions = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            // Android 13+: READ_MEDIA_IMAGES
            arrayOf(Manifest.permission.READ_MEDIA_IMAGES)
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            // Android 11+: 无需READ_EXTERNAL_STORAGE权限，直接使用MediaStore
            arrayOf()
        } else {
            // Android 10及以下: 需要READ_EXTERNAL_STORAGE权限
            arrayOf(Manifest.permission.READ_EXTERNAL_STORAGE)
        }
        
        // 如果没有需要请求的权限，或者所有权限都已经授予，直接打开相册
        if (permissions.isEmpty() || permissions.all {
            ContextCompat.checkSelfPermission(this, it) == PackageManager.PERMISSION_GRANTED
        }) {
            pickFromGallery()
        } else {
            requestPermissionLauncher.launch(permissions)
        }
    }

    // 打开相册
    fun pickFromGallery() {
        try {
            // 使用GetContent API打开系统图库
            pickImage.launch("image/*")
        } catch (e: Exception) {
            Log.e("UploadMaterialsActivity", "打开相册失败", e)
            Toast.makeText(this, "打开相册失败: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        
        // 初始化会话管理器和ViewModel
        sessionManager = SessionManager(applicationContext)
        viewModel = UploadViewModel(sessionManager, applicationContext)
        
        // 从Intent中获取数据
        intent.getStringExtra("IMAGE_URI")?.let { uriString ->
            try {
                selectedImageUri.value = Uri.parse(uriString)
                Log.d("UploadMaterialsActivity", "收到图片URI: $uriString")
            } catch (e: Exception) {
                Log.e("UploadMaterialsActivity", "URI解析失败: $uriString", e)
            }
        }
        
        intent.getStringExtra("FOLDER_ID")?.let { id ->
            folderId.value = id
            viewModel.setFolderId(id)
            Log.d("UploadMaterialsActivity", "收到文件夹ID: $id")
        }
        
        intent.getStringExtra("IMAGE_URL")?.let { url ->
            Log.d("UploadMaterialsActivity", "=== 编辑模式图片处理 ===")
            Log.d("UploadMaterialsActivity", "收到原始图片URL: '$url'")

            // 检查是否有缓存的本地图片
            val cachedUri = folderId.value?.toIntOrNull()?.let { id ->
                val cached = ImageCacheManager.getImageUri(applicationContext, id)
                Log.d("UploadMaterialsActivity", "检查缓存: folderId=$id, cached='$cached'")
                cached
            }

            // 优先使用缓存的URI，但要检查其有效性
            val displayUrl = if (cachedUri != null && isUriAccessible(cachedUri)) {
                Log.d("UploadMaterialsActivity", "使用有效的缓存URI")
                cachedUri
            } else {
                Log.d("UploadMaterialsActivity", "缓存URI无效或不存在，使用服务器URL")
                // 如果缓存URI无效，清除缓存
                folderId.value?.toIntOrNull()?.let { id ->
                    ImageCacheManager.removeImageUri(applicationContext, id)
                }
                url
            }

            imageUrl.value = displayUrl
            viewModel.setImageUrl(displayUrl)
            // 编辑模式下清空selectedImageUri，确保显示网络图片
            selectedImageUri.value = null

            Log.d("UploadMaterialsActivity", "缓存图片URI: '$cachedUri'")
            Log.d("UploadMaterialsActivity", "最终显示URL: '$displayUrl'")
            Log.d("UploadMaterialsActivity", "selectedImageUri已清空: ${selectedImageUri.value}")
            Log.d("UploadMaterialsActivity", "imageUrl.value设置为: '${imageUrl.value}'")
            Log.d("UploadMaterialsActivity", "===============================")
        }
        
        setContent {
            AIHealthButlerTheme {
                val isUploading by viewModel.isUploading.collectAsState()
                val uploadError by viewModel.uploadError.collectAsState()
                val uploadSuccess by viewModel.uploadSuccess.collectAsState()
                val currentImageUrl by viewModel.imageUrl.collectAsState()
                val userInfo by viewModel.userInfo.collectAsState()
                
                // 使用URL来显示图片，如果有的话
                val displayImageUrl = currentImageUrl ?: imageUrl.value
                UploadMaterialsScreen(
                    selectedImageUri = selectedImageUri.value,
                    imageUrl = displayImageUrl,
                    folderId = folderId.value,
                    onImageSelect = { checkAndRequestPermissions() },
                    showPermissionDialog = showPermissionSettingsDialog,
                    onDismissPermissionDialog = { showPermissionSettingsDialog = false },
                    onOpenSettings = { openAppSettings() },
                    isUploading = isUploading,
                    uploadError = uploadError,
                    uploadSuccess = uploadSuccess,
                    userInfo = userInfo,
                    onConfirmUpload = {
                        selectedImageUri.value?.let { uri ->
                            viewModel.uploadImage(uri)
                        } ?: run {
                            Toast.makeText(this, "请先选择图片", Toast.LENGTH_SHORT).show()
                        }
                    }
                )
                // 上传成功后自动返回并setResult
                LaunchedEffect(uploadSuccess) {
                    if (uploadSuccess) {
                        Toast.makeText(applicationContext, "上传成功，即将返回", Toast.LENGTH_SHORT).show()
                        // 获取最新图片URL
                        val url = viewModel.imageUrl.value
                        val intent = Intent().apply {
                            putExtra("IMAGE_URL", url)
                            // 资料夹ID可通过后端返回的data获取，这里假设有
                            // putExtra("FOLDER_ID", folderId)
                        }
                        setResult(Activity.RESULT_OK, intent)
                        kotlinx.coroutines.delay(1200)
                        finish()
                    }
                }
            }
        }
    }
    
    // 检查URI是否可访问
    private fun isUriAccessible(uriString: String): Boolean {
        return try {
            val uri = Uri.parse(uriString)
            // 尝试打开输入流来检查URI是否有效
            contentResolver.openInputStream(uri)?.use {
                true
            } ?: false
        } catch (e: Exception) {
            Log.w("UploadMaterialsActivity", "URI不可访问: $uriString, 错误: ${e.message}")
            false
        }
    }

    // 打开应用设置
    private fun openAppSettings() {
        val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
            data = Uri.fromParts("package", packageName, null)
        }
        startActivity(intent)
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun UploadMaterialsScreen(
    selectedImageUri: Uri? = null,
    imageUrl: String? = null,
    folderId: String? = null,
    onImageSelect: () -> Unit = {},
    showPermissionDialog: Boolean = false,
    onDismissPermissionDialog: () -> Unit = {},
    onOpenSettings: () -> Unit = {},
    isUploading: Boolean = false,
    uploadError: String? = null,
    uploadSuccess: Boolean = false,
    userInfo: PersonInfoData? = null,
    onConfirmUpload: () -> Unit = {}
) {
    val context = LocalContext.current
    
    // 权限设置对话框
    if (showPermissionDialog) {
        PermissionDialog(
            onDismiss = onDismissPermissionDialog,
            onConfirm = onOpenSettings
        )
    }
    
    Scaffold(
        topBar = {
            CenterAlignedTopAppBar(
                title = {
                    Text(
                        text = "上传资料",
                        fontSize = 20.sp,
                        fontWeight = FontWeight.Bold
                    )
                },
                navigationIcon = {
                    IconButton(onClick = {
                        val activity = context as? ComponentActivity
                        activity?.finish()
                    }) {
                        Icon(
                            painter = painterResource(id = R.drawable.left_arrow),
                            contentDescription = "返回",
                            modifier = Modifier.size(24.dp)
                        )
                    }
                },
                colors = TopAppBarDefaults.centerAlignedTopAppBarColors(
                    containerColor = Color(0xFFF5F6F7)
                )
            )
        },
        containerColor = Color(0xFFF5F6F7)
    ) { innerPadding ->
        val scrollState = rememberScrollState()

        Column(
            modifier = Modifier
                .fillMaxSize()
                .background(Color(0xFFF5F6F7))
                .padding(innerPadding)
                .verticalScroll(scrollState) // 添加垂直滚动
                .padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // 用户信息
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 24.dp),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = if (userInfo != null) {
                        "${userInfo.name}|${userInfo.sexText}|${userInfo.age}岁"
                    } else {
                        "加载中..."
                    },
                    fontSize = 16.sp,
                    color = Color.Gray
                )
                
//                Row(
//                    verticalAlignment = Alignment.CenterVertically,
//                    modifier = Modifier.clickable { /* 切换成员 */ }
//                ) {
//                    Text(
//                        text = "切换成员",
//                        fontSize = 14.sp,
//                        color = Color.Black
//                    )
//                    Icon(
//                        painter = painterResource(id = R.drawable.right_arrow),
//                        contentDescription = null,
//                        modifier = Modifier.size(16.dp)
//                    )
//                }
            }
            
            // 显示文件夹ID（如果有）
            folderId?.let {
                Text(
                    text = "资料夹ID: $it",
                    fontSize = 14.sp,
                    color = Color.DarkGray,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 8.dp)
                )
            }

            // 编辑模式提示
            if (folderId != null && imageUrl != null) {
                Text(
                    text = "编辑模式 - 当前图片",
                    fontSize = 12.sp,
                    color = Color(0xFF22C1C3),
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 4.dp)
                )
            }
            
            // 图片选择区域
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(5.dp)
                    .clickable { onImageSelect() },
                shape = RoundedCornerShape(12.dp),
                colors = CardDefaults.cardColors(containerColor = Color.White)
            ) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .wrapContentHeight() // 自适应高度
                        .padding(8.dp),
                    contentAlignment = Alignment.Center
                ) {
                    // 添加调试日志
                    LaunchedEffect(selectedImageUri, imageUrl) {
                        Log.d("UploadMaterialsScreen", "=== 图片显示状态 ===")
                        Log.d("UploadMaterialsScreen", "selectedImageUri: '$selectedImageUri'")
                        Log.d("UploadMaterialsScreen", "imageUrl: '$imageUrl'")
                        Log.d("UploadMaterialsScreen", "将显示: ${when {
                            selectedImageUri != null -> "本地图片"
                            imageUrl != null -> "网络图片"
                            else -> "占位图"
                        }}")
                        Log.d("UploadMaterialsScreen", "==================")
                    }

                    // 优先显示本地URI图片，如果没有则显示网络图片URL
                    if (selectedImageUri != null) {
                        AsyncImage(
                            model = ImageRequest.Builder(LocalContext.current)
                                .data(selectedImageUri)
                                .crossfade(true)
                                .build(),
                            contentDescription = "已选择图片",
                            modifier = Modifier
                                .fillMaxWidth() // 横向填充
                                .wrapContentHeight() // 高度自适应
                                .clip(RoundedCornerShape(8.dp)),
                            contentScale = ContentScale.FillWidth, // 横向填充，纵向自适应
                            error = painterResource(id = R.drawable.ic_image),
                            placeholder = painterResource(id = R.drawable.ic_image)
                        )
                        
                        // 添加调试日志
                        LaunchedEffect(selectedImageUri) {
                            Log.d("UploadMaterialsScreen", "显示本地图片: $selectedImageUri")
                        }
                    } else if (imageUrl != null) {
                        val fixedImageUrl = fixImageUrl(imageUrl)
                        AsyncImage(
                            model = ImageRequest.Builder(LocalContext.current)
                                .data(fixedImageUrl)
                                .crossfade(true)
                                .allowHardware(false) // 有时候硬件加速会导致问题
                                .build(),
                            contentDescription = "已上传图片",
                            modifier = Modifier
                                .fillMaxWidth() // 横向填充
                                .wrapContentHeight() // 高度自适应
                                .clip(RoundedCornerShape(8.dp)),
                            contentScale = ContentScale.FillWidth, // 横向填充，纵向自适应
                            error = painterResource(id = R.drawable.ic_image),
                            placeholder = painterResource(id = R.drawable.ic_image),
                            onSuccess = {
                                Log.d("UploadMaterialsScreen", "网络图片加载成功: $fixedImageUrl")
                            },
                            onError = { error ->
                                Log.e("UploadMaterialsScreen", "网络图片加载失败: $fixedImageUrl, 错误: ${error.result.throwable}")
                                // 如果是权限错误，尝试清除缓存
                                if (error.result.throwable is SecurityException) {
                                    Log.w("UploadMaterialsScreen", "检测到权限错误，可能是URI已失效")
                                }
                            }
                        )

                        // 添加调试日志
                        LaunchedEffect(imageUrl) {
                            Log.d("UploadMaterialsScreen", "=== 网络图片处理 ===")
                            Log.d("UploadMaterialsScreen", "原始图片URL: '$imageUrl'")
                            Log.d("UploadMaterialsScreen", "修复后图片URL: '$fixedImageUrl'")
                            Log.d("UploadMaterialsScreen", "URL类型判断:")
                            Log.d("UploadMaterialsScreen", "  - 是否为空: ${imageUrl.isNullOrBlank()}")
                            Log.d("UploadMaterialsScreen", "  - 是否content URI: ${imageUrl?.startsWith("content://")}")
                            Log.d("UploadMaterialsScreen", "  - 是否HTTP URL: ${imageUrl?.startsWith("http")}")
                            Log.d("UploadMaterialsScreen", "  - 是否Android内部路径: ${imageUrl?.startsWith("/data/user/0/com.example.aihealthbutler/")}")
                            Log.d("UploadMaterialsScreen", "  - 是否文件路径: ${imageUrl?.startsWith("/")}")
                            Log.d("UploadMaterialsScreen", "====================")
                        }
                    } else {
                        // 未选择图片时显示提示
                        Column(
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(200.dp), // 给占位图一个合适的高度
                            verticalArrangement = Arrangement.Center,
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            Icon(
                                painter = painterResource(id = R.drawable.ic_image),
                                contentDescription = "选择图片",
                                tint = Color(0xFF22C1C3),
                                modifier = Modifier.size(60.dp) // 增大图标尺寸
                            )
                            Spacer(modifier = Modifier.height(12.dp))
                            Text(
                                text = "点击选择图片",
                                fontSize = 18.sp,
                                color = Color.Gray,
                                fontWeight = FontWeight.Medium
                            )
                        }
                    }
                    
                    // 显示上传状态
                    if (isUploading) {
                        Box(
                            modifier = Modifier
                                .fillMaxSize()
                                .background(Color(0x80000000)),
                            contentAlignment = Alignment.Center
                        ) {
                            Column(
                                horizontalAlignment = Alignment.CenterHorizontally
                            ) {
                                CircularProgressIndicator(
                                    color = Color.White
                                )
                                Spacer(modifier = Modifier.height(8.dp))
                                Text(
                                    text = "正在上传...",
                                    color = Color.White,
                                    fontSize = 16.sp
                                )
                            }
                        }
                    }
                }
            }
            
            // 错误信息
            if (uploadError != null) {
                Text(
                    text = uploadError,
                    color = Color.Red,
                    fontSize = 14.sp,
                    modifier = Modifier.padding(top = 8.dp)
                )
            }
            
            // 上传成功提示
            if (uploadSuccess) {
                Text(
                    text = "上传成功，即将返回",
                    color = Color(0xFF22C1C3),
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier.padding(top = 16.dp)
                )
            }
            
            // 添加底部间距，确保按钮不会贴边
            Spacer(modifier = Modifier.height(24.dp))

            // 底部确认按钮
            Button(
                onClick = {
                    if ((selectedImageUri != null || imageUrl != null) && !isUploading) {
                        onConfirmUpload()
                    } else if (selectedImageUri == null && imageUrl == null) {
                        Toast.makeText(context, "请先选择图片", Toast.LENGTH_SHORT).show()
                    }
                },
                modifier = Modifier
                    .fillMaxWidth()
                    .height(56.dp),
                shape = RoundedCornerShape(28.dp),
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color(0xFF22C1C3),
                    disabledContainerColor = Color(0xFFAAAAAA)
                ),
                enabled = (selectedImageUri != null || imageUrl != null) && !isUploading
            ) {
                Text(
                    text = "确定上传",
                    fontSize = 18.sp,
                    color = Color.White
                )
            }

            // 底部安全间距，确保内容不会被系统导航栏遮挡
            Spacer(modifier = Modifier.height(16.dp))
        }
    }
}

@Composable
fun PermissionDialog(
    onDismiss: () -> Unit,
    onConfirm: () -> Unit
) {
    Dialog(onDismissRequest = onDismiss) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(containerColor = Color.White)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "需要相册权限",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.Black
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                Text(
                    text = "为了选择图片，我们需要访问您的相册。请在设置中开启相册权限。",
                    fontSize = 16.sp,
                    color = Color.DarkGray,
                    textAlign = TextAlign.Center
                )
                
                Spacer(modifier = Modifier.height(24.dp))
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Button(
                        onClick = onDismiss,
                        modifier = Modifier
                            .weight(1f)
                            .padding(end = 8.dp),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color.LightGray
                        )
                    ) {
                        Text("取消")
                    }
                    
                    Button(
                        onClick = onConfirm,
                        modifier = Modifier
                            .weight(1f)
                            .padding(start = 8.dp),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFF22C1C3)
                        )
                    ) {
                        Text("去设置")
                    }
                }
            }
        }
    }
}


