package com.example.aihealthbutler.utils

import android.content.Context
import android.util.Log
import com.example.aihealthbutler.dify.DifyApiClient
import com.example.aihealthbutler.dify.DifyConfig
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.*
import java.io.IOException
import java.net.InetSocketAddress
import java.net.Socket
import java.util.concurrent.TimeUnit

/**
 * Dify连接测试工具
 * 专门用于诊断和测试Dify API连接问题
 */
object DifyConnectionTester {
    
    private const val TAG = "DifyConnectionTester"
    
    /**
     * 执行完整的Dify连接诊断
     */
    suspend fun runFullDiagnostic(context: Context): DifyDiagnosticResult = withContext(Dispatchers.IO) {
        val result = DifyDiagnosticResult()
        
        Log.d(TAG, "开始Dify连接诊断...")
        
        // 1. 基础网络检查
        result.networkAvailable = NetworkUtils.isNetworkAvailable(context)
        result.networkType = NetworkUtils.getNetworkTypeName(context)
        
        // 2. DNS解析测试
        result.dnsResolution = testDnsResolution()
        
        // 3. Socket连接测试
        result.socketConnection = testSocketConnection()
        
        // 4. HTTP连接测试
        result.httpConnection = testHttpConnection()
        
        // 5. API认证测试
        result.apiAuthentication = testApiAuthentication()
        
        // 6. 完整API测试
        if (result.apiAuthentication) {
            result.fullApiTest = testFullApi()
        }

        // 7. 语音转文字API测试
        if (result.apiAuthentication) {
            result.audioToTextTest = testAudioToTextApi()
        }
        
        Log.d(TAG, "Dify连接诊断完成: ${result.getOverallStatus()}")
        result
    }
    
    /**
     * 测试DNS解析
     */
    private suspend fun testDnsResolution(): Boolean = withContext(Dispatchers.IO) {
        try {
            val host = extractHost(DifyConfig.BASE_URL)
            val address = java.net.InetAddress.getByName(host)
            Log.d(TAG, "DNS解析成功: $host -> ${address.hostAddress}")
            true
        } catch (e: Exception) {
            Log.e(TAG, "DNS解析失败", e)
            false
        }
    }
    
    /**
     * 测试Socket连接
     */
    private suspend fun testSocketConnection(): Boolean = withContext(Dispatchers.IO) {
        try {
            val host = extractHost(DifyConfig.BASE_URL)
            val port = extractPort(DifyConfig.BASE_URL)
            
            Socket().use { socket ->
                socket.connect(InetSocketAddress(host, port), 5000)
                Log.d(TAG, "Socket连接成功: $host:$port")
                true
            }
        } catch (e: Exception) {
            Log.e(TAG, "Socket连接失败", e)
            false
        }
    }
    
    /**
     * 测试HTTPS连接
     */
    private suspend fun testHttpConnection(): Boolean = withContext(Dispatchers.IO) {
        try {
            val client = HttpsConfigHelper.createTestHttpsClient(10)

            val request = Request.Builder()
                .url(DifyConfig.BASE_URL)
                .get()
                .build()

            val response = client.newCall(request).execute()
            val success = response.code in 200..499 // 2xx或4xx都表示服务器可达

            Log.d(TAG, "HTTPS连接测试: ${response.code} - ${if (success) "成功" else "失败"}")
            response.close()
            success
        } catch (e: Exception) {
            Log.e(TAG, "HTTP连接测试失败", e)
            false
        }
    }
    
    /**
     * 测试API认证
     */
    private suspend fun testApiAuthentication(): Boolean = withContext(Dispatchers.IO) {
        try {
            val client = HttpsConfigHelper.createTestHttpsClient(10)

            val request = Request.Builder()
                .url("${DifyConfig.BASE_URL}/chat-messages")
                .get()
                .addHeader("Authorization", "Bearer ${DifyConfig.API_KEY}")
                .build()

            val response = client.newCall(request).execute()
            val success = response.code != 401 // 401表示认证失败

            Log.d(TAG, "HTTPS API认证测试: ${response.code} - ${if (success) "成功" else "认证失败"}")
            response.close()
            success
        } catch (e: Exception) {
            Log.e(TAG, "HTTPS API认证测试失败", e)
            false
        }
    }
    
    /**
     * 测试完整API功能
     */
    private suspend fun testFullApi(): Boolean = withContext(Dispatchers.IO) {
        try {
            val apiClient = DifyApiClient(DifyConfig.API_KEY, DifyConfig.BASE_URL)
            val testResult = apiClient.testApiEndpoint()

            Log.d(TAG, "完整API测试: ${if (testResult.isFullyWorking()) "成功" else "失败"}")
            testResult.isFullyWorking()
        } catch (e: Exception) {
            Log.e(TAG, "完整API测试失败", e)
            false
        }
    }

    /**
     * 测试语音转文字API
     */
    private suspend fun testAudioToTextApi(): Boolean = withContext(Dispatchers.IO) {
        try {
            val client = HttpsConfigHelper.createTestHttpsClient(30) // 语音转文字可能需要更长时间

            val request = Request.Builder()
                .url("${DifyConfig.BASE_URL}/audio-to-text")
                .head() // 使用HEAD请求测试端点是否存在
                .addHeader("Authorization", "Bearer ${DifyConfig.API_KEY}")
                .build()

            val response = client.newCall(request).execute()
            // 405 Method Not Allowed 也表示端点存在，只是不支持HEAD方法
            val success = response.code in 200..499 && response.code != 404

            Log.d(TAG, "语音转文字API测试: ${response.code} - ${if (success) "端点可用" else "端点不可用"}")
            response.close()
            success
        } catch (e: Exception) {
            Log.e(TAG, "语音转文字API测试失败", e)
            false
        }
    }
    
    /**
     * 从URL提取主机名
     */
    private fun extractHost(url: String): String {
        return url.replace("http://", "").replace("https://", "")
            .split("/")[0].split(":")[0]
    }
    
    /**
     * 从URL提取端口
     */
    private fun extractPort(url: String): Int {
        val cleanUrl = url.replace("http://", "").replace("https://", "")
        val parts = cleanUrl.split("/")[0].split(":")
        
        return if (parts.size > 1) {
            parts[1].toIntOrNull() ?: 80
        } else {
            if (url.startsWith("https://")) 443 else 80
        }
    }
}

/**
 * Dify诊断结果
 */
data class DifyDiagnosticResult(
    var networkAvailable: Boolean = false,
    var networkType: String = "",
    var dnsResolution: Boolean = false,
    var socketConnection: Boolean = false,
    var httpConnection: Boolean = false,
    var apiAuthentication: Boolean = false,
    var fullApiTest: Boolean = false,
    var audioToTextTest: Boolean = false
) {
    
    /**
     * 获取总体状态
     */
    fun getOverallStatus(): String {
        return when {
            !networkAvailable -> "网络不可用"
            !dnsResolution -> "DNS解析失败"
            !socketConnection -> "无法连接服务器"
            !httpConnection -> "HTTP连接失败"
            !apiAuthentication -> "API认证失败"
            !fullApiTest -> "API功能异常"
            else -> "连接正常"
        }
    }
    
    /**
     * 获取详细报告
     */
    fun getDetailedReport(): String = buildString {
        appendLine("=== Dify连接诊断报告 ===")
        appendLine()
        
        appendLine("📶 网络状态:")
        appendLine("  可用性: ${if (networkAvailable) "✓ 可用" else "✗ 不可用"}")
        appendLine("  类型: $networkType")
        appendLine()
        
        appendLine("🌐 连接测试:")
        appendLine("  DNS解析: ${if (dnsResolution) "✓ 成功" else "✗ 失败"}")
        appendLine("  Socket连接: ${if (socketConnection) "✓ 成功" else "✗ 失败"}")
        appendLine("  HTTP连接: ${if (httpConnection) "✓ 成功" else "✗ 失败"}")
        appendLine()
        
        appendLine("🔐 API测试:")
        appendLine("  认证测试: ${if (apiAuthentication) "✓ 成功" else "✗ 失败"}")
        appendLine("  功能测试: ${if (fullApiTest) "✓ 成功" else "✗ 失败"}")
        appendLine("  语音转文字: ${if (audioToTextTest) "✓ 可用" else "✗ 不可用"}")
        appendLine()
        
        appendLine("📊 总体状态: ${getOverallStatus()}")
        appendLine()
        
        // 问题建议
        if (!isFullyWorking()) {
            appendLine("🔧 建议解决方案:")
            when {
                !networkAvailable -> {
                    appendLine("  1. 检查网络连接")
                    appendLine("  2. 尝试切换网络")
                    appendLine("  3. 重启设备网络")
                }
                !dnsResolution -> {
                    appendLine("  1. 检查DNS设置")
                    appendLine("  2. 尝试使用其他DNS")
                    appendLine("  3. 检查网络代理")
                }
                !socketConnection -> {
                    appendLine("  1. 检查服务器地址")
                    appendLine("  2. 确认服务器在线")
                    appendLine("  3. 检查防火墙设置")
                }
                !httpConnection -> {
                    appendLine("  1. 检查HTTP协议支持")
                    appendLine("  2. 尝试HTTPS连接")
                    appendLine("  3. 检查代理设置")
                }
                !apiAuthentication -> {
                    appendLine("  1. 验证API密钥")
                    appendLine("  2. 检查密钥格式")
                    appendLine("  3. 联系API提供商")
                }
                !fullApiTest -> {
                    appendLine("  1. 检查API版本")
                    appendLine("  2. 验证请求格式")
                    appendLine("  3. 查看服务器日志")
                }
            }
        }
    }
    
    /**
     * 是否完全正常工作
     */
    fun isFullyWorking(): Boolean {
        return networkAvailable && dnsResolution && socketConnection &&
               httpConnection && apiAuthentication && fullApiTest
    }

    /**
     * 是否支持语音转文字功能
     */
    fun isAudioToTextSupported(): Boolean {
        return isFullyWorking() && audioToTextTest
    }
}
