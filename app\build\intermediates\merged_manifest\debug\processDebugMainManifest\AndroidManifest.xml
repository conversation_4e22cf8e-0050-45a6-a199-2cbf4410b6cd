<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.example.aihealthbutler"
    android:versionCode="1"
    android:versionName="1.0" >

    <uses-sdk
        android:minSdkVersion="24"
        android:targetSdkVersion="35" />

    <!-- 相关应用权限 -->
    <!-- 网络 -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <!-- 存储 -->
    <uses-permission
        android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <!-- 相机 -->
    <uses-permission android:name="android.permission.CAMERA" />
    <!-- 麦克风 -->
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <!-- OPPO设备特殊权限 -->
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <!-- 位置 -->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />

    <!-- 如果需要在Android 15上获取连接信息，需要添加此权限 -->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />

    <permission
        android:name="com.example.aihealthbutler.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
        android:protectionLevel="signature" />

    <uses-permission android:name="com.example.aihealthbutler.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" /> <!-- Don't require camera, as this requires a rear camera. This allows it to work on the Nexus 7 -->
    <uses-feature
        android:name="android.hardware.camera"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.camera.front"
        android:required="false" /> <!-- TODO replace above two with next line after Android 4.2 -->
    <!-- <uses-feature android:name="android.hardware.camera.any"/> -->
    <uses-feature
        android:name="android.hardware.camera.autofocus"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.camera.flash"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.screen.landscape"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.wifi"
        android:required="false" />

    <application
        android:allowBackup="true"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:debuggable="true"
        android:enableOnBackInvokedCallback="true"
        android:extractNativeLibs="false"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:networkSecurityConfig="@xml/network_security_config"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:testOnly="true"
        android:theme="@style/Theme.AIHealthButler"
        android:usesCleartextTraffic="true" >

        <!--
        allowBackup：允许应用程序参与数据备份和恢复；
        dataExtractionRules：XML文件，定义应用程序数据提取的规则；
        fullBackupContent：定义应用程序全量备份的内容规则；
        icon：设置应用程序的图标；
        label：应用程序的显示名称；
        roundIcon：设置应用程序的圆形图标；
        android:usesCleartextTraffic="true"：HTTP明文流量限制
        -->


        <!-- 相关服务 -->
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="com.example.aihealthbutler.provider"
            android:exported="false"
            android:grantUriPermissions="true" >
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

        <!-- 登录界面为起始页 -->
        <!-- 登录页面 -->
        <activity
            android:name="com.example.aihealthbutler.SignInActivity"
            android:exported="true"
            android:label="@string/app_name"
            android:theme="@style/Theme.AIHealthButler" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- 对话界面 -->
        <activity
            android:name="com.example.aihealthbutler.ConversationInterface"
            android:exported="true"
            android:label="@string/app_name"
            android:theme="@style/Theme.AIHealthButler" >
        </activity>

        <!-- &lt;!&ndash; 对话界面 &ndash;&gt; -->
        <!-- <activity -->
        <!-- android:name=".ConversationInterface" -->
        <!-- android:exported="true" -->
        <!-- android:theme="@style/Theme.AIHealthButler" /> -->


        <!-- 我的界面为起始页 -->


        <!-- 登录页面 -->
        <!-- <activity -->
        <!-- android:name=".SignInActivity" -->
        <!-- android:exported="true" -->
        <!-- android:theme="@style/Theme.AIHealthButler" /> -->


        <!-- &lt;!&ndash; 我的页面 &ndash;&gt; -->
        <!-- <activity -->
        <!-- android:name=".MainActivity" -->
        <!-- android:exported="true"> -->
        <!-- <intent-filter> -->
        <!-- <action android:name="android.intent.action.MAIN" /> -->
        <!-- <category android:name="android.intent.category.LAUNCHER" /> -->
        <!-- </intent-filter> -->
        <!-- </activity> -->


        <!-- 用户协议界面为首界面 -->
        <!-- 用户协议界面 -->
        <!-- <activity -->
        <!-- android:name=".FirstUserPopupActivity" -->
        <!-- android:exported="true"> -->
        <!-- <intent-filter> -->
        <!-- <action android:name="android.intent.action.MAIN" /> -->
        <!-- <category android:name="android.intent.category.LAUNCHER" /> -->
        <!-- </intent-filter> -->
        <!-- </activity> -->


        <!-- &lt;!&ndash;登录页面 &ndash;&gt; -->
        <!-- <activity -->
        <!-- android:name=".SignInActivity" -->
        <!-- android:exported="true" -->
        <!-- android:theme="@style/Theme.AIHealthButler" /> -->


        <!-- &lt;!&ndash; 我的页面 &ndash;&gt; -->
        <!-- <activity -->
        <!-- android:name=".MainActivity" -->
        <!-- android:exported="true" -->
        <!-- android:theme="@style/Theme.AIHealthButler" /> -->


        <!-- 我的页面 -->
        <activity
            android:name="com.example.aihealthbutler.MainActivity"
            android:exported="true"
            android:theme="@style/Theme.AIHealthButler" />

        <!-- 用药计划页面 -->
        <activity
            android:name="com.example.aihealthbutler.MedicationPlanActivity"
            android:exported="true"
            android:theme="@style/Theme.AIHealthButler" />

        <!-- 新增用药计划 -->
        <activity
            android:name="com.example.aihealthbutler.AddnewmedicationplanActivity"
            android:exported="true"
            android:theme="@style/Theme.AIHealthButler" />

        <!-- 运动建议 -->
        <activity
            android:name="com.example.aihealthbutler.ExerciseAdviceActivity"
            android:exported="true"
            android:theme="@style/Theme.AIHealthButler" />

        <!-- 饮食建议 -->
        <activity
            android:name="com.example.aihealthbutler.DietaryAdviceActivity"
            android:exported="true"
            android:theme="@style/Theme.AIHealthButler" />

        <!-- 个人信息 -->
        <activity
            android:name="com.example.aihealthbutler.PersonalInformationActivity"
            android:exported="true"
            android:theme="@style/Theme.AIHealthButler" />

        <!-- 新增家庭成员 -->
        <activity
            android:name="com.example.aihealthbutler.AddNewFamilyMember"
            android:exported="true"
            android:theme="@style/Theme.AIHealthButler" />

        <!-- 资料夹主界面 -->
        <activity
            android:name="com.example.aihealthbutler.DocumentFolderActivity"
            android:exported="true"
            android:theme="@style/Theme.AIHealthButler" />

        <!-- 上传资料 -->
        <activity
            android:name="com.example.aihealthbutler.UploadMaterialsActivity"
            android:exported="true"
            android:theme="@style/Theme.AIHealthButler" />

        <!-- 健康史 -->
        <activity
            android:name="com.example.aihealthbutler.HealthHistoryActivity"
            android:exported="false"
            android:screenOrientation="portrait" />

        <!-- 权限设置 -->
        <activity
            android:name="com.example.aihealthbutler.PermissionsManagerActivity"
            android:exported="true"
            android:theme="@style/Theme.AIHealthButler" />

        <!-- 家庭成员切换 -->
        <activity
            android:name="com.example.aihealthbutler.FamilyMemberSwitchActivity"
            android:exported="true"
            android:theme="@style/Theme.AIHealthButler" />

        <!-- 邀请二维码生成 -->
        <activity
            android:name="com.example.aihealthbutler.InviteQRCodeActivity"
            android:exported="true"
            android:theme="@style/Theme.AIHealthButler" />

        <!-- 二维码扫描 -->
        <activity
            android:name="com.example.aihealthbutler.QRScannerActivity"
            android:exported="true"
            android:theme="@style/Theme.AIHealthButler" />

        <!-- 添加成员确认 -->
        <activity
            android:name="com.example.aihealthbutler.AddMemberConfirmActivity"
            android:exported="true"
            android:theme="@style/Theme.AIHealthButler" />

        <!-- 关系设置 -->
        <activity
            android:name="com.example.aihealthbutler.RelationshipSettingActivity"
            android:exported="true"
            android:theme="@style/Theme.AIHealthButler" />

        <!-- 网络调试工具 -->
        <activity
            android:name="com.example.aihealthbutler.debug.NetworkDebugActivity"
            android:exported="false"
            android:theme="@style/Theme.AIHealthButler" />
        <activity
            android:name="androidx.compose.ui.tooling.PreviewActivity"
            android:exported="true" />

        <service
            android:name="androidx.camera.core.impl.MetadataHolderService"
            android:enabled="false"
            android:exported="false" >
            <meta-data
                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
        </service>
        <service
            android:name="com.google.mlkit.common.internal.MlKitComponentDiscoveryService"
            android:directBootAware="true"
            android:exported="false" >
            <meta-data
                android:name="com.google.firebase.components:com.google.mlkit.vision.barcode.internal.BarcodeRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
        </service>

        <provider
            android:name="com.google.mlkit.common.internal.MlKitInitProvider"
            android:authorities="com.example.aihealthbutler.mlkitinitprovider"
            android:exported="false"
            android:initOrder="99" />

        <activity
            android:name="com.google.android.gms.common.api.GoogleApiActivity"
            android:exported="false"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />

        <meta-data
            android:name="com.google.android.gms.version"
            android:value="@integer/google_play_services_version" />

        <activity
            android:name="androidx.activity.ComponentActivity"
            android:exported="true" />

        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="com.example.aihealthbutler.androidx-startup"
            android:exported="false" >
            <meta-data
                android:name="androidx.emoji2.text.EmojiCompatInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
                android:value="androidx.startup" />
        </provider>

        <uses-library
            android:name="androidx.window.extensions"
            android:required="false" />
        <uses-library
            android:name="androidx.window.sidecar"
            android:required="false" />

        <receiver
            android:name="androidx.profileinstaller.ProfileInstallReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.DUMP" >
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
            </intent-filter>
        </receiver>

        <service
            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
            android:exported="false" >
            <meta-data
                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
                android:value="cct" />
        </service>
        <service
            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
            android:exported="false"
            android:permission="android.permission.BIND_JOB_SERVICE" >
        </service>

        <receiver
            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
            android:exported="false" />

        <activity
            android:name="com.journeyapps.barcodescanner.CaptureActivity"
            android:clearTaskOnLaunch="true"
            android:screenOrientation="sensorLandscape"
            android:stateNotNeeded="true"
            android:theme="@style/zxing_CaptureTheme"
            android:windowSoftInputMode="stateAlwaysHidden" />
    </application>

</manifest>