package com.example.aihealthbutler

import android.content.ContentValues
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.net.Uri
import android.os.Bundle
import android.provider.MediaStore
import android.util.Log
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Share
import androidx.compose.ui.res.painterResource
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.lifecycle.viewmodel.compose.viewModel
import com.example.aihealthbutler.api.Repository
import com.example.aihealthbutler.api.SessionManager
import com.example.aihealthbutler.qr.QRCodeGenerator
import com.example.aihealthbutler.qr.QRCodeResult
import com.example.aihealthbutler.qr.QRInviteData
import com.example.aihealthbutler.ui.theme.AIHealthButlerTheme
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.OutputStream

/**
 * 邀请二维码生成页面
 */
class InviteQRCodeActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            AIHealthButlerTheme {
                val sessionManager = remember { SessionManager(applicationContext) }
                val repository = remember { Repository() }
                val viewModel = remember { InviteQRCodeViewModel(repository, sessionManager) }
                
                InviteQRCodeScreen(
                    viewModel = viewModel,
                    onBack = { finish() },
                    onSaveQRCode = { bitmap ->
                        saveQRCodeToGallery(bitmap)
                    },
                    onShareQRCode = { bitmap ->
                        shareQRCode(bitmap)
                    }
                )
            }
        }
    }
    
    /**
     * 保存二维码到相册
     */
    private fun saveQRCodeToGallery(bitmap: Bitmap) {
        try {
            val contentValues = ContentValues().apply {
                put(MediaStore.Images.Media.DISPLAY_NAME, "invite_qr_${System.currentTimeMillis()}.png")
                put(MediaStore.Images.Media.MIME_TYPE, "image/png")
                put(MediaStore.Images.Media.RELATIVE_PATH, "Pictures/AIHealthButler")
            }
            
            val uri = contentResolver.insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, contentValues)
            uri?.let { imageUri ->
                contentResolver.openOutputStream(imageUri)?.use { outputStream ->
                    bitmap.compress(Bitmap.CompressFormat.PNG, 100, outputStream)
                    Toast.makeText(this, "二维码已保存到相册", Toast.LENGTH_SHORT).show()
                }
            } ?: run {
                Toast.makeText(this, "保存失败", Toast.LENGTH_SHORT).show()
            }
        } catch (e: Exception) {
            Log.e("InviteQRCodeActivity", "保存二维码失败", e)
            Toast.makeText(this, "保存失败: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }
    
    /**
     * 分享二维码
     */
    private fun shareQRCode(bitmap: Bitmap) {
        try {
            // 创建临时文件
            val contentValues = ContentValues().apply {
                put(MediaStore.Images.Media.DISPLAY_NAME, "temp_qr_${System.currentTimeMillis()}.png")
                put(MediaStore.Images.Media.MIME_TYPE, "image/png")
            }
            
            val uri = contentResolver.insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, contentValues)
            uri?.let { imageUri ->
                contentResolver.openOutputStream(imageUri)?.use { outputStream ->
                    bitmap.compress(Bitmap.CompressFormat.PNG, 100, outputStream)
                }
                
                // 创建分享Intent
                val shareIntent = Intent().apply {
                    action = Intent.ACTION_SEND
                    type = "image/png"
                    putExtra(Intent.EXTRA_STREAM, imageUri)
                    putExtra(Intent.EXTRA_TEXT, "扫描二维码添加我为家庭成员")
                }
                
                startActivity(Intent.createChooser(shareIntent, "分享邀请二维码"))
            }
        } catch (e: Exception) {
            Log.e("InviteQRCodeActivity", "分享二维码失败", e)
            Toast.makeText(this, "分享失败: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }
}

/**
 * 邀请二维码ViewModel
 */
class InviteQRCodeViewModel(
    private val repository: Repository,
    private val sessionManager: SessionManager
) : ViewModel() {
    private val TAG = "InviteQRCodeViewModel"
    
    private val _uiState = MutableStateFlow(InviteQRCodeUiState())
    val uiState: StateFlow<InviteQRCodeUiState> = _uiState
    
    init {
        generateQRCode()
    }
    
    /**
     * 生成二维码
     */
    fun generateQRCode() {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true, error = null)
                
                // 获取当前用户信息
                val currentUser = getCurrentUserInfo()
                if (currentUser == null) {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = "获取用户信息失败，请重新登录"
                    )
                    return@launch
                }
                
                // 创建邀请数据
                val inviteData = QRInviteData(
                    userId = currentUser.defaultId,
                    account = currentUser.account ?: "",
                    name = currentUser.name ?: "未设置姓名"
                )
                
                // 生成二维码
                val result = withContext(Dispatchers.IO) {
                    QRCodeGenerator.generateInviteQRCode(inviteData)
                }
                
                when (result) {
                    is QRCodeResult.Success -> {
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            qrCodeBitmap = result.bitmap,
                            userInfo = currentUser,
                            error = null
                        )
                    }
                    is QRCodeResult.Error -> {
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            error = result.message
                        )
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "生成二维码失败", e)
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "生成二维码失败: ${e.message}"
                )
            }
        }
    }
    
    /**
     * 获取当前用户信息
     */
    private suspend fun getCurrentUserInfo(): UserInfo? {
        return try {
            val token = sessionManager.token.firstOrNull()
            val defaultId = sessionManager.defaultId.firstOrNull()
            val account = sessionManager.account.value
            
            if (token.isNullOrEmpty() || defaultId.isNullOrEmpty() || account.isNullOrEmpty()) {
                Log.w(TAG, "用户未登录或信息不完整")
                return null
            }
            
            // 尝试从缓存获取
            val cachedInfo = sessionManager.getCachedPersonInfo()
            if (cachedInfo != null) {
                return UserInfo(
                    defaultId = cachedInfo.defaultId,
                    account = cachedInfo.account,
                    name = cachedInfo.name,
                    sexText = cachedInfo.sexText,
                    birthday = cachedInfo.birthday
                )
            }
            
            // 从API获取
            val result = repository.getPersonInfo(token, defaultId)
            result.fold(
                onSuccess = { response ->
                    if (response.code == 200 && response.data != null) {
                        UserInfo(
                            defaultId = response.data.defaultId,
                            account = response.data.account ?: account,
                            name = response.data.name ?: "未设置姓名",
                            sexText = response.data.sexText ?: "未设置",
                            birthday = response.data.birthday ?: "未设置"
                        )
                    } else {
                        null
                    }
                },
                onFailure = { null }
            )
        } catch (e: Exception) {
            Log.e(TAG, "获取用户信息失败", e)
            null
        }
    }
}

/**
 * UI状态数据类
 */
data class InviteQRCodeUiState(
    val isLoading: Boolean = false,
    val qrCodeBitmap: Bitmap? = null,
    val userInfo: UserInfo? = null,
    val error: String? = null
)

/**
 * 用户信息数据类
 */
data class UserInfo(
    val defaultId: String,
    val account: String,
    val name: String,
    val sexText: String,
    val birthday: String
)

/**
 * 邀请二维码页面UI
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun InviteQRCodeScreen(
    viewModel: InviteQRCodeViewModel,
    onBack: () -> Unit,
    onSaveQRCode: (Bitmap) -> Unit,
    onShareQRCode: (Bitmap) -> Unit
) {
    val uiState by viewModel.uiState.collectAsState()
    var showDisclaimer by remember { mutableStateOf(false) }

    Scaffold(
        topBar = {
            CenterAlignedTopAppBar(
                title = {
                    Text(
                        "我的邀请码",
                        fontSize = 20.sp,
                        fontWeight = FontWeight.Bold
                    )
                },
                navigationIcon = {
                    IconButton(onClick = onBack) {
                        Icon(
                            painter = painterResource(id = R.drawable.left_arrow),
                            contentDescription = "返回",
                            modifier = Modifier.size(24.dp)
                        )
                    }
                },
                colors = TopAppBarDefaults.centerAlignedTopAppBarColors(
                    containerColor = Color.White,
                    titleContentColor = Color.Black,
                    navigationIconContentColor = Color.Black
                )
            )
        },
        containerColor = Color.White
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .background(Color.White)
                .padding(paddingValues)
                .verticalScroll(rememberScrollState())
                .padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // 用户信息卡片
            uiState.userInfo?.let { userInfo ->
                UserInfoCard(userInfo = userInfo)
                Spacer(modifier = Modifier.height(24.dp))
            }

            // 二维码区域
            QRCodeSection(
                uiState = uiState,
                onRetry = { viewModel.generateQRCode() }
            )

            Spacer(modifier = Modifier.height(24.dp))

            // 操作按钮
            uiState.qrCodeBitmap?.let { bitmap ->
                ActionButtons(
                    qrCodeBitmap = bitmap,
                    onSave = onSaveQRCode,
                    onShare = onShareQRCode
                )
            }

            Spacer(modifier = Modifier.height(16.dp))

            // 使用说明
            UsageInstructions(
                onShowDisclaimer = { showDisclaimer = true }
            )
        }
    }

    // 免责声明弹窗
    if (showDisclaimer) {
        DisclaimerDialog(
            onDismiss = { showDisclaimer = false }
        )
    }
}

/**
 * 用户信息卡片
 */
@Composable
private fun UserInfoCard(userInfo: UserInfo) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = Color(0xFFF8F9FA)),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(20.dp),
            horizontalAlignment = Alignment.Start
        ) {
            Text(
                text = "${userInfo.name}的账户",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                color = Color.Black
            )
            Spacer(modifier = Modifier.height(12.dp))

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = userInfo.sexText,
                    fontSize = 14.sp,
                    color = Color.Gray
                )
                Text(
                    text = userInfo.birthday,
                    fontSize = 14.sp,
                    color = Color.Gray
                )
            }

            Spacer(modifier = Modifier.height(8.dp))

            Text(
                text = "账号: ${userInfo.account}",
                fontSize = 14.sp,
                color = Color.Gray
            )
        }
    }
}

/**
 * 二维码区域
 */
@Composable
private fun QRCodeSection(
    uiState: InviteQRCodeUiState,
    onRetry: () -> Unit
) {
    Card(
        modifier = Modifier.size(300.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
        shape = RoundedCornerShape(16.dp)
    ) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            when {
                uiState.isLoading -> {
                    Column(horizontalAlignment = Alignment.CenterHorizontally) {
                        CircularProgressIndicator()
                        Spacer(modifier = Modifier.height(16.dp))
                        Text("正在生成二维码...", color = Color.Gray)
                    }
                }
                uiState.error != null -> {
                    Column(horizontalAlignment = Alignment.CenterHorizontally) {
                        Text(
                            text = uiState.error,
                            color = Color.Red,
                            textAlign = TextAlign.Center,
                            modifier = Modifier.padding(16.dp)
                        )
                        Button(onClick = onRetry) {
                            Text("重试")
                        }
                    }
                }
                uiState.qrCodeBitmap != null -> {
                    Image(
                        bitmap = uiState.qrCodeBitmap.asImageBitmap(),
                        contentDescription = "邀请二维码",
                        modifier = Modifier
                            .size(260.dp)
                            .padding(20.dp)
                            .clip(RoundedCornerShape(8.dp))
                    )
                }
            }
        }
    }
}

/**
 * 操作按钮组
 */
@Composable
private fun ActionButtons(
    qrCodeBitmap: Bitmap,
    onSave: (Bitmap) -> Unit,
    onShare: (Bitmap) -> Unit
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceEvenly
    ) {
        OutlinedButton(
            onClick = { onSave(qrCodeBitmap) },
            modifier = Modifier.weight(1f),
            colors = ButtonDefaults.outlinedButtonColors(
                contentColor = Color(0xFF22C1C3)
            ),
            border = BorderStroke(1.dp, Color(0xFF22C1C3))
        ) {
            Icon(
                painter = painterResource(id = R.drawable.ic_save),
                contentDescription = null,
                modifier = Modifier.size(18.dp)
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text("保存")
        }

        Spacer(modifier = Modifier.width(16.dp))

        Button(
            onClick = { onShare(qrCodeBitmap) },
            modifier = Modifier.weight(1f),
            colors = ButtonDefaults.buttonColors(
                containerColor = Color(0xFF22C1C3)
            )
        ) {
            Icon(Icons.Default.Share, contentDescription = null, modifier = Modifier.size(18.dp))
            Spacer(modifier = Modifier.width(8.dp))
            Text("分享")
        }
    }


}

/**
 * 使用说明
 */
@Composable
private fun UsageInstructions(onShowDisclaimer: () -> Unit) {
    Column {
        // 查看使用须知按钮
        TextButton(
            onClick = onShowDisclaimer,
            modifier = Modifier.align(Alignment.CenterHorizontally)
        ) {
            Text(
                text = "查看使用须知",
                color = Color.Gray,
                fontSize = 14.sp
            )
        }

        Spacer(modifier = Modifier.height(16.dp))

        // 使用说明卡片
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(containerColor = Color(0xFFF0F8FF)),
            elevation = CardDefaults.cardElevation(defaultElevation = 1.dp),
            shape = RoundedCornerShape(12.dp)
        ) {
            Column(modifier = Modifier.padding(16.dp)) {
                Text(
                    text = "使用说明",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF22C1C3)
                )
                Spacer(modifier = Modifier.height(8.dp))

                Text(
                    text = "1. 将此二维码分享给需要添加您为家庭成员的人",
                    fontSize = 14.sp,
                    color = Color(0xFF424242),
                    modifier = Modifier.padding(vertical = 2.dp)
                )
            }
        }
    }
}

/**
 * 免责声明弹窗
 */
@Composable
private fun DisclaimerDialog(onDismiss: () -> Unit) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = "⚠️ 重要提示",
                fontWeight = FontWeight.Bold,
                color = Color(0xFFE65100)
            )
        },
        text = {
            Column {
                Text(
                    text = "请注意保护您的个人隐私：",
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier.padding(bottom = 8.dp)
                )

                val warnings = listOf(
                    "• 请勿将此二维码分享给陌生人",
                    "• 此二维码包含您的基本信息",
                    "• 扫码者可将您添加为其家庭成员",
                    "• 二维码24小时后自动失效",
                    "• 如有疑虑，请重新生成新的二维码"
                )

                warnings.forEach { warning ->
                    Text(
                        text = warning,
                        fontSize = 14.sp,
                        color = Color(0xFF424242),
                        modifier = Modifier.padding(vertical = 1.dp)
                    )
                }

                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "继续使用即表示您已了解上述风险。",
                    fontSize = 12.sp,
                    color = Color.Gray,
                    fontStyle = androidx.compose.ui.text.font.FontStyle.Italic
                )
            }
        },
        confirmButton = {
            TextButton(onClick = onDismiss) {
                Text("我知道了")
            }
        }
    )
}
