int anim abc_fade_in 0x7f010000
int anim abc_fade_out 0x7f010001
int anim abc_grow_fade_in_from_bottom 0x7f010002
int anim abc_popup_enter 0x7f010003
int anim abc_popup_exit 0x7f010004
int anim abc_shrink_fade_out_from_bottom 0x7f010005
int anim abc_slide_in_bottom 0x7f010006
int anim abc_slide_in_top 0x7f010007
int anim abc_slide_out_bottom 0x7f010008
int anim abc_slide_out_top 0x7f010009
int anim abc_tooltip_enter 0x7f01000a
int anim abc_tooltip_exit 0x7f01000b
int anim btn_checkbox_to_checked_box_inner_merged_animation 0x7f01000c
int anim btn_checkbox_to_checked_box_outer_merged_animation 0x7f01000d
int anim btn_checkbox_to_checked_icon_null_animation 0x7f01000e
int anim btn_checkbox_to_unchecked_box_inner_merged_animation 0x7f01000f
int anim btn_checkbox_to_unchecked_check_path_merged_animation 0x7f010010
int anim btn_checkbox_to_unchecked_icon_null_animation 0x7f010011
int anim btn_radio_to_off_mtrl_dot_group_animation 0x7f010012
int anim btn_radio_to_off_mtrl_ring_outer_animation 0x7f010013
int anim btn_radio_to_off_mtrl_ring_outer_path_animation 0x7f010014
int anim btn_radio_to_on_mtrl_dot_group_animation 0x7f010015
int anim btn_radio_to_on_mtrl_ring_outer_animation 0x7f010016
int anim btn_radio_to_on_mtrl_ring_outer_path_animation 0x7f010017
int anim fragment_fast_out_extra_slow_in 0x7f010018
int animator fragment_close_enter 0x7f020000
int animator fragment_close_exit 0x7f020001
int animator fragment_fade_enter 0x7f020002
int animator fragment_fade_exit 0x7f020003
int animator fragment_open_enter 0x7f020004
int animator fragment_open_exit 0x7f020005
int array exo_controls_playback_speeds 0x7f030000
int attr actionBarDivider 0x7f040000
int attr actionBarItemBackground 0x7f040001
int attr actionBarPopupTheme 0x7f040002
int attr actionBarSize 0x7f040003
int attr actionBarSplitStyle 0x7f040004
int attr actionBarStyle 0x7f040005
int attr actionBarTabBarStyle 0x7f040006
int attr actionBarTabStyle 0x7f040007
int attr actionBarTabTextStyle 0x7f040008
int attr actionBarTheme 0x7f040009
int attr actionBarWidgetTheme 0x7f04000a
int attr actionButtonStyle 0x7f04000b
int attr actionDropDownStyle 0x7f04000c
int attr actionLayout 0x7f04000d
int attr actionMenuTextAppearance 0x7f04000e
int attr actionMenuTextColor 0x7f04000f
int attr actionModeBackground 0x7f040010
int attr actionModeCloseButtonStyle 0x7f040011
int attr actionModeCloseContentDescription 0x7f040012
int attr actionModeCloseDrawable 0x7f040013
int attr actionModeCopyDrawable 0x7f040014
int attr actionModeCutDrawable 0x7f040015
int attr actionModeFindDrawable 0x7f040016
int attr actionModePasteDrawable 0x7f040017
int attr actionModePopupWindowStyle 0x7f040018
int attr actionModeSelectAllDrawable 0x7f040019
int attr actionModeShareDrawable 0x7f04001a
int attr actionModeSplitBackground 0x7f04001b
int attr actionModeStyle 0x7f04001c
int attr actionModeTheme 0x7f04001d
int attr actionModeWebSearchDrawable 0x7f04001e
int attr actionOverflowButtonStyle 0x7f04001f
int attr actionOverflowMenuStyle 0x7f040020
int attr actionProviderClass 0x7f040021
int attr actionViewClass 0x7f040022
int attr activityAction 0x7f040023
int attr activityChooserViewStyle 0x7f040024
int attr activityName 0x7f040025
int attr ad_marker_color 0x7f040026
int attr ad_marker_width 0x7f040027
int attr alertDialogButtonGroupStyle 0x7f040028
int attr alertDialogCenterButtons 0x7f040029
int attr alertDialogStyle 0x7f04002a
int attr alertDialogTheme 0x7f04002b
int attr allowStacking 0x7f04002c
int attr alpha 0x7f04002d
int attr alphabeticModifiers 0x7f04002e
int attr alwaysExpand 0x7f04002f
int attr animation_enabled 0x7f040030
int attr arrowHeadLength 0x7f040031
int attr arrowShaftLength 0x7f040032
int attr artwork_display_mode 0x7f040033
int attr autoCompleteTextViewStyle 0x7f040034
int attr autoSizeMaxTextSize 0x7f040035
int attr autoSizeMinTextSize 0x7f040036
int attr autoSizePresetSizes 0x7f040037
int attr autoSizeStepGranularity 0x7f040038
int attr autoSizeTextType 0x7f040039
int attr auto_show 0x7f04003a
int attr background 0x7f04003b
int attr backgroundSplit 0x7f04003c
int attr backgroundStacked 0x7f04003d
int attr backgroundTint 0x7f04003e
int attr backgroundTintMode 0x7f04003f
int attr barLength 0x7f040040
int attr bar_gravity 0x7f040041
int attr bar_height 0x7f040042
int attr borderlessButtonStyle 0x7f040043
int attr buffered_color 0x7f040044
int attr buttonBarButtonStyle 0x7f040045
int attr buttonBarNegativeButtonStyle 0x7f040046
int attr buttonBarNeutralButtonStyle 0x7f040047
int attr buttonBarPositiveButtonStyle 0x7f040048
int attr buttonBarStyle 0x7f040049
int attr buttonCompat 0x7f04004a
int attr buttonGravity 0x7f04004b
int attr buttonIconDimen 0x7f04004c
int attr buttonPanelSideLayout 0x7f04004d
int attr buttonSize 0x7f04004e
int attr buttonStyle 0x7f04004f
int attr buttonStyleSmall 0x7f040050
int attr buttonTint 0x7f040051
int attr buttonTintMode 0x7f040052
int attr checkMarkCompat 0x7f040053
int attr checkMarkTint 0x7f040054
int attr checkMarkTintMode 0x7f040055
int attr checkboxStyle 0x7f040056
int attr checkedTextViewStyle 0x7f040057
int attr circleCrop 0x7f040058
int attr clearTop 0x7f040059
int attr closeIcon 0x7f04005a
int attr closeItemLayout 0x7f04005b
int attr collapseContentDescription 0x7f04005c
int attr collapseIcon 0x7f04005d
int attr color 0x7f04005e
int attr colorAccent 0x7f04005f
int attr colorBackgroundFloating 0x7f040060
int attr colorButtonNormal 0x7f040061
int attr colorControlActivated 0x7f040062
int attr colorControlHighlight 0x7f040063
int attr colorControlNormal 0x7f040064
int attr colorError 0x7f040065
int attr colorPrimary 0x7f040066
int attr colorPrimaryDark 0x7f040067
int attr colorScheme 0x7f040068
int attr colorSwitchThumbNormal 0x7f040069
int attr commitIcon 0x7f04006a
int attr contentDescription 0x7f04006b
int attr contentInsetEnd 0x7f04006c
int attr contentInsetEndWithActions 0x7f04006d
int attr contentInsetLeft 0x7f04006e
int attr contentInsetRight 0x7f04006f
int attr contentInsetStart 0x7f040070
int attr contentInsetStartWithNavigation 0x7f040071
int attr controlBackground 0x7f040072
int attr controller_layout_id 0x7f040073
int attr customNavigationLayout 0x7f040074
int attr defaultQueryHint 0x7f040075
int attr default_artwork 0x7f040076
int attr dialogCornerRadius 0x7f040077
int attr dialogPreferredPadding 0x7f040078
int attr dialogTheme 0x7f040079
int attr displayOptions 0x7f04007a
int attr divider 0x7f04007b
int attr dividerHorizontal 0x7f04007c
int attr dividerPadding 0x7f04007d
int attr dividerVertical 0x7f04007e
int attr drawableBottomCompat 0x7f04007f
int attr drawableEndCompat 0x7f040080
int attr drawableLeftCompat 0x7f040081
int attr drawableRightCompat 0x7f040082
int attr drawableSize 0x7f040083
int attr drawableStartCompat 0x7f040084
int attr drawableTint 0x7f040085
int attr drawableTintMode 0x7f040086
int attr drawableTopCompat 0x7f040087
int attr drawerArrowStyle 0x7f040088
int attr dropDownListViewStyle 0x7f040089
int attr dropdownListPreferredItemHeight 0x7f04008a
int attr editTextBackground 0x7f04008b
int attr editTextColor 0x7f04008c
int attr editTextStyle 0x7f04008d
int attr elevation 0x7f04008e
int attr emojiCompatEnabled 0x7f04008f
int attr expandActivityOverflowButtonDrawable 0x7f040090
int attr fastScrollEnabled 0x7f040091
int attr fastScrollHorizontalThumbDrawable 0x7f040092
int attr fastScrollHorizontalTrackDrawable 0x7f040093
int attr fastScrollVerticalThumbDrawable 0x7f040094
int attr fastScrollVerticalTrackDrawable 0x7f040095
int attr finishPrimaryWithSecondary 0x7f040096
int attr finishSecondaryWithPrimary 0x7f040097
int attr firstBaselineToTopHeight 0x7f040098
int attr font 0x7f040099
int attr fontFamily 0x7f04009a
int attr fontProviderAuthority 0x7f04009b
int attr fontProviderCerts 0x7f04009c
int attr fontProviderFetchStrategy 0x7f04009d
int attr fontProviderFetchTimeout 0x7f04009e
int attr fontProviderPackage 0x7f04009f
int attr fontProviderQuery 0x7f0400a0
int attr fontProviderSystemFontFamily 0x7f0400a1
int attr fontStyle 0x7f0400a2
int attr fontVariationSettings 0x7f0400a3
int attr fontWeight 0x7f0400a4
int attr gapBetweenBars 0x7f0400a5
int attr goIcon 0x7f0400a6
int attr height 0x7f0400a7
int attr hideOnContentScroll 0x7f0400a8
int attr hide_during_ads 0x7f0400a9
int attr hide_on_touch 0x7f0400aa
int attr homeAsUpIndicator 0x7f0400ab
int attr homeLayout 0x7f0400ac
int attr icon 0x7f0400ad
int attr iconTint 0x7f0400ae
int attr iconTintMode 0x7f0400af
int attr iconifiedByDefault 0x7f0400b0
int attr imageAspectRatio 0x7f0400b1
int attr imageAspectRatioAdjust 0x7f0400b2
int attr imageButtonStyle 0x7f0400b3
int attr implementationMode 0x7f0400b4
int attr indeterminateProgressStyle 0x7f0400b5
int attr initialActivityCount 0x7f0400b6
int attr isLightTheme 0x7f0400b7
int attr itemPadding 0x7f0400b8
int attr keep_content_on_player_reset 0x7f0400b9
int attr lStar 0x7f0400ba
int attr lastBaselineToBottomHeight 0x7f0400bb
int attr layout 0x7f0400bc
int attr layoutManager 0x7f0400bd
int attr lineHeight 0x7f0400be
int attr listChoiceBackgroundIndicator 0x7f0400bf
int attr listChoiceIndicatorMultipleAnimated 0x7f0400c0
int attr listChoiceIndicatorSingleAnimated 0x7f0400c1
int attr listDividerAlertDialog 0x7f0400c2
int attr listItemLayout 0x7f0400c3
int attr listLayout 0x7f0400c4
int attr listMenuViewStyle 0x7f0400c5
int attr listPopupWindowStyle 0x7f0400c6
int attr listPreferredItemHeight 0x7f0400c7
int attr listPreferredItemHeightLarge 0x7f0400c8
int attr listPreferredItemHeightSmall 0x7f0400c9
int attr listPreferredItemPaddingEnd 0x7f0400ca
int attr listPreferredItemPaddingLeft 0x7f0400cb
int attr listPreferredItemPaddingRight 0x7f0400cc
int attr listPreferredItemPaddingStart 0x7f0400cd
int attr logo 0x7f0400ce
int attr logoDescription 0x7f0400cf
int attr maxButtonHeight 0x7f0400d0
int attr measureWithLargestChild 0x7f0400d1
int attr menu 0x7f0400d2
int attr multiChoiceItemLayout 0x7f0400d3
int attr navigationContentDescription 0x7f0400d4
int attr navigationIcon 0x7f0400d5
int attr navigationMode 0x7f0400d6
int attr nestedScrollViewStyle 0x7f0400d7
int attr numericModifiers 0x7f0400d8
int attr overlapAnchor 0x7f0400d9
int attr paddingBottomNoButtons 0x7f0400da
int attr paddingEnd 0x7f0400db
int attr paddingStart 0x7f0400dc
int attr paddingTopNoTitle 0x7f0400dd
int attr panelBackground 0x7f0400de
int attr panelMenuListTheme 0x7f0400df
int attr panelMenuListWidth 0x7f0400e0
int attr placeholderActivityName 0x7f0400e1
int attr played_ad_marker_color 0x7f0400e2
int attr played_color 0x7f0400e3
int attr player_layout_id 0x7f0400e4
int attr popupMenuStyle 0x7f0400e5
int attr popupTheme 0x7f0400e6
int attr popupWindowStyle 0x7f0400e7
int attr preserveIconSpacing 0x7f0400e8
int attr primaryActivityName 0x7f0400e9
int attr progressBarPadding 0x7f0400ea
int attr progressBarStyle 0x7f0400eb
int attr queryBackground 0x7f0400ec
int attr queryHint 0x7f0400ed
int attr queryPatterns 0x7f0400ee
int attr radioButtonStyle 0x7f0400ef
int attr ratingBarStyle 0x7f0400f0
int attr ratingBarStyleIndicator 0x7f0400f1
int attr ratingBarStyleSmall 0x7f0400f2
int attr recyclerViewStyle 0x7f0400f3
int attr repeat_toggle_modes 0x7f0400f4
int attr resize_mode 0x7f0400f5
int attr reverseLayout 0x7f0400f6
int attr scaleType 0x7f0400f7
int attr scopeUris 0x7f0400f8
int attr scrubber_color 0x7f0400f9
int attr scrubber_disabled_size 0x7f0400fa
int attr scrubber_dragged_size 0x7f0400fb
int attr scrubber_drawable 0x7f0400fc
int attr scrubber_enabled_size 0x7f0400fd
int attr searchHintIcon 0x7f0400fe
int attr searchIcon 0x7f0400ff
int attr searchViewStyle 0x7f040100
int attr secondaryActivityAction 0x7f040101
int attr secondaryActivityName 0x7f040102
int attr seekBarStyle 0x7f040103
int attr selectableItemBackground 0x7f040104
int attr selectableItemBackgroundBorderless 0x7f040105
int attr shortcutMatchRequired 0x7f040106
int attr showAsAction 0x7f040107
int attr showDividers 0x7f040108
int attr showText 0x7f040109
int attr showTitle 0x7f04010a
int attr show_buffering 0x7f04010b
int attr show_fastforward_button 0x7f04010c
int attr show_next_button 0x7f04010d
int attr show_previous_button 0x7f04010e
int attr show_rewind_button 0x7f04010f
int attr show_shuffle_button 0x7f040110
int attr show_subtitle_button 0x7f040111
int attr show_timeout 0x7f040112
int attr show_vr_button 0x7f040113
int attr shutter_background_color 0x7f040114
int attr singleChoiceItemLayout 0x7f040115
int attr spanCount 0x7f040116
int attr spinBars 0x7f040117
int attr spinnerDropDownItemStyle 0x7f040118
int attr spinnerStyle 0x7f040119
int attr splitLayoutDirection 0x7f04011a
int attr splitMinSmallestWidth 0x7f04011b
int attr splitMinWidth 0x7f04011c
int attr splitRatio 0x7f04011d
int attr splitTrack 0x7f04011e
int attr srcCompat 0x7f04011f
int attr stackFromEnd 0x7f040120
int attr state_above_anchor 0x7f040121
int attr subMenuArrow 0x7f040122
int attr submitBackground 0x7f040123
int attr subtitle 0x7f040124
int attr subtitleTextAppearance 0x7f040125
int attr subtitleTextColor 0x7f040126
int attr subtitleTextStyle 0x7f040127
int attr suggestionRowLayout 0x7f040128
int attr surface_type 0x7f040129
int attr switchMinWidth 0x7f04012a
int attr switchPadding 0x7f04012b
int attr switchStyle 0x7f04012c
int attr switchTextAppearance 0x7f04012d
int attr textAllCaps 0x7f04012e
int attr textAppearanceLargePopupMenu 0x7f04012f
int attr textAppearanceListItem 0x7f040130
int attr textAppearanceListItemSecondary 0x7f040131
int attr textAppearanceListItemSmall 0x7f040132
int attr textAppearancePopupMenuHeader 0x7f040133
int attr textAppearanceSearchResultSubtitle 0x7f040134
int attr textAppearanceSearchResultTitle 0x7f040135
int attr textAppearanceSmallPopupMenu 0x7f040136
int attr textColorAlertDialogListItem 0x7f040137
int attr textColorSearchUrl 0x7f040138
int attr textLocale 0x7f040139
int attr theme 0x7f04013a
int attr thickness 0x7f04013b
int attr thumbTextPadding 0x7f04013c
int attr thumbTint 0x7f04013d
int attr thumbTintMode 0x7f04013e
int attr tickMark 0x7f04013f
int attr tickMarkTint 0x7f040140
int attr tickMarkTintMode 0x7f040141
int attr time_bar_min_update_interval 0x7f040142
int attr tint 0x7f040143
int attr tintMode 0x7f040144
int attr title 0x7f040145
int attr titleMargin 0x7f040146
int attr titleMarginBottom 0x7f040147
int attr titleMarginEnd 0x7f040148
int attr titleMarginStart 0x7f040149
int attr titleMarginTop 0x7f04014a
int attr titleMargins 0x7f04014b
int attr titleTextAppearance 0x7f04014c
int attr titleTextColor 0x7f04014d
int attr titleTextStyle 0x7f04014e
int attr toolbarNavigationButtonStyle 0x7f04014f
int attr toolbarStyle 0x7f040150
int attr tooltipForegroundColor 0x7f040151
int attr tooltipFrameBackground 0x7f040152
int attr tooltipText 0x7f040153
int attr touch_target_height 0x7f040154
int attr track 0x7f040155
int attr trackTint 0x7f040156
int attr trackTintMode 0x7f040157
int attr ttcIndex 0x7f040158
int attr unplayed_color 0x7f040159
int attr use_artwork 0x7f04015a
int attr use_controller 0x7f04015b
int attr viewInflaterClass 0x7f04015c
int attr voiceIcon 0x7f04015d
int attr windowActionBar 0x7f04015e
int attr windowActionBarOverlay 0x7f04015f
int attr windowActionModeOverlay 0x7f040160
int attr windowFixedHeightMajor 0x7f040161
int attr windowFixedHeightMinor 0x7f040162
int attr windowFixedWidthMajor 0x7f040163
int attr windowFixedWidthMinor 0x7f040164
int attr windowMinWidthMajor 0x7f040165
int attr windowMinWidthMinor 0x7f040166
int attr windowNoTitle 0x7f040167
int attr zxing_framing_rect_height 0x7f040168
int attr zxing_framing_rect_width 0x7f040169
int attr zxing_possible_result_points 0x7f04016a
int attr zxing_preview_scaling_strategy 0x7f04016b
int attr zxing_result_view 0x7f04016c
int attr zxing_scanner_layout 0x7f04016d
int attr zxing_use_texture_view 0x7f04016e
int attr zxing_viewfinder_laser 0x7f04016f
int attr zxing_viewfinder_laser_visibility 0x7f040170
int attr zxing_viewfinder_mask 0x7f040171
int bool abc_action_bar_embed_tabs 0x7f050000
int bool abc_config_actionMenuItemAllCaps 0x7f050001
int color abc_background_cache_hint_selector_material_dark 0x7f060000
int color abc_background_cache_hint_selector_material_light 0x7f060001
int color abc_btn_colored_borderless_text_material 0x7f060002
int color abc_btn_colored_text_material 0x7f060003
int color abc_color_highlight_material 0x7f060004
int color abc_decor_view_status_guard 0x7f060005
int color abc_decor_view_status_guard_light 0x7f060006
int color abc_hint_foreground_material_dark 0x7f060007
int color abc_hint_foreground_material_light 0x7f060008
int color abc_primary_text_disable_only_material_dark 0x7f060009
int color abc_primary_text_disable_only_material_light 0x7f06000a
int color abc_primary_text_material_dark 0x7f06000b
int color abc_primary_text_material_light 0x7f06000c
int color abc_search_url_text 0x7f06000d
int color abc_search_url_text_normal 0x7f06000e
int color abc_search_url_text_pressed 0x7f06000f
int color abc_search_url_text_selected 0x7f060010
int color abc_secondary_text_material_dark 0x7f060011
int color abc_secondary_text_material_light 0x7f060012
int color abc_tint_btn_checkable 0x7f060013
int color abc_tint_default 0x7f060014
int color abc_tint_edittext 0x7f060015
int color abc_tint_seek_thumb 0x7f060016
int color abc_tint_spinner 0x7f060017
int color abc_tint_switch_track 0x7f060018
int color accent_material_dark 0x7f060019
int color accent_material_light 0x7f06001a
int color androidx_core_ripple_material_light 0x7f06001b
int color androidx_core_secondary_text_default_material_light 0x7f06001c
int color background_floating_material_dark 0x7f06001d
int color background_floating_material_light 0x7f06001e
int color background_material_dark 0x7f06001f
int color background_material_light 0x7f060020
int color black 0x7f060021
int color bright_foreground_disabled_material_dark 0x7f060022
int color bright_foreground_disabled_material_light 0x7f060023
int color bright_foreground_inverse_material_dark 0x7f060024
int color bright_foreground_inverse_material_light 0x7f060025
int color bright_foreground_material_dark 0x7f060026
int color bright_foreground_material_light 0x7f060027
int color button_material_dark 0x7f060028
int color button_material_light 0x7f060029
int color call_notification_answer_color 0x7f06002a
int color call_notification_decline_color 0x7f06002b
int color common_google_signin_btn_text_dark 0x7f06002c
int color common_google_signin_btn_text_dark_default 0x7f06002d
int color common_google_signin_btn_text_dark_disabled 0x7f06002e
int color common_google_signin_btn_text_dark_focused 0x7f06002f
int color common_google_signin_btn_text_dark_pressed 0x7f060030
int color common_google_signin_btn_text_light 0x7f060031
int color common_google_signin_btn_text_light_default 0x7f060032
int color common_google_signin_btn_text_light_disabled 0x7f060033
int color common_google_signin_btn_text_light_focused 0x7f060034
int color common_google_signin_btn_text_light_pressed 0x7f060035
int color common_google_signin_btn_tint 0x7f060036
int color dim_foreground_disabled_material_dark 0x7f060037
int color dim_foreground_disabled_material_light 0x7f060038
int color dim_foreground_material_dark 0x7f060039
int color dim_foreground_material_light 0x7f06003a
int color error_color_material_dark 0x7f06003b
int color error_color_material_light 0x7f06003c
int color exo_black_opacity_60 0x7f06003d
int color exo_black_opacity_70 0x7f06003e
int color exo_bottom_bar_background 0x7f06003f
int color exo_edit_mode_background_color 0x7f060040
int color exo_error_message_background_color 0x7f060041
int color exo_styled_error_message_background 0x7f060042
int color exo_white 0x7f060043
int color exo_white_opacity_70 0x7f060044
int color foreground_material_dark 0x7f060045
int color foreground_material_light 0x7f060046
int color highlighted_text_material_dark 0x7f060047
int color highlighted_text_material_light 0x7f060048
int color material_blue_grey_800 0x7f060049
int color material_blue_grey_900 0x7f06004a
int color material_blue_grey_950 0x7f06004b
int color material_deep_teal_200 0x7f06004c
int color material_deep_teal_500 0x7f06004d
int color material_grey_100 0x7f06004e
int color material_grey_300 0x7f06004f
int color material_grey_50 0x7f060050
int color material_grey_600 0x7f060051
int color material_grey_800 0x7f060052
int color material_grey_850 0x7f060053
int color material_grey_900 0x7f060054
int color notification_action_color_filter 0x7f060055
int color notification_icon_bg_color 0x7f060056
int color notification_material_background_media_default_color 0x7f060057
int color primary_dark_material_dark 0x7f060058
int color primary_dark_material_light 0x7f060059
int color primary_material_dark 0x7f06005a
int color primary_material_light 0x7f06005b
int color primary_text_default_material_dark 0x7f06005c
int color primary_text_default_material_light 0x7f06005d
int color primary_text_disabled_material_dark 0x7f06005e
int color primary_text_disabled_material_light 0x7f06005f
int color purple_200 0x7f060060
int color purple_500 0x7f060061
int color purple_700 0x7f060062
int color ripple_material_dark 0x7f060063
int color ripple_material_light 0x7f060064
int color secondary_text_default_material_dark 0x7f060065
int color secondary_text_default_material_light 0x7f060066
int color secondary_text_disabled_material_dark 0x7f060067
int color secondary_text_disabled_material_light 0x7f060068
int color switch_thumb_disabled_material_dark 0x7f060069
int color switch_thumb_disabled_material_light 0x7f06006a
int color switch_thumb_material_dark 0x7f06006b
int color switch_thumb_material_light 0x7f06006c
int color switch_thumb_normal_material_dark 0x7f06006d
int color switch_thumb_normal_material_light 0x7f06006e
int color teal_200 0x7f06006f
int color teal_700 0x7f060070
int color tooltip_background_dark 0x7f060071
int color tooltip_background_light 0x7f060072
int color vector_tint_color 0x7f060073
int color vector_tint_theme_color 0x7f060074
int color white 0x7f060075
int color zxing_custom_possible_result_points 0x7f060076
int color zxing_custom_result_view 0x7f060077
int color zxing_custom_viewfinder_laser 0x7f060078
int color zxing_custom_viewfinder_mask 0x7f060079
int color zxing_possible_result_points 0x7f06007a
int color zxing_result_view 0x7f06007b
int color zxing_status_text 0x7f06007c
int color zxing_transparent 0x7f06007d
int color zxing_viewfinder_laser 0x7f06007e
int color zxing_viewfinder_mask 0x7f06007f
int dimen abc_action_bar_content_inset_material 0x7f070000
int dimen abc_action_bar_content_inset_with_nav 0x7f070001
int dimen abc_action_bar_default_height_material 0x7f070002
int dimen abc_action_bar_default_padding_end_material 0x7f070003
int dimen abc_action_bar_default_padding_start_material 0x7f070004
int dimen abc_action_bar_elevation_material 0x7f070005
int dimen abc_action_bar_icon_vertical_padding_material 0x7f070006
int dimen abc_action_bar_overflow_padding_end_material 0x7f070007
int dimen abc_action_bar_overflow_padding_start_material 0x7f070008
int dimen abc_action_bar_stacked_max_height 0x7f070009
int dimen abc_action_bar_stacked_tab_max_width 0x7f07000a
int dimen abc_action_bar_subtitle_bottom_margin_material 0x7f07000b
int dimen abc_action_bar_subtitle_top_margin_material 0x7f07000c
int dimen abc_action_button_min_height_material 0x7f07000d
int dimen abc_action_button_min_width_material 0x7f07000e
int dimen abc_action_button_min_width_overflow_material 0x7f07000f
int dimen abc_alert_dialog_button_bar_height 0x7f070010
int dimen abc_alert_dialog_button_dimen 0x7f070011
int dimen abc_button_inset_horizontal_material 0x7f070012
int dimen abc_button_inset_vertical_material 0x7f070013
int dimen abc_button_padding_horizontal_material 0x7f070014
int dimen abc_button_padding_vertical_material 0x7f070015
int dimen abc_cascading_menus_min_smallest_width 0x7f070016
int dimen abc_config_prefDialogWidth 0x7f070017
int dimen abc_control_corner_material 0x7f070018
int dimen abc_control_inset_material 0x7f070019
int dimen abc_control_padding_material 0x7f07001a
int dimen abc_dialog_corner_radius_material 0x7f07001b
int dimen abc_dialog_fixed_height_major 0x7f07001c
int dimen abc_dialog_fixed_height_minor 0x7f07001d
int dimen abc_dialog_fixed_width_major 0x7f07001e
int dimen abc_dialog_fixed_width_minor 0x7f07001f
int dimen abc_dialog_list_padding_bottom_no_buttons 0x7f070020
int dimen abc_dialog_list_padding_top_no_title 0x7f070021
int dimen abc_dialog_min_width_major 0x7f070022
int dimen abc_dialog_min_width_minor 0x7f070023
int dimen abc_dialog_padding_material 0x7f070024
int dimen abc_dialog_padding_top_material 0x7f070025
int dimen abc_dialog_title_divider_material 0x7f070026
int dimen abc_disabled_alpha_material_dark 0x7f070027
int dimen abc_disabled_alpha_material_light 0x7f070028
int dimen abc_dropdownitem_icon_width 0x7f070029
int dimen abc_dropdownitem_text_padding_left 0x7f07002a
int dimen abc_dropdownitem_text_padding_right 0x7f07002b
int dimen abc_edit_text_inset_bottom_material 0x7f07002c
int dimen abc_edit_text_inset_horizontal_material 0x7f07002d
int dimen abc_edit_text_inset_top_material 0x7f07002e
int dimen abc_floating_window_z 0x7f07002f
int dimen abc_list_item_height_large_material 0x7f070030
int dimen abc_list_item_height_material 0x7f070031
int dimen abc_list_item_height_small_material 0x7f070032
int dimen abc_list_item_padding_horizontal_material 0x7f070033
int dimen abc_panel_menu_list_width 0x7f070034
int dimen abc_progress_bar_height_material 0x7f070035
int dimen abc_search_view_preferred_height 0x7f070036
int dimen abc_search_view_preferred_width 0x7f070037
int dimen abc_seekbar_track_background_height_material 0x7f070038
int dimen abc_seekbar_track_progress_height_material 0x7f070039
int dimen abc_select_dialog_padding_start_material 0x7f07003a
int dimen abc_star_big 0x7f07003b
int dimen abc_star_medium 0x7f07003c
int dimen abc_star_small 0x7f07003d
int dimen abc_switch_padding 0x7f07003e
int dimen abc_text_size_body_1_material 0x7f07003f
int dimen abc_text_size_body_2_material 0x7f070040
int dimen abc_text_size_button_material 0x7f070041
int dimen abc_text_size_caption_material 0x7f070042
int dimen abc_text_size_display_1_material 0x7f070043
int dimen abc_text_size_display_2_material 0x7f070044
int dimen abc_text_size_display_3_material 0x7f070045
int dimen abc_text_size_display_4_material 0x7f070046
int dimen abc_text_size_headline_material 0x7f070047
int dimen abc_text_size_large_material 0x7f070048
int dimen abc_text_size_medium_material 0x7f070049
int dimen abc_text_size_menu_header_material 0x7f07004a
int dimen abc_text_size_menu_material 0x7f07004b
int dimen abc_text_size_small_material 0x7f07004c
int dimen abc_text_size_subhead_material 0x7f07004d
int dimen abc_text_size_subtitle_material_toolbar 0x7f07004e
int dimen abc_text_size_title_material 0x7f07004f
int dimen abc_text_size_title_material_toolbar 0x7f070050
int dimen compat_button_inset_horizontal_material 0x7f070051
int dimen compat_button_inset_vertical_material 0x7f070052
int dimen compat_button_padding_horizontal_material 0x7f070053
int dimen compat_button_padding_vertical_material 0x7f070054
int dimen compat_control_corner_material 0x7f070055
int dimen compat_notification_large_icon_max_height 0x7f070056
int dimen compat_notification_large_icon_max_width 0x7f070057
int dimen disabled_alpha_material_dark 0x7f070058
int dimen disabled_alpha_material_light 0x7f070059
int dimen exo_error_message_height 0x7f07005a
int dimen exo_error_message_margin_bottom 0x7f07005b
int dimen exo_error_message_text_padding_horizontal 0x7f07005c
int dimen exo_error_message_text_padding_vertical 0x7f07005d
int dimen exo_error_message_text_size 0x7f07005e
int dimen exo_icon_horizontal_margin 0x7f07005f
int dimen exo_icon_padding 0x7f070060
int dimen exo_icon_padding_bottom 0x7f070061
int dimen exo_icon_size 0x7f070062
int dimen exo_icon_text_size 0x7f070063
int dimen exo_media_button_height 0x7f070064
int dimen exo_media_button_width 0x7f070065
int dimen exo_setting_width 0x7f070066
int dimen exo_settings_height 0x7f070067
int dimen exo_settings_icon_size 0x7f070068
int dimen exo_settings_main_text_size 0x7f070069
int dimen exo_settings_offset 0x7f07006a
int dimen exo_settings_sub_text_size 0x7f07006b
int dimen exo_settings_text_height 0x7f07006c
int dimen exo_small_icon_height 0x7f07006d
int dimen exo_small_icon_horizontal_margin 0x7f07006e
int dimen exo_small_icon_padding_horizontal 0x7f07006f
int dimen exo_small_icon_padding_vertical 0x7f070070
int dimen exo_small_icon_width 0x7f070071
int dimen exo_styled_bottom_bar_height 0x7f070072
int dimen exo_styled_bottom_bar_margin_top 0x7f070073
int dimen exo_styled_bottom_bar_time_padding 0x7f070074
int dimen exo_styled_controls_padding 0x7f070075
int dimen exo_styled_minimal_controls_margin_bottom 0x7f070076
int dimen exo_styled_progress_bar_height 0x7f070077
int dimen exo_styled_progress_dragged_thumb_size 0x7f070078
int dimen exo_styled_progress_enabled_thumb_size 0x7f070079
int dimen exo_styled_progress_layout_height 0x7f07007a
int dimen exo_styled_progress_margin_bottom 0x7f07007b
int dimen exo_styled_progress_touch_target_height 0x7f07007c
int dimen fastscroll_default_thickness 0x7f07007d
int dimen fastscroll_margin 0x7f07007e
int dimen fastscroll_minimum_range 0x7f07007f
int dimen highlight_alpha_material_colored 0x7f070080
int dimen highlight_alpha_material_dark 0x7f070081
int dimen highlight_alpha_material_light 0x7f070082
int dimen hint_alpha_material_dark 0x7f070083
int dimen hint_alpha_material_light 0x7f070084
int dimen hint_pressed_alpha_material_dark 0x7f070085
int dimen hint_pressed_alpha_material_light 0x7f070086
int dimen item_touch_helper_max_drag_scroll_per_frame 0x7f070087
int dimen item_touch_helper_swipe_escape_max_velocity 0x7f070088
int dimen item_touch_helper_swipe_escape_velocity 0x7f070089
int dimen notification_action_icon_size 0x7f07008a
int dimen notification_action_text_size 0x7f07008b
int dimen notification_big_circle_margin 0x7f07008c
int dimen notification_content_margin_start 0x7f07008d
int dimen notification_large_icon_height 0x7f07008e
int dimen notification_large_icon_width 0x7f07008f
int dimen notification_main_column_padding_top 0x7f070090
int dimen notification_media_narrow_margin 0x7f070091
int dimen notification_right_icon_size 0x7f070092
int dimen notification_right_side_padding_top 0x7f070093
int dimen notification_small_icon_background_padding 0x7f070094
int dimen notification_small_icon_size_as_large 0x7f070095
int dimen notification_subtext_size 0x7f070096
int dimen notification_top_pad 0x7f070097
int dimen notification_top_pad_large_text 0x7f070098
int dimen tooltip_corner_radius 0x7f070099
int dimen tooltip_horizontal_padding 0x7f07009a
int dimen tooltip_margin 0x7f07009b
int dimen tooltip_precise_anchor_extra_offset 0x7f07009c
int dimen tooltip_precise_anchor_threshold 0x7f07009d
int dimen tooltip_vertical_padding 0x7f07009e
int dimen tooltip_y_offset_non_touch 0x7f07009f
int dimen tooltip_y_offset_touch 0x7f0700a0
int drawable abc_ab_share_pack_mtrl_alpha 0x7f080001
int drawable abc_action_bar_item_background_material 0x7f080002
int drawable abc_btn_borderless_material 0x7f080003
int drawable abc_btn_check_material 0x7f080004
int drawable abc_btn_check_material_anim 0x7f080005
int drawable abc_btn_check_to_on_mtrl_000 0x7f080006
int drawable abc_btn_check_to_on_mtrl_015 0x7f080007
int drawable abc_btn_colored_material 0x7f080008
int drawable abc_btn_default_mtrl_shape 0x7f080009
int drawable abc_btn_radio_material 0x7f08000a
int drawable abc_btn_radio_material_anim 0x7f08000b
int drawable abc_btn_radio_to_on_mtrl_000 0x7f08000c
int drawable abc_btn_radio_to_on_mtrl_015 0x7f08000d
int drawable abc_btn_switch_to_on_mtrl_00001 0x7f08000e
int drawable abc_btn_switch_to_on_mtrl_00012 0x7f08000f
int drawable abc_cab_background_internal_bg 0x7f080010
int drawable abc_cab_background_top_material 0x7f080011
int drawable abc_cab_background_top_mtrl_alpha 0x7f080012
int drawable abc_control_background_material 0x7f080013
int drawable abc_dialog_material_background 0x7f080014
int drawable abc_edit_text_material 0x7f080015
int drawable abc_ic_ab_back_material 0x7f080016
int drawable abc_ic_arrow_drop_right_black_24dp 0x7f080017
int drawable abc_ic_clear_material 0x7f080018
int drawable abc_ic_commit_search_api_mtrl_alpha 0x7f080019
int drawable abc_ic_go_search_api_material 0x7f08001a
int drawable abc_ic_menu_copy_mtrl_am_alpha 0x7f08001b
int drawable abc_ic_menu_cut_mtrl_alpha 0x7f08001c
int drawable abc_ic_menu_overflow_material 0x7f08001d
int drawable abc_ic_menu_paste_mtrl_am_alpha 0x7f08001e
int drawable abc_ic_menu_selectall_mtrl_alpha 0x7f08001f
int drawable abc_ic_menu_share_mtrl_alpha 0x7f080020
int drawable abc_ic_search_api_material 0x7f080021
int drawable abc_ic_voice_search_api_material 0x7f080022
int drawable abc_item_background_holo_dark 0x7f080023
int drawable abc_item_background_holo_light 0x7f080024
int drawable abc_list_divider_material 0x7f080025
int drawable abc_list_divider_mtrl_alpha 0x7f080026
int drawable abc_list_focused_holo 0x7f080027
int drawable abc_list_longpressed_holo 0x7f080028
int drawable abc_list_pressed_holo_dark 0x7f080029
int drawable abc_list_pressed_holo_light 0x7f08002a
int drawable abc_list_selector_background_transition_holo_dark 0x7f08002b
int drawable abc_list_selector_background_transition_holo_light 0x7f08002c
int drawable abc_list_selector_disabled_holo_dark 0x7f08002d
int drawable abc_list_selector_disabled_holo_light 0x7f08002e
int drawable abc_list_selector_holo_dark 0x7f08002f
int drawable abc_list_selector_holo_light 0x7f080030
int drawable abc_menu_hardkey_panel_mtrl_mult 0x7f080031
int drawable abc_popup_background_mtrl_mult 0x7f080032
int drawable abc_ratingbar_indicator_material 0x7f080033
int drawable abc_ratingbar_material 0x7f080034
int drawable abc_ratingbar_small_material 0x7f080035
int drawable abc_scrubber_control_off_mtrl_alpha 0x7f080036
int drawable abc_scrubber_control_to_pressed_mtrl_000 0x7f080037
int drawable abc_scrubber_control_to_pressed_mtrl_005 0x7f080038
int drawable abc_scrubber_primary_mtrl_alpha 0x7f080039
int drawable abc_scrubber_track_mtrl_alpha 0x7f08003a
int drawable abc_seekbar_thumb_material 0x7f08003b
int drawable abc_seekbar_tick_mark_material 0x7f08003c
int drawable abc_seekbar_track_material 0x7f08003d
int drawable abc_spinner_mtrl_am_alpha 0x7f08003e
int drawable abc_spinner_textfield_background_material 0x7f08003f
int drawable abc_star_black_48dp 0x7f080040
int drawable abc_star_half_black_48dp 0x7f080041
int drawable abc_switch_thumb_material 0x7f080042
int drawable abc_switch_track_mtrl_alpha 0x7f080043
int drawable abc_tab_indicator_material 0x7f080044
int drawable abc_tab_indicator_mtrl_alpha 0x7f080045
int drawable abc_text_cursor_material 0x7f080046
int drawable abc_text_select_handle_left_mtrl 0x7f080047
int drawable abc_text_select_handle_middle_mtrl 0x7f080048
int drawable abc_text_select_handle_right_mtrl 0x7f080049
int drawable abc_textfield_activated_mtrl_alpha 0x7f08004a
int drawable abc_textfield_default_mtrl_alpha 0x7f08004b
int drawable abc_textfield_search_activated_mtrl_alpha 0x7f08004c
int drawable abc_textfield_search_default_mtrl_alpha 0x7f08004d
int drawable abc_textfield_search_material 0x7f08004e
int drawable abc_vector_test 0x7f08004f
int drawable breakfast_porridge 0x7f080050
int drawable btn_checkbox_checked_mtrl 0x7f080051
int drawable btn_checkbox_checked_to_unchecked_mtrl_animation 0x7f080052
int drawable btn_checkbox_unchecked_mtrl 0x7f080053
int drawable btn_checkbox_unchecked_to_checked_mtrl_animation 0x7f080054
int drawable btn_radio_off_mtrl 0x7f080055
int drawable btn_radio_off_to_on_mtrl_animation 0x7f080056
int drawable btn_radio_on_mtrl 0x7f080057
int drawable btn_radio_on_to_off_mtrl_animation 0x7f080058
int drawable common_full_open_on_phone 0x7f080059
int drawable common_google_signin_btn_icon_dark 0x7f08005a
int drawable common_google_signin_btn_icon_dark_focused 0x7f08005b
int drawable common_google_signin_btn_icon_dark_normal 0x7f08005c
int drawable common_google_signin_btn_icon_dark_normal_background 0x7f08005d
int drawable common_google_signin_btn_icon_disabled 0x7f08005e
int drawable common_google_signin_btn_icon_light 0x7f08005f
int drawable common_google_signin_btn_icon_light_focused 0x7f080060
int drawable common_google_signin_btn_icon_light_normal 0x7f080061
int drawable common_google_signin_btn_icon_light_normal_background 0x7f080062
int drawable common_google_signin_btn_text_dark 0x7f080063
int drawable common_google_signin_btn_text_dark_focused 0x7f080064
int drawable common_google_signin_btn_text_dark_normal 0x7f080065
int drawable common_google_signin_btn_text_dark_normal_background 0x7f080066
int drawable common_google_signin_btn_text_disabled 0x7f080067
int drawable common_google_signin_btn_text_light 0x7f080068
int drawable common_google_signin_btn_text_light_focused 0x7f080069
int drawable common_google_signin_btn_text_light_normal 0x7f08006a
int drawable common_google_signin_btn_text_light_normal_background 0x7f08006b
int drawable conversation_back 0x7f08006c
int drawable dinner_spinach_omelette 0x7f08006d
int drawable exo_edit_mode_logo 0x7f08006e
int drawable exo_ic_audiotrack 0x7f08006f
int drawable exo_ic_check 0x7f080070
int drawable exo_ic_chevron_left 0x7f080071
int drawable exo_ic_chevron_right 0x7f080072
int drawable exo_ic_default_album_image 0x7f080073
int drawable exo_ic_forward 0x7f080074
int drawable exo_ic_fullscreen_enter 0x7f080075
int drawable exo_ic_fullscreen_exit 0x7f080076
int drawable exo_ic_pause_circle_filled 0x7f080077
int drawable exo_ic_play_circle_filled 0x7f080078
int drawable exo_ic_rewind 0x7f080079
int drawable exo_ic_settings 0x7f08007a
int drawable exo_ic_skip_next 0x7f08007b
int drawable exo_ic_skip_previous 0x7f08007c
int drawable exo_ic_speed 0x7f08007d
int drawable exo_ic_subtitle_off 0x7f08007e
int drawable exo_ic_subtitle_on 0x7f08007f
int drawable exo_icon_circular_play 0x7f080080
int drawable exo_icon_fastforward 0x7f080081
int drawable exo_icon_fullscreen_enter 0x7f080082
int drawable exo_icon_fullscreen_exit 0x7f080083
int drawable exo_icon_next 0x7f080084
int drawable exo_icon_pause 0x7f080085
int drawable exo_icon_play 0x7f080086
int drawable exo_icon_previous 0x7f080087
int drawable exo_icon_repeat_all 0x7f080088
int drawable exo_icon_repeat_off 0x7f080089
int drawable exo_icon_repeat_one 0x7f08008a
int drawable exo_icon_rewind 0x7f08008b
int drawable exo_icon_shuffle_off 0x7f08008c
int drawable exo_icon_shuffle_on 0x7f08008d
int drawable exo_icon_stop 0x7f08008e
int drawable exo_icon_vr 0x7f08008f
int drawable exo_legacy_controls_fastforward 0x7f080090
int drawable exo_legacy_controls_fullscreen_enter 0x7f080091
int drawable exo_legacy_controls_fullscreen_exit 0x7f080092
int drawable exo_legacy_controls_next 0x7f080093
int drawable exo_legacy_controls_pause 0x7f080094
int drawable exo_legacy_controls_play 0x7f080095
int drawable exo_legacy_controls_previous 0x7f080096
int drawable exo_legacy_controls_repeat_all 0x7f080097
int drawable exo_legacy_controls_repeat_off 0x7f080098
int drawable exo_legacy_controls_repeat_one 0x7f080099
int drawable exo_legacy_controls_rewind 0x7f08009a
int drawable exo_legacy_controls_shuffle_off 0x7f08009b
int drawable exo_legacy_controls_shuffle_on 0x7f08009c
int drawable exo_legacy_controls_vr 0x7f08009d
int drawable exo_notification_fastforward 0x7f08009e
int drawable exo_notification_next 0x7f08009f
int drawable exo_notification_pause 0x7f0800a0
int drawable exo_notification_play 0x7f0800a1
int drawable exo_notification_previous 0x7f0800a2
int drawable exo_notification_rewind 0x7f0800a3
int drawable exo_notification_small_icon 0x7f0800a4
int drawable exo_notification_stop 0x7f0800a5
int drawable exo_rounded_rectangle 0x7f0800a6
int drawable exo_styled_controls_audiotrack 0x7f0800a7
int drawable exo_styled_controls_check 0x7f0800a8
int drawable exo_styled_controls_fastforward 0x7f0800a9
int drawable exo_styled_controls_fullscreen_enter 0x7f0800aa
int drawable exo_styled_controls_fullscreen_exit 0x7f0800ab
int drawable exo_styled_controls_next 0x7f0800ac
int drawable exo_styled_controls_overflow_hide 0x7f0800ad
int drawable exo_styled_controls_overflow_show 0x7f0800ae
int drawable exo_styled_controls_pause 0x7f0800af
int drawable exo_styled_controls_play 0x7f0800b0
int drawable exo_styled_controls_previous 0x7f0800b1
int drawable exo_styled_controls_repeat_all 0x7f0800b2
int drawable exo_styled_controls_repeat_off 0x7f0800b3
int drawable exo_styled_controls_repeat_one 0x7f0800b4
int drawable exo_styled_controls_rewind 0x7f0800b5
int drawable exo_styled_controls_settings 0x7f0800b6
int drawable exo_styled_controls_shuffle_off 0x7f0800b7
int drawable exo_styled_controls_shuffle_on 0x7f0800b8
int drawable exo_styled_controls_speed 0x7f0800b9
int drawable exo_styled_controls_subtitle_off 0x7f0800ba
int drawable exo_styled_controls_subtitle_on 0x7f0800bb
int drawable exo_styled_controls_vr 0x7f0800bc
int drawable googleg_disabled_color_18 0x7f0800bd
int drawable googleg_standard_color_18 0x7f0800be
int drawable hiding 0x7f0800bf
int drawable ic_add 0x7f0800c0
int drawable ic_add_circle_outline 0x7f0800c1
int drawable ic_addfolder 0x7f0800c2
int drawable ic_addnew 0x7f0800c3
int drawable ic_alcohol 0x7f0800c4
int drawable ic_allergy 0x7f0800c5
int drawable ic_calendar 0x7f0800c6
int drawable ic_calendar_today 0x7f0800c7
int drawable ic_call_answer 0x7f0800c8
int drawable ic_call_answer_low 0x7f0800c9
int drawable ic_call_answer_video 0x7f0800ca
int drawable ic_call_answer_video_low 0x7f0800cb
int drawable ic_call_decline 0x7f0800cc
int drawable ic_call_decline_low 0x7f0800cd
int drawable ic_camera 0x7f0800ce
int drawable ic_check_circle 0x7f0800cf
int drawable ic_close 0x7f0800d0
int drawable ic_customer_service 0x7f0800d1
int drawable ic_delete 0x7f0800d2
int drawable ic_diet 0x7f0800d3
int drawable ic_dropdown 0x7f0800d4
int drawable ic_edit 0x7f0800d5
int drawable ic_exercise 0x7f0800d6
int drawable ic_family 0x7f0800d7
int drawable ic_feedback 0x7f0800d8
int drawable ic_female 0x7f0800d9
int drawable ic_filter 0x7f0800da
int drawable ic_folder 0x7f0800db
int drawable ic_font_size 0x7f0800dc
int drawable ic_help 0x7f0800dd
int drawable ic_image 0x7f0800de
int drawable ic_keyboard 0x7f0800df
int drawable ic_launcher 0x7f0800e0
int drawable ic_launcher_background 0x7f0800e1
int drawable ic_launcher_foreground 0x7f0800e2
int drawable ic_leaf 0x7f0800e3
int drawable ic_lightbulboff 0x7f0800e4
int drawable ic_lightbulbon 0x7f0800e5
int drawable ic_list 0x7f0800e6
int drawable ic_location 0x7f0800e7
int drawable ic_logout 0x7f0800e8
int drawable ic_male 0x7f0800e9
int drawable ic_medical_history 0x7f0800ea
int drawable ic_medication 0x7f0800eb
int drawable ic_menstrual 0x7f0800ec
int drawable ic_mic 0x7f0800ed
int drawable ic_more_vert 0x7f0800ee
int drawable ic_pain 0x7f0800ef
int drawable ic_pause 0x7f0800f0
int drawable ic_person 0x7f0800f1
int drawable ic_picture 0x7f0800f2
int drawable ic_pill 0x7f0800f3
int drawable ic_play 0x7f0800f4
int drawable ic_pregnancy 0x7f0800f5
int drawable ic_privacy 0x7f0800f6
int drawable ic_qr_code 0x7f0800f7
int drawable ic_save 0x7f0800f8
int drawable ic_scan 0x7f0800f9
int drawable ic_scanning 0x7f0800fa
int drawable ic_security 0x7f0800fb
int drawable ic_send 0x7f0800fc
int drawable ic_settings 0x7f0800fd
int drawable ic_share 0x7f0800fe
int drawable ic_smoking 0x7f0800ff
int drawable ic_switch_arrow 0x7f080100
int drawable ic_user_agreement 0x7f080101
int drawable ic_vaccine 0x7f080102
int drawable ic_warning 0x7f080103
int drawable icon_add 0x7f080104
int drawable jumping_rope 0x7f080105
int drawable left_arrow 0x7f080106
int drawable lunch_beef_potato 0x7f080107
int drawable minebackground 0x7f080108
int drawable modify_information 0x7f080109
int drawable notification_action_background 0x7f08010a
int drawable notification_bg 0x7f08010b
int drawable notification_bg_low 0x7f08010c
int drawable notification_bg_low_normal 0x7f08010d
int drawable notification_bg_low_pressed 0x7f08010e
int drawable notification_bg_normal 0x7f08010f
int drawable notification_bg_normal_pressed 0x7f080110
int drawable notification_icon_background 0x7f080111
int drawable notification_oversize_large_icon_bg 0x7f080112
int drawable notification_template_icon_bg 0x7f080113
int drawable notification_template_icon_low_bg 0x7f080114
int drawable notification_tile_bg 0x7f080115
int drawable notify_panel_notification_icon_bg 0x7f080116
int drawable permission_back 0x7f080117
int drawable right_arrow 0x7f080118
int drawable seeing 0x7f080119
int drawable sign_background 0x7f08011a
int drawable test_level_drawable 0x7f08011b
int drawable tooltip_frame_dark 0x7f08011c
int drawable tooltip_frame_light 0x7f08011d
int drawable yoga_illustration 0x7f08011e
int drawable yoga_pose 0x7f08011f
int font roboto_medium_numbers 0x7f090000
int id ALT 0x7f0a0000
int id CTRL 0x7f0a0001
int id FUNCTION 0x7f0a0002
int id META 0x7f0a0003
int id SHIFT 0x7f0a0004
int id SYM 0x7f0a0005
int id accessibility_action_clickable_span 0x7f0a0006
int id accessibility_custom_action_0 0x7f0a0007
int id accessibility_custom_action_1 0x7f0a0008
int id accessibility_custom_action_10 0x7f0a0009
int id accessibility_custom_action_11 0x7f0a000a
int id accessibility_custom_action_12 0x7f0a000b
int id accessibility_custom_action_13 0x7f0a000c
int id accessibility_custom_action_14 0x7f0a000d
int id accessibility_custom_action_15 0x7f0a000e
int id accessibility_custom_action_16 0x7f0a000f
int id accessibility_custom_action_17 0x7f0a0010
int id accessibility_custom_action_18 0x7f0a0011
int id accessibility_custom_action_19 0x7f0a0012
int id accessibility_custom_action_2 0x7f0a0013
int id accessibility_custom_action_20 0x7f0a0014
int id accessibility_custom_action_21 0x7f0a0015
int id accessibility_custom_action_22 0x7f0a0016
int id accessibility_custom_action_23 0x7f0a0017
int id accessibility_custom_action_24 0x7f0a0018
int id accessibility_custom_action_25 0x7f0a0019
int id accessibility_custom_action_26 0x7f0a001a
int id accessibility_custom_action_27 0x7f0a001b
int id accessibility_custom_action_28 0x7f0a001c
int id accessibility_custom_action_29 0x7f0a001d
int id accessibility_custom_action_3 0x7f0a001e
int id accessibility_custom_action_30 0x7f0a001f
int id accessibility_custom_action_31 0x7f0a0020
int id accessibility_custom_action_4 0x7f0a0021
int id accessibility_custom_action_5 0x7f0a0022
int id accessibility_custom_action_6 0x7f0a0023
int id accessibility_custom_action_7 0x7f0a0024
int id accessibility_custom_action_8 0x7f0a0025
int id accessibility_custom_action_9 0x7f0a0026
int id action0 0x7f0a0027
int id action_bar 0x7f0a0028
int id action_bar_activity_content 0x7f0a0029
int id action_bar_container 0x7f0a002a
int id action_bar_root 0x7f0a002b
int id action_bar_spinner 0x7f0a002c
int id action_bar_subtitle 0x7f0a002d
int id action_bar_title 0x7f0a002e
int id action_container 0x7f0a002f
int id action_context_bar 0x7f0a0030
int id action_divider 0x7f0a0031
int id action_image 0x7f0a0032
int id action_menu_divider 0x7f0a0033
int id action_menu_presenter 0x7f0a0034
int id action_mode_bar 0x7f0a0035
int id action_mode_bar_stub 0x7f0a0036
int id action_mode_close_button 0x7f0a0037
int id action_text 0x7f0a0038
int id actions 0x7f0a0039
int id activity_chooser_view_content 0x7f0a003a
int id add 0x7f0a003b
int id adjust_height 0x7f0a003c
int id adjust_width 0x7f0a003d
int id alertTitle 0x7f0a003e
int id all 0x7f0a003f
int id always 0x7f0a0040
int id androidx_compose_ui_view_composition_context 0x7f0a0041
int id androidx_window_activity_scope 0x7f0a0042
int id async 0x7f0a0043
int id auto 0x7f0a0044
int id beginning 0x7f0a0045
int id blocking 0x7f0a0046
int id bottom 0x7f0a0047
int id buttonPanel 0x7f0a0048
int id cancel_action 0x7f0a0049
int id center 0x7f0a004a
int id centerCrop 0x7f0a004b
int id center_vertical 0x7f0a004c
int id checkbox 0x7f0a004d
int id checked 0x7f0a004e
int id chronometer 0x7f0a004f
int id coil_request_manager 0x7f0a0050
int id collapseActionView 0x7f0a0051
int id compatible 0x7f0a0052
int id compose_view_saveable_id_tag 0x7f0a0053
int id consume_window_insets_tag 0x7f0a0054
int id content 0x7f0a0055
int id contentPanel 0x7f0a0056
int id custom 0x7f0a0057
int id customPanel 0x7f0a0058
int id dark 0x7f0a0059
int id decor_content_parent 0x7f0a005a
int id default_activity_button 0x7f0a005b
int id dialog_button 0x7f0a005c
int id disableHome 0x7f0a005d
int id edit_query 0x7f0a005e
int id edit_text_id 0x7f0a005f
int id end 0x7f0a0060
int id end_padder 0x7f0a0061
int id exo_ad_overlay 0x7f0a0062
int id exo_artwork 0x7f0a0063
int id exo_audio_track 0x7f0a0064
int id exo_basic_controls 0x7f0a0065
int id exo_bottom_bar 0x7f0a0066
int id exo_buffering 0x7f0a0067
int id exo_center_controls 0x7f0a0068
int id exo_check 0x7f0a0069
int id exo_content_frame 0x7f0a006a
int id exo_controller 0x7f0a006b
int id exo_controller_placeholder 0x7f0a006c
int id exo_controls_background 0x7f0a006d
int id exo_duration 0x7f0a006e
int id exo_error_message 0x7f0a006f
int id exo_extra_controls 0x7f0a0070
int id exo_extra_controls_scroll_view 0x7f0a0071
int id exo_ffwd 0x7f0a0072
int id exo_ffwd_with_amount 0x7f0a0073
int id exo_fullscreen 0x7f0a0074
int id exo_icon 0x7f0a0075
int id exo_main_text 0x7f0a0076
int id exo_minimal_controls 0x7f0a0077
int id exo_minimal_fullscreen 0x7f0a0078
int id exo_next 0x7f0a0079
int id exo_overflow_hide 0x7f0a007a
int id exo_overflow_show 0x7f0a007b
int id exo_overlay 0x7f0a007c
int id exo_pause 0x7f0a007d
int id exo_play 0x7f0a007e
int id exo_play_pause 0x7f0a007f
int id exo_playback_speed 0x7f0a0080
int id exo_position 0x7f0a0081
int id exo_prev 0x7f0a0082
int id exo_progress 0x7f0a0083
int id exo_progress_placeholder 0x7f0a0084
int id exo_repeat_toggle 0x7f0a0085
int id exo_rew 0x7f0a0086
int id exo_rew_with_amount 0x7f0a0087
int id exo_settings 0x7f0a0088
int id exo_settings_listview 0x7f0a0089
int id exo_shuffle 0x7f0a008a
int id exo_shutter 0x7f0a008b
int id exo_sub_text 0x7f0a008c
int id exo_subtitle 0x7f0a008d
int id exo_subtitles 0x7f0a008e
int id exo_text 0x7f0a008f
int id exo_time 0x7f0a0090
int id exo_track_selection_view 0x7f0a0091
int id exo_vr 0x7f0a0092
int id expand_activities_button 0x7f0a0093
int id expanded_menu 0x7f0a0094
int id fill 0x7f0a0095
int id fillCenter 0x7f0a0096
int id fillEnd 0x7f0a0097
int id fillStart 0x7f0a0098
int id fit 0x7f0a0099
int id fitCenter 0x7f0a009a
int id fitEnd 0x7f0a009b
int id fitStart 0x7f0a009c
int id fitXY 0x7f0a009d
int id fixed_height 0x7f0a009e
int id fixed_width 0x7f0a009f
int id forever 0x7f0a00a0
int id fragment_container_view_tag 0x7f0a00a1
int id group_divider 0x7f0a00a2
int id hide_ime_id 0x7f0a00a3
int id hide_in_inspector_tag 0x7f0a00a4
int id home 0x7f0a00a5
int id homeAsUp 0x7f0a00a6
int id icon 0x7f0a00a7
int id icon_group 0x7f0a00a8
int id icon_only 0x7f0a00a9
int id ifRoom 0x7f0a00aa
int id image 0x7f0a00ab
int id info 0x7f0a00ac
int id inspection_slot_table_set 0x7f0a00ad
int id is_pooling_container_tag 0x7f0a00ae
int id italic 0x7f0a00af
int id item_touch_helper_previous_elevation 0x7f0a00b0
int id light 0x7f0a00b1
int id line1 0x7f0a00b2
int id line3 0x7f0a00b3
int id listMode 0x7f0a00b4
int id list_item 0x7f0a00b5
int id locale 0x7f0a00b6
int id ltr 0x7f0a00b7
int id media_actions 0x7f0a00b8
int id media_controller_compat_view_tag 0x7f0a00b9
int id message 0x7f0a00ba
int id middle 0x7f0a00bb
int id multiply 0x7f0a00bc
int id never 0x7f0a00bd
int id none 0x7f0a00be
int id normal 0x7f0a00bf
int id notification_background 0x7f0a00c0
int id notification_main_column 0x7f0a00c1
int id notification_main_column_container 0x7f0a00c2
int id off 0x7f0a00c3
int id on 0x7f0a00c4
int id one 0x7f0a00c5
int id parentPanel 0x7f0a00c6
int id performance 0x7f0a00c7
int id pooling_container_listener_holder_tag 0x7f0a00c8
int id progress_circular 0x7f0a00c9
int id progress_horizontal 0x7f0a00ca
int id radio 0x7f0a00cb
int id report_drawn 0x7f0a00cc
int id right_icon 0x7f0a00cd
int id right_side 0x7f0a00ce
int id rtl 0x7f0a00cf
int id screen 0x7f0a00d0
int id scrollIndicatorDown 0x7f0a00d1
int id scrollIndicatorUp 0x7f0a00d2
int id scrollView 0x7f0a00d3
int id search_badge 0x7f0a00d4
int id search_bar 0x7f0a00d5
int id search_button 0x7f0a00d6
int id search_close_btn 0x7f0a00d7
int id search_edit_frame 0x7f0a00d8
int id search_go_btn 0x7f0a00d9
int id search_mag_icon 0x7f0a00da
int id search_plate 0x7f0a00db
int id search_src_text 0x7f0a00dc
int id search_voice_btn 0x7f0a00dd
int id select_dialog_listview 0x7f0a00de
int id shortcut 0x7f0a00df
int id showCustom 0x7f0a00e0
int id showHome 0x7f0a00e1
int id showTitle 0x7f0a00e2
int id spacer 0x7f0a00e3
int id special_effects_controller_view_tag 0x7f0a00e4
int id spherical_gl_surface_view 0x7f0a00e5
int id split_action_bar 0x7f0a00e6
int id src_atop 0x7f0a00e7
int id src_in 0x7f0a00e8
int id src_over 0x7f0a00e9
int id standard 0x7f0a00ea
int id status_bar_latest_event_content 0x7f0a00eb
int id submenuarrow 0x7f0a00ec
int id submit_area 0x7f0a00ed
int id surface_view 0x7f0a00ee
int id tabMode 0x7f0a00ef
int id tag_accessibility_actions 0x7f0a00f0
int id tag_accessibility_clickable_spans 0x7f0a00f1
int id tag_accessibility_heading 0x7f0a00f2
int id tag_accessibility_pane_title 0x7f0a00f3
int id tag_on_apply_window_listener 0x7f0a00f4
int id tag_on_receive_content_listener 0x7f0a00f5
int id tag_on_receive_content_mime_types 0x7f0a00f6
int id tag_screen_reader_focusable 0x7f0a00f7
int id tag_state_description 0x7f0a00f8
int id tag_transition_group 0x7f0a00f9
int id tag_unhandled_key_event_manager 0x7f0a00fa
int id tag_unhandled_key_listeners 0x7f0a00fb
int id tag_window_insets_animation_callback 0x7f0a00fc
int id text 0x7f0a00fd
int id text2 0x7f0a00fe
int id textSpacerNoButtons 0x7f0a00ff
int id textSpacerNoTitle 0x7f0a0100
int id texture_view 0x7f0a0101
int id time 0x7f0a0102
int id title 0x7f0a0103
int id titleDividerNoCustom 0x7f0a0104
int id title_template 0x7f0a0105
int id top 0x7f0a0106
int id topPanel 0x7f0a0107
int id unchecked 0x7f0a0108
int id uniform 0x7f0a0109
int id up 0x7f0a010a
int id useLogo 0x7f0a010b
int id video_decoder_gl_surface_view 0x7f0a010c
int id view_tree_lifecycle_owner 0x7f0a010d
int id view_tree_on_back_pressed_dispatcher_owner 0x7f0a010e
int id view_tree_saved_state_registry_owner 0x7f0a010f
int id view_tree_view_model_store_owner 0x7f0a0110
int id visible_removing_fragment_view_tag 0x7f0a0111
int id when_playing 0x7f0a0112
int id wide 0x7f0a0113
int id withText 0x7f0a0114
int id wrap_content 0x7f0a0115
int id wrapped_composition_tag 0x7f0a0116
int id zoom 0x7f0a0117
int id zxing_back_button 0x7f0a0118
int id zxing_barcode_scanner 0x7f0a0119
int id zxing_barcode_surface 0x7f0a011a
int id zxing_camera_closed 0x7f0a011b
int id zxing_camera_error 0x7f0a011c
int id zxing_decode 0x7f0a011d
int id zxing_decode_failed 0x7f0a011e
int id zxing_decode_succeeded 0x7f0a011f
int id zxing_possible_result_points 0x7f0a0120
int id zxing_preview_failed 0x7f0a0121
int id zxing_prewiew_size_ready 0x7f0a0122
int id zxing_status_view 0x7f0a0123
int id zxing_viewfinder_view 0x7f0a0124
int integer abc_config_activityDefaultDur 0x7f0b0000
int integer abc_config_activityShortDur 0x7f0b0001
int integer cancel_button_image_alpha 0x7f0b0002
int integer config_tooltipAnimTime 0x7f0b0003
int integer exo_media_button_opacity_percentage_disabled 0x7f0b0004
int integer exo_media_button_opacity_percentage_enabled 0x7f0b0005
int integer google_play_services_version 0x7f0b0006
int integer status_bar_notification_info_maxnum 0x7f0b0007
int interpolator btn_checkbox_checked_mtrl_animation_interpolator_0 0x7f0c0000
int interpolator btn_checkbox_checked_mtrl_animation_interpolator_1 0x7f0c0001
int interpolator btn_checkbox_unchecked_mtrl_animation_interpolator_0 0x7f0c0002
int interpolator btn_checkbox_unchecked_mtrl_animation_interpolator_1 0x7f0c0003
int interpolator btn_radio_to_off_mtrl_animation_interpolator_0 0x7f0c0004
int interpolator btn_radio_to_on_mtrl_animation_interpolator_0 0x7f0c0005
int interpolator fast_out_slow_in 0x7f0c0006
int layout abc_action_bar_title_item 0x7f0d0000
int layout abc_action_bar_up_container 0x7f0d0001
int layout abc_action_menu_item_layout 0x7f0d0002
int layout abc_action_menu_layout 0x7f0d0003
int layout abc_action_mode_bar 0x7f0d0004
int layout abc_action_mode_close_item_material 0x7f0d0005
int layout abc_activity_chooser_view 0x7f0d0006
int layout abc_activity_chooser_view_list_item 0x7f0d0007
int layout abc_alert_dialog_button_bar_material 0x7f0d0008
int layout abc_alert_dialog_material 0x7f0d0009
int layout abc_alert_dialog_title_material 0x7f0d000a
int layout abc_cascading_menu_item_layout 0x7f0d000b
int layout abc_dialog_title_material 0x7f0d000c
int layout abc_expanded_menu_layout 0x7f0d000d
int layout abc_list_menu_item_checkbox 0x7f0d000e
int layout abc_list_menu_item_icon 0x7f0d000f
int layout abc_list_menu_item_layout 0x7f0d0010
int layout abc_list_menu_item_radio 0x7f0d0011
int layout abc_popup_menu_header_item_layout 0x7f0d0012
int layout abc_popup_menu_item_layout 0x7f0d0013
int layout abc_screen_content_include 0x7f0d0014
int layout abc_screen_simple 0x7f0d0015
int layout abc_screen_simple_overlay_action_mode 0x7f0d0016
int layout abc_screen_toolbar 0x7f0d0017
int layout abc_search_dropdown_item_icons_2line 0x7f0d0018
int layout abc_search_view 0x7f0d0019
int layout abc_select_dialog_material 0x7f0d001a
int layout abc_tooltip 0x7f0d001b
int layout custom_dialog 0x7f0d001c
int layout exo_legacy_player_control_view 0x7f0d001d
int layout exo_list_divider 0x7f0d001e
int layout exo_player_control_ffwd_button 0x7f0d001f
int layout exo_player_control_rewind_button 0x7f0d0020
int layout exo_player_control_view 0x7f0d0021
int layout exo_player_view 0x7f0d0022
int layout exo_styled_settings_list 0x7f0d0023
int layout exo_styled_settings_list_item 0x7f0d0024
int layout exo_styled_sub_settings_list_item 0x7f0d0025
int layout exo_track_selection_dialog 0x7f0d0026
int layout ime_base_split_test_activity 0x7f0d0027
int layout ime_secondary_split_test_activity 0x7f0d0028
int layout notification_action 0x7f0d0029
int layout notification_action_tombstone 0x7f0d002a
int layout notification_media_action 0x7f0d002b
int layout notification_media_cancel_action 0x7f0d002c
int layout notification_template_big_media 0x7f0d002d
int layout notification_template_big_media_custom 0x7f0d002e
int layout notification_template_big_media_narrow 0x7f0d002f
int layout notification_template_big_media_narrow_custom 0x7f0d0030
int layout notification_template_custom_big 0x7f0d0031
int layout notification_template_icon_group 0x7f0d0032
int layout notification_template_lines_media 0x7f0d0033
int layout notification_template_media 0x7f0d0034
int layout notification_template_media_custom 0x7f0d0035
int layout notification_template_part_chronometer 0x7f0d0036
int layout notification_template_part_time 0x7f0d0037
int layout select_dialog_item_material 0x7f0d0038
int layout select_dialog_multichoice_material 0x7f0d0039
int layout select_dialog_singlechoice_material 0x7f0d003a
int layout support_simple_spinner_dropdown_item 0x7f0d003b
int layout zxing_barcode_scanner 0x7f0d003c
int layout zxing_capture 0x7f0d003d
int mipmap ic_launcher 0x7f0e0000
int mipmap ic_launcher_round 0x7f0e0001
int plurals exo_controls_fastforward_by_amount_description 0x7f0f0000
int plurals exo_controls_rewind_by_amount_description 0x7f0f0001
int raw zxing_beep 0x7f100000
int string abc_action_bar_home_description 0x7f110000
int string abc_action_bar_up_description 0x7f110001
int string abc_action_menu_overflow_description 0x7f110002
int string abc_action_mode_done 0x7f110003
int string abc_activity_chooser_view_see_all 0x7f110004
int string abc_activitychooserview_choose_application 0x7f110005
int string abc_capital_off 0x7f110006
int string abc_capital_on 0x7f110007
int string abc_menu_alt_shortcut_label 0x7f110008
int string abc_menu_ctrl_shortcut_label 0x7f110009
int string abc_menu_delete_shortcut_label 0x7f11000a
int string abc_menu_enter_shortcut_label 0x7f11000b
int string abc_menu_function_shortcut_label 0x7f11000c
int string abc_menu_meta_shortcut_label 0x7f11000d
int string abc_menu_shift_shortcut_label 0x7f11000e
int string abc_menu_space_shortcut_label 0x7f11000f
int string abc_menu_sym_shortcut_label 0x7f110010
int string abc_prepend_shortcut_label 0x7f110011
int string abc_search_hint 0x7f110012
int string abc_searchview_description_clear 0x7f110013
int string abc_searchview_description_query 0x7f110014
int string abc_searchview_description_search 0x7f110015
int string abc_searchview_description_submit 0x7f110016
int string abc_searchview_description_voice 0x7f110017
int string abc_shareactionprovider_share_with 0x7f110018
int string abc_shareactionprovider_share_with_application 0x7f110019
int string abc_toolbar_collapse_description 0x7f11001a
int string androidx_startup 0x7f11001b
int string app_name 0x7f11001c
int string app_version 0x7f11001d
int string call_notification_answer_action 0x7f11001e
int string call_notification_answer_video_action 0x7f11001f
int string call_notification_decline_action 0x7f110020
int string call_notification_hang_up_action 0x7f110021
int string call_notification_incoming_text 0x7f110022
int string call_notification_ongoing_text 0x7f110023
int string call_notification_screening_text 0x7f110024
int string close_drawer 0x7f110025
int string close_sheet 0x7f110026
int string common_google_play_services_enable_button 0x7f110027
int string common_google_play_services_enable_text 0x7f110028
int string common_google_play_services_enable_title 0x7f110029
int string common_google_play_services_install_button 0x7f11002a
int string common_google_play_services_install_text 0x7f11002b
int string common_google_play_services_install_title 0x7f11002c
int string common_google_play_services_notification_channel_name 0x7f11002d
int string common_google_play_services_notification_ticker 0x7f11002e
int string common_google_play_services_unknown_issue 0x7f11002f
int string common_google_play_services_unsupported_text 0x7f110030
int string common_google_play_services_update_button 0x7f110031
int string common_google_play_services_update_text 0x7f110032
int string common_google_play_services_update_title 0x7f110033
int string common_google_play_services_updating_text 0x7f110034
int string common_google_play_services_wear_update_text 0x7f110035
int string common_open_on_phone 0x7f110036
int string common_signin_button_text 0x7f110037
int string common_signin_button_text_long 0x7f110038
int string default_error_message 0x7f110039
int string default_popup_window_title 0x7f11003a
int string define_zxingandroidembedded 0x7f11003b
int string dropdown_menu 0x7f11003c
int string exo_controls_cc_disabled_description 0x7f11003d
int string exo_controls_cc_enabled_description 0x7f11003e
int string exo_controls_custom_playback_speed 0x7f11003f
int string exo_controls_fastforward_description 0x7f110040
int string exo_controls_fullscreen_enter_description 0x7f110041
int string exo_controls_fullscreen_exit_description 0x7f110042
int string exo_controls_hide 0x7f110043
int string exo_controls_next_description 0x7f110044
int string exo_controls_overflow_hide_description 0x7f110045
int string exo_controls_overflow_show_description 0x7f110046
int string exo_controls_pause_description 0x7f110047
int string exo_controls_play_description 0x7f110048
int string exo_controls_playback_speed 0x7f110049
int string exo_controls_previous_description 0x7f11004a
int string exo_controls_repeat_all_description 0x7f11004b
int string exo_controls_repeat_off_description 0x7f11004c
int string exo_controls_repeat_one_description 0x7f11004d
int string exo_controls_rewind_description 0x7f11004e
int string exo_controls_seek_bar_description 0x7f11004f
int string exo_controls_settings_description 0x7f110050
int string exo_controls_show 0x7f110051
int string exo_controls_shuffle_off_description 0x7f110052
int string exo_controls_shuffle_on_description 0x7f110053
int string exo_controls_stop_description 0x7f110054
int string exo_controls_time_placeholder 0x7f110055
int string exo_controls_vr_description 0x7f110056
int string exo_download_completed 0x7f110057
int string exo_download_description 0x7f110058
int string exo_download_downloading 0x7f110059
int string exo_download_failed 0x7f11005a
int string exo_download_notification_channel_name 0x7f11005b
int string exo_download_paused 0x7f11005c
int string exo_download_paused_for_network 0x7f11005d
int string exo_download_paused_for_wifi 0x7f11005e
int string exo_download_removing 0x7f11005f
int string exo_item_list 0x7f110060
int string exo_track_bitrate 0x7f110061
int string exo_track_mono 0x7f110062
int string exo_track_resolution 0x7f110063
int string exo_track_role_alternate 0x7f110064
int string exo_track_role_closed_captions 0x7f110065
int string exo_track_role_commentary 0x7f110066
int string exo_track_role_supplementary 0x7f110067
int string exo_track_selection_auto 0x7f110068
int string exo_track_selection_none 0x7f110069
int string exo_track_selection_title_audio 0x7f11006a
int string exo_track_selection_title_text 0x7f11006b
int string exo_track_selection_title_video 0x7f11006c
int string exo_track_stereo 0x7f11006d
int string exo_track_surround 0x7f11006e
int string exo_track_surround_5_point_1 0x7f11006f
int string exo_track_surround_7_point_1 0x7f110070
int string exo_track_unknown 0x7f110071
int string in_progress 0x7f110072
int string indeterminate 0x7f110073
int string library_zxingandroidembedded_author 0x7f110074
int string library_zxingandroidembedded_authorWebsite 0x7f110075
int string library_zxingandroidembedded_isOpenSource 0x7f110076
int string library_zxingandroidembedded_libraryDescription 0x7f110077
int string library_zxingandroidembedded_libraryName 0x7f110078
int string library_zxingandroidembedded_libraryVersion 0x7f110079
int string library_zxingandroidembedded_libraryWebsite 0x7f11007a
int string library_zxingandroidembedded_licenseId 0x7f11007b
int string library_zxingandroidembedded_repositoryLink 0x7f11007c
int string m3c_bottom_sheet_collapse_description 0x7f11007d
int string m3c_bottom_sheet_dismiss_description 0x7f11007e
int string m3c_bottom_sheet_drag_handle_description 0x7f11007f
int string m3c_bottom_sheet_expand_description 0x7f110080
int string m3c_bottom_sheet_pane_title 0x7f110081
int string m3c_date_input_headline 0x7f110082
int string m3c_date_input_headline_description 0x7f110083
int string m3c_date_input_invalid_for_pattern 0x7f110084
int string m3c_date_input_invalid_not_allowed 0x7f110085
int string m3c_date_input_invalid_year_range 0x7f110086
int string m3c_date_input_label 0x7f110087
int string m3c_date_input_no_input_description 0x7f110088
int string m3c_date_input_title 0x7f110089
int string m3c_date_picker_headline 0x7f11008a
int string m3c_date_picker_headline_description 0x7f11008b
int string m3c_date_picker_navigate_to_year_description 0x7f11008c
int string m3c_date_picker_no_selection_description 0x7f11008d
int string m3c_date_picker_scroll_to_earlier_years 0x7f11008e
int string m3c_date_picker_scroll_to_later_years 0x7f11008f
int string m3c_date_picker_switch_to_calendar_mode 0x7f110090
int string m3c_date_picker_switch_to_day_selection 0x7f110091
int string m3c_date_picker_switch_to_input_mode 0x7f110092
int string m3c_date_picker_switch_to_next_month 0x7f110093
int string m3c_date_picker_switch_to_previous_month 0x7f110094
int string m3c_date_picker_switch_to_year_selection 0x7f110095
int string m3c_date_picker_title 0x7f110096
int string m3c_date_picker_today_description 0x7f110097
int string m3c_date_picker_year_picker_pane_title 0x7f110098
int string m3c_date_range_input_invalid_range_input 0x7f110099
int string m3c_date_range_input_title 0x7f11009a
int string m3c_date_range_picker_day_in_range 0x7f11009b
int string m3c_date_range_picker_end_headline 0x7f11009c
int string m3c_date_range_picker_scroll_to_next_month 0x7f11009d
int string m3c_date_range_picker_scroll_to_previous_month 0x7f11009e
int string m3c_date_range_picker_start_headline 0x7f11009f
int string m3c_date_range_picker_title 0x7f1100a0
int string m3c_dialog 0x7f1100a1
int string m3c_dropdown_menu_collapsed 0x7f1100a2
int string m3c_dropdown_menu_expanded 0x7f1100a3
int string m3c_search_bar_search 0x7f1100a4
int string m3c_snackbar_dismiss 0x7f1100a5
int string m3c_suggestions_available 0x7f1100a6
int string m3c_time_picker_am 0x7f1100a7
int string m3c_time_picker_hour 0x7f1100a8
int string m3c_time_picker_hour_24h_suffix 0x7f1100a9
int string m3c_time_picker_hour_selection 0x7f1100aa
int string m3c_time_picker_hour_suffix 0x7f1100ab
int string m3c_time_picker_hour_text_field 0x7f1100ac
int string m3c_time_picker_minute 0x7f1100ad
int string m3c_time_picker_minute_selection 0x7f1100ae
int string m3c_time_picker_minute_suffix 0x7f1100af
int string m3c_time_picker_minute_text_field 0x7f1100b0
int string m3c_time_picker_period_toggle_description 0x7f1100b1
int string m3c_time_picker_pm 0x7f1100b2
int string m3c_tooltip_long_press_label 0x7f1100b3
int string m3c_tooltip_pane_description 0x7f1100b4
int string navigation_menu 0x7f1100b5
int string not_selected 0x7f1100b6
int string off 0x7f1100b7
int string on 0x7f1100b8
int string range_end 0x7f1100b9
int string range_start 0x7f1100ba
int string search_menu_title 0x7f1100bb
int string selected 0x7f1100bc
int string status_bar_notification_info_overflow 0x7f1100bd
int string switch_role 0x7f1100be
int string tab 0x7f1100bf
int string template_percent 0x7f1100c0
int string tooltip_description 0x7f1100c1
int string tooltip_label 0x7f1100c2
int string zxing_app_name 0x7f1100c3
int string zxing_button_ok 0x7f1100c4
int string zxing_msg_camera_framework_bug 0x7f1100c5
int string zxing_msg_default_status 0x7f1100c6
int style AlertDialog_AppCompat 0x7f120000
int style AlertDialog_AppCompat_Light 0x7f120001
int style Animation_AppCompat_Dialog 0x7f120002
int style Animation_AppCompat_DropDownUp 0x7f120003
int style Animation_AppCompat_Tooltip 0x7f120004
int style Base_AlertDialog_AppCompat 0x7f120005
int style Base_AlertDialog_AppCompat_Light 0x7f120006
int style Base_Animation_AppCompat_Dialog 0x7f120007
int style Base_Animation_AppCompat_DropDownUp 0x7f120008
int style Base_Animation_AppCompat_Tooltip 0x7f120009
int style Base_DialogWindowTitle_AppCompat 0x7f12000a
int style Base_DialogWindowTitleBackground_AppCompat 0x7f12000b
int style Base_TextAppearance_AppCompat 0x7f12000c
int style Base_TextAppearance_AppCompat_Body1 0x7f12000d
int style Base_TextAppearance_AppCompat_Body2 0x7f12000e
int style Base_TextAppearance_AppCompat_Button 0x7f12000f
int style Base_TextAppearance_AppCompat_Caption 0x7f120010
int style Base_TextAppearance_AppCompat_Display1 0x7f120011
int style Base_TextAppearance_AppCompat_Display2 0x7f120012
int style Base_TextAppearance_AppCompat_Display3 0x7f120013
int style Base_TextAppearance_AppCompat_Display4 0x7f120014
int style Base_TextAppearance_AppCompat_Headline 0x7f120015
int style Base_TextAppearance_AppCompat_Inverse 0x7f120016
int style Base_TextAppearance_AppCompat_Large 0x7f120017
int style Base_TextAppearance_AppCompat_Large_Inverse 0x7f120018
int style Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Large 0x7f120019
int style Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Small 0x7f12001a
int style Base_TextAppearance_AppCompat_Medium 0x7f12001b
int style Base_TextAppearance_AppCompat_Medium_Inverse 0x7f12001c
int style Base_TextAppearance_AppCompat_Menu 0x7f12001d
int style Base_TextAppearance_AppCompat_SearchResult 0x7f12001e
int style Base_TextAppearance_AppCompat_SearchResult_Subtitle 0x7f12001f
int style Base_TextAppearance_AppCompat_SearchResult_Title 0x7f120020
int style Base_TextAppearance_AppCompat_Small 0x7f120021
int style Base_TextAppearance_AppCompat_Small_Inverse 0x7f120022
int style Base_TextAppearance_AppCompat_Subhead 0x7f120023
int style Base_TextAppearance_AppCompat_Subhead_Inverse 0x7f120024
int style Base_TextAppearance_AppCompat_Title 0x7f120025
int style Base_TextAppearance_AppCompat_Title_Inverse 0x7f120026
int style Base_TextAppearance_AppCompat_Tooltip 0x7f120027
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Menu 0x7f120028
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle 0x7f120029
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse 0x7f12002a
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Title 0x7f12002b
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse 0x7f12002c
int style Base_TextAppearance_AppCompat_Widget_ActionMode_Subtitle 0x7f12002d
int style Base_TextAppearance_AppCompat_Widget_ActionMode_Title 0x7f12002e
int style Base_TextAppearance_AppCompat_Widget_Button 0x7f12002f
int style Base_TextAppearance_AppCompat_Widget_Button_Borderless_Colored 0x7f120030
int style Base_TextAppearance_AppCompat_Widget_Button_Colored 0x7f120031
int style Base_TextAppearance_AppCompat_Widget_Button_Inverse 0x7f120032
int style Base_TextAppearance_AppCompat_Widget_DropDownItem 0x7f120033
int style Base_TextAppearance_AppCompat_Widget_PopupMenu_Header 0x7f120034
int style Base_TextAppearance_AppCompat_Widget_PopupMenu_Large 0x7f120035
int style Base_TextAppearance_AppCompat_Widget_PopupMenu_Small 0x7f120036
int style Base_TextAppearance_AppCompat_Widget_Switch 0x7f120037
int style Base_TextAppearance_AppCompat_Widget_TextView_SpinnerItem 0x7f120038
int style Base_TextAppearance_Widget_AppCompat_ExpandedMenu_Item 0x7f120039
int style Base_TextAppearance_Widget_AppCompat_Toolbar_Subtitle 0x7f12003a
int style Base_TextAppearance_Widget_AppCompat_Toolbar_Title 0x7f12003b
int style Base_Theme_AppCompat 0x7f12003c
int style Base_Theme_AppCompat_CompactMenu 0x7f12003d
int style Base_Theme_AppCompat_Dialog 0x7f12003e
int style Base_Theme_AppCompat_Dialog_Alert 0x7f12003f
int style Base_Theme_AppCompat_Dialog_FixedSize 0x7f120040
int style Base_Theme_AppCompat_Dialog_MinWidth 0x7f120041
int style Base_Theme_AppCompat_DialogWhenLarge 0x7f120042
int style Base_Theme_AppCompat_Light 0x7f120043
int style Base_Theme_AppCompat_Light_DarkActionBar 0x7f120044
int style Base_Theme_AppCompat_Light_Dialog 0x7f120045
int style Base_Theme_AppCompat_Light_Dialog_Alert 0x7f120046
int style Base_Theme_AppCompat_Light_Dialog_FixedSize 0x7f120047
int style Base_Theme_AppCompat_Light_Dialog_MinWidth 0x7f120048
int style Base_Theme_AppCompat_Light_DialogWhenLarge 0x7f120049
int style Base_ThemeOverlay_AppCompat 0x7f12004a
int style Base_ThemeOverlay_AppCompat_ActionBar 0x7f12004b
int style Base_ThemeOverlay_AppCompat_Dark 0x7f12004c
int style Base_ThemeOverlay_AppCompat_Dark_ActionBar 0x7f12004d
int style Base_ThemeOverlay_AppCompat_Dialog 0x7f12004e
int style Base_ThemeOverlay_AppCompat_Dialog_Alert 0x7f12004f
int style Base_ThemeOverlay_AppCompat_Light 0x7f120050
int style Base_V21_Theme_AppCompat 0x7f120051
int style Base_V21_Theme_AppCompat_Dialog 0x7f120052
int style Base_V21_Theme_AppCompat_Light 0x7f120053
int style Base_V21_Theme_AppCompat_Light_Dialog 0x7f120054
int style Base_V21_ThemeOverlay_AppCompat_Dialog 0x7f120055
int style Base_V22_Theme_AppCompat 0x7f120056
int style Base_V22_Theme_AppCompat_Light 0x7f120057
int style Base_V23_Theme_AppCompat 0x7f120058
int style Base_V23_Theme_AppCompat_Light 0x7f120059
int style Base_V26_Theme_AppCompat 0x7f12005a
int style Base_V26_Theme_AppCompat_Light 0x7f12005b
int style Base_V26_Widget_AppCompat_Toolbar 0x7f12005c
int style Base_V28_Theme_AppCompat 0x7f12005d
int style Base_V28_Theme_AppCompat_Light 0x7f12005e
int style Base_V7_Theme_AppCompat 0x7f12005f
int style Base_V7_Theme_AppCompat_Dialog 0x7f120060
int style Base_V7_Theme_AppCompat_Light 0x7f120061
int style Base_V7_Theme_AppCompat_Light_Dialog 0x7f120062
int style Base_V7_ThemeOverlay_AppCompat_Dialog 0x7f120063
int style Base_V7_Widget_AppCompat_AutoCompleteTextView 0x7f120064
int style Base_V7_Widget_AppCompat_EditText 0x7f120065
int style Base_V7_Widget_AppCompat_Toolbar 0x7f120066
int style Base_Widget_AppCompat_ActionBar 0x7f120067
int style Base_Widget_AppCompat_ActionBar_Solid 0x7f120068
int style Base_Widget_AppCompat_ActionBar_TabBar 0x7f120069
int style Base_Widget_AppCompat_ActionBar_TabText 0x7f12006a
int style Base_Widget_AppCompat_ActionBar_TabView 0x7f12006b
int style Base_Widget_AppCompat_ActionButton 0x7f12006c
int style Base_Widget_AppCompat_ActionButton_CloseMode 0x7f12006d
int style Base_Widget_AppCompat_ActionButton_Overflow 0x7f12006e
int style Base_Widget_AppCompat_ActionMode 0x7f12006f
int style Base_Widget_AppCompat_ActivityChooserView 0x7f120070
int style Base_Widget_AppCompat_AutoCompleteTextView 0x7f120071
int style Base_Widget_AppCompat_Button 0x7f120072
int style Base_Widget_AppCompat_Button_Borderless 0x7f120073
int style Base_Widget_AppCompat_Button_Borderless_Colored 0x7f120074
int style Base_Widget_AppCompat_Button_ButtonBar_AlertDialog 0x7f120075
int style Base_Widget_AppCompat_Button_Colored 0x7f120076
int style Base_Widget_AppCompat_Button_Small 0x7f120077
int style Base_Widget_AppCompat_ButtonBar 0x7f120078
int style Base_Widget_AppCompat_ButtonBar_AlertDialog 0x7f120079
int style Base_Widget_AppCompat_CompoundButton_CheckBox 0x7f12007a
int style Base_Widget_AppCompat_CompoundButton_RadioButton 0x7f12007b
int style Base_Widget_AppCompat_CompoundButton_Switch 0x7f12007c
int style Base_Widget_AppCompat_DrawerArrowToggle 0x7f12007d
int style Base_Widget_AppCompat_DrawerArrowToggle_Common 0x7f12007e
int style Base_Widget_AppCompat_DropDownItem_Spinner 0x7f12007f
int style Base_Widget_AppCompat_EditText 0x7f120080
int style Base_Widget_AppCompat_ImageButton 0x7f120081
int style Base_Widget_AppCompat_Light_ActionBar 0x7f120082
int style Base_Widget_AppCompat_Light_ActionBar_Solid 0x7f120083
int style Base_Widget_AppCompat_Light_ActionBar_TabBar 0x7f120084
int style Base_Widget_AppCompat_Light_ActionBar_TabText 0x7f120085
int style Base_Widget_AppCompat_Light_ActionBar_TabText_Inverse 0x7f120086
int style Base_Widget_AppCompat_Light_ActionBar_TabView 0x7f120087
int style Base_Widget_AppCompat_Light_PopupMenu 0x7f120088
int style Base_Widget_AppCompat_Light_PopupMenu_Overflow 0x7f120089
int style Base_Widget_AppCompat_ListMenuView 0x7f12008a
int style Base_Widget_AppCompat_ListPopupWindow 0x7f12008b
int style Base_Widget_AppCompat_ListView 0x7f12008c
int style Base_Widget_AppCompat_ListView_DropDown 0x7f12008d
int style Base_Widget_AppCompat_ListView_Menu 0x7f12008e
int style Base_Widget_AppCompat_PopupMenu 0x7f12008f
int style Base_Widget_AppCompat_PopupMenu_Overflow 0x7f120090
int style Base_Widget_AppCompat_PopupWindow 0x7f120091
int style Base_Widget_AppCompat_ProgressBar 0x7f120092
int style Base_Widget_AppCompat_ProgressBar_Horizontal 0x7f120093
int style Base_Widget_AppCompat_RatingBar 0x7f120094
int style Base_Widget_AppCompat_RatingBar_Indicator 0x7f120095
int style Base_Widget_AppCompat_RatingBar_Small 0x7f120096
int style Base_Widget_AppCompat_SearchView 0x7f120097
int style Base_Widget_AppCompat_SearchView_ActionBar 0x7f120098
int style Base_Widget_AppCompat_SeekBar 0x7f120099
int style Base_Widget_AppCompat_SeekBar_Discrete 0x7f12009a
int style Base_Widget_AppCompat_Spinner 0x7f12009b
int style Base_Widget_AppCompat_Spinner_Underlined 0x7f12009c
int style Base_Widget_AppCompat_TextView 0x7f12009d
int style Base_Widget_AppCompat_TextView_SpinnerItem 0x7f12009e
int style Base_Widget_AppCompat_Toolbar 0x7f12009f
int style Base_Widget_AppCompat_Toolbar_Button_Navigation 0x7f1200a0
int style DialogWindowTheme 0x7f1200a1
int style ExoMediaButton 0x7f1200a2
int style ExoMediaButton_FastForward 0x7f1200a3
int style ExoMediaButton_Next 0x7f1200a4
int style ExoMediaButton_Pause 0x7f1200a5
int style ExoMediaButton_Play 0x7f1200a6
int style ExoMediaButton_Previous 0x7f1200a7
int style ExoMediaButton_Rewind 0x7f1200a8
int style ExoMediaButton_VR 0x7f1200a9
int style ExoStyledControls 0x7f1200aa
int style ExoStyledControls_Button 0x7f1200ab
int style ExoStyledControls_Button_Bottom 0x7f1200ac
int style ExoStyledControls_Button_Bottom_AudioTrack 0x7f1200ad
int style ExoStyledControls_Button_Bottom_CC 0x7f1200ae
int style ExoStyledControls_Button_Bottom_FullScreen 0x7f1200af
int style ExoStyledControls_Button_Bottom_OverflowHide 0x7f1200b0
int style ExoStyledControls_Button_Bottom_OverflowShow 0x7f1200b1
int style ExoStyledControls_Button_Bottom_PlaybackSpeed 0x7f1200b2
int style ExoStyledControls_Button_Bottom_RepeatToggle 0x7f1200b3
int style ExoStyledControls_Button_Bottom_Settings 0x7f1200b4
int style ExoStyledControls_Button_Bottom_Shuffle 0x7f1200b5
int style ExoStyledControls_Button_Bottom_VR 0x7f1200b6
int style ExoStyledControls_Button_Center 0x7f1200b7
int style ExoStyledControls_Button_Center_FfwdWithAmount 0x7f1200b8
int style ExoStyledControls_Button_Center_Next 0x7f1200b9
int style ExoStyledControls_Button_Center_PlayPause 0x7f1200ba
int style ExoStyledControls_Button_Center_Previous 0x7f1200bb
int style ExoStyledControls_Button_Center_RewWithAmount 0x7f1200bc
int style ExoStyledControls_TimeBar 0x7f1200bd
int style ExoStyledControls_TimeText 0x7f1200be
int style ExoStyledControls_TimeText_Duration 0x7f1200bf
int style ExoStyledControls_TimeText_Position 0x7f1200c0
int style ExoStyledControls_TimeText_Separator 0x7f1200c1
int style FloatingDialogTheme 0x7f1200c2
int style FloatingDialogWindowTheme 0x7f1200c3
int style Platform_AppCompat 0x7f1200c4
int style Platform_AppCompat_Light 0x7f1200c5
int style Platform_ThemeOverlay_AppCompat 0x7f1200c6
int style Platform_ThemeOverlay_AppCompat_Dark 0x7f1200c7
int style Platform_ThemeOverlay_AppCompat_Light 0x7f1200c8
int style Platform_V21_AppCompat 0x7f1200c9
int style Platform_V21_AppCompat_Light 0x7f1200ca
int style Platform_V25_AppCompat 0x7f1200cb
int style Platform_V25_AppCompat_Light 0x7f1200cc
int style Platform_Widget_AppCompat_Spinner 0x7f1200cd
int style RtlOverlay_DialogWindowTitle_AppCompat 0x7f1200ce
int style RtlOverlay_Widget_AppCompat_ActionBar_TitleItem 0x7f1200cf
int style RtlOverlay_Widget_AppCompat_DialogTitle_Icon 0x7f1200d0
int style RtlOverlay_Widget_AppCompat_PopupMenuItem 0x7f1200d1
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_InternalGroup 0x7f1200d2
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_Shortcut 0x7f1200d3
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_SubmenuArrow 0x7f1200d4
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_Text 0x7f1200d5
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_Title 0x7f1200d6
int style RtlOverlay_Widget_AppCompat_Search_DropDown 0x7f1200d7
int style RtlOverlay_Widget_AppCompat_Search_DropDown_Icon1 0x7f1200d8
int style RtlOverlay_Widget_AppCompat_Search_DropDown_Icon2 0x7f1200d9
int style RtlOverlay_Widget_AppCompat_Search_DropDown_Query 0x7f1200da
int style RtlOverlay_Widget_AppCompat_Search_DropDown_Text 0x7f1200db
int style RtlOverlay_Widget_AppCompat_SearchView_MagIcon 0x7f1200dc
int style RtlUnderlay_Widget_AppCompat_ActionButton 0x7f1200dd
int style RtlUnderlay_Widget_AppCompat_ActionButton_Overflow 0x7f1200de
int style TextAppearance_AppCompat 0x7f1200df
int style TextAppearance_AppCompat_Body1 0x7f1200e0
int style TextAppearance_AppCompat_Body2 0x7f1200e1
int style TextAppearance_AppCompat_Button 0x7f1200e2
int style TextAppearance_AppCompat_Caption 0x7f1200e3
int style TextAppearance_AppCompat_Display1 0x7f1200e4
int style TextAppearance_AppCompat_Display2 0x7f1200e5
int style TextAppearance_AppCompat_Display3 0x7f1200e6
int style TextAppearance_AppCompat_Display4 0x7f1200e7
int style TextAppearance_AppCompat_Headline 0x7f1200e8
int style TextAppearance_AppCompat_Inverse 0x7f1200e9
int style TextAppearance_AppCompat_Large 0x7f1200ea
int style TextAppearance_AppCompat_Large_Inverse 0x7f1200eb
int style TextAppearance_AppCompat_Light_SearchResult_Subtitle 0x7f1200ec
int style TextAppearance_AppCompat_Light_SearchResult_Title 0x7f1200ed
int style TextAppearance_AppCompat_Light_Widget_PopupMenu_Large 0x7f1200ee
int style TextAppearance_AppCompat_Light_Widget_PopupMenu_Small 0x7f1200ef
int style TextAppearance_AppCompat_Medium 0x7f1200f0
int style TextAppearance_AppCompat_Medium_Inverse 0x7f1200f1
int style TextAppearance_AppCompat_Menu 0x7f1200f2
int style TextAppearance_AppCompat_SearchResult_Subtitle 0x7f1200f3
int style TextAppearance_AppCompat_SearchResult_Title 0x7f1200f4
int style TextAppearance_AppCompat_Small 0x7f1200f5
int style TextAppearance_AppCompat_Small_Inverse 0x7f1200f6
int style TextAppearance_AppCompat_Subhead 0x7f1200f7
int style TextAppearance_AppCompat_Subhead_Inverse 0x7f1200f8
int style TextAppearance_AppCompat_Title 0x7f1200f9
int style TextAppearance_AppCompat_Title_Inverse 0x7f1200fa
int style TextAppearance_AppCompat_Tooltip 0x7f1200fb
int style TextAppearance_AppCompat_Widget_ActionBar_Menu 0x7f1200fc
int style TextAppearance_AppCompat_Widget_ActionBar_Subtitle 0x7f1200fd
int style TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse 0x7f1200fe
int style TextAppearance_AppCompat_Widget_ActionBar_Title 0x7f1200ff
int style TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse 0x7f120100
int style TextAppearance_AppCompat_Widget_ActionMode_Subtitle 0x7f120101
int style TextAppearance_AppCompat_Widget_ActionMode_Subtitle_Inverse 0x7f120102
int style TextAppearance_AppCompat_Widget_ActionMode_Title 0x7f120103
int style TextAppearance_AppCompat_Widget_ActionMode_Title_Inverse 0x7f120104
int style TextAppearance_AppCompat_Widget_Button 0x7f120105
int style TextAppearance_AppCompat_Widget_Button_Borderless_Colored 0x7f120106
int style TextAppearance_AppCompat_Widget_Button_Colored 0x7f120107
int style TextAppearance_AppCompat_Widget_Button_Inverse 0x7f120108
int style TextAppearance_AppCompat_Widget_DropDownItem 0x7f120109
int style TextAppearance_AppCompat_Widget_PopupMenu_Header 0x7f12010a
int style TextAppearance_AppCompat_Widget_PopupMenu_Large 0x7f12010b
int style TextAppearance_AppCompat_Widget_PopupMenu_Small 0x7f12010c
int style TextAppearance_AppCompat_Widget_Switch 0x7f12010d
int style TextAppearance_AppCompat_Widget_TextView_SpinnerItem 0x7f12010e
int style TextAppearance_Compat_Notification 0x7f12010f
int style TextAppearance_Compat_Notification_Info 0x7f120110
int style TextAppearance_Compat_Notification_Info_Media 0x7f120111
int style TextAppearance_Compat_Notification_Line2 0x7f120112
int style TextAppearance_Compat_Notification_Line2_Media 0x7f120113
int style TextAppearance_Compat_Notification_Media 0x7f120114
int style TextAppearance_Compat_Notification_Time 0x7f120115
int style TextAppearance_Compat_Notification_Time_Media 0x7f120116
int style TextAppearance_Compat_Notification_Title 0x7f120117
int style TextAppearance_Compat_Notification_Title_Media 0x7f120118
int style TextAppearance_Widget_AppCompat_ExpandedMenu_Item 0x7f120119
int style TextAppearance_Widget_AppCompat_Toolbar_Subtitle 0x7f12011a
int style TextAppearance_Widget_AppCompat_Toolbar_Title 0x7f12011b
int style Theme_AIHealthButler 0x7f12011c
int style Theme_AppCompat 0x7f12011d
int style Theme_AppCompat_CompactMenu 0x7f12011e
int style Theme_AppCompat_DayNight 0x7f12011f
int style Theme_AppCompat_DayNight_DarkActionBar 0x7f120120
int style Theme_AppCompat_DayNight_Dialog 0x7f120121
int style Theme_AppCompat_DayNight_Dialog_Alert 0x7f120122
int style Theme_AppCompat_DayNight_Dialog_MinWidth 0x7f120123
int style Theme_AppCompat_DayNight_DialogWhenLarge 0x7f120124
int style Theme_AppCompat_DayNight_NoActionBar 0x7f120125
int style Theme_AppCompat_Dialog 0x7f120126
int style Theme_AppCompat_Dialog_Alert 0x7f120127
int style Theme_AppCompat_Dialog_MinWidth 0x7f120128
int style Theme_AppCompat_DialogWhenLarge 0x7f120129
int style Theme_AppCompat_Empty 0x7f12012a
int style Theme_AppCompat_Light 0x7f12012b
int style Theme_AppCompat_Light_DarkActionBar 0x7f12012c
int style Theme_AppCompat_Light_Dialog 0x7f12012d
int style Theme_AppCompat_Light_Dialog_Alert 0x7f12012e
int style Theme_AppCompat_Light_Dialog_MinWidth 0x7f12012f
int style Theme_AppCompat_Light_DialogWhenLarge 0x7f120130
int style Theme_AppCompat_Light_NoActionBar 0x7f120131
int style Theme_AppCompat_NoActionBar 0x7f120132
int style ThemeOverlay_AppCompat 0x7f120133
int style ThemeOverlay_AppCompat_ActionBar 0x7f120134
int style ThemeOverlay_AppCompat_Dark 0x7f120135
int style ThemeOverlay_AppCompat_Dark_ActionBar 0x7f120136
int style ThemeOverlay_AppCompat_DayNight 0x7f120137
int style ThemeOverlay_AppCompat_DayNight_ActionBar 0x7f120138
int style ThemeOverlay_AppCompat_Dialog 0x7f120139
int style ThemeOverlay_AppCompat_Dialog_Alert 0x7f12013a
int style ThemeOverlay_AppCompat_Light 0x7f12013b
int style Widget_AppCompat_ActionBar 0x7f12013c
int style Widget_AppCompat_ActionBar_Solid 0x7f12013d
int style Widget_AppCompat_ActionBar_TabBar 0x7f12013e
int style Widget_AppCompat_ActionBar_TabText 0x7f12013f
int style Widget_AppCompat_ActionBar_TabView 0x7f120140
int style Widget_AppCompat_ActionButton 0x7f120141
int style Widget_AppCompat_ActionButton_CloseMode 0x7f120142
int style Widget_AppCompat_ActionButton_Overflow 0x7f120143
int style Widget_AppCompat_ActionMode 0x7f120144
int style Widget_AppCompat_ActivityChooserView 0x7f120145
int style Widget_AppCompat_AutoCompleteTextView 0x7f120146
int style Widget_AppCompat_Button 0x7f120147
int style Widget_AppCompat_Button_Borderless 0x7f120148
int style Widget_AppCompat_Button_Borderless_Colored 0x7f120149
int style Widget_AppCompat_Button_ButtonBar_AlertDialog 0x7f12014a
int style Widget_AppCompat_Button_Colored 0x7f12014b
int style Widget_AppCompat_Button_Small 0x7f12014c
int style Widget_AppCompat_ButtonBar 0x7f12014d
int style Widget_AppCompat_ButtonBar_AlertDialog 0x7f12014e
int style Widget_AppCompat_CompoundButton_CheckBox 0x7f12014f
int style Widget_AppCompat_CompoundButton_RadioButton 0x7f120150
int style Widget_AppCompat_CompoundButton_Switch 0x7f120151
int style Widget_AppCompat_DrawerArrowToggle 0x7f120152
int style Widget_AppCompat_DropDownItem_Spinner 0x7f120153
int style Widget_AppCompat_EditText 0x7f120154
int style Widget_AppCompat_ImageButton 0x7f120155
int style Widget_AppCompat_Light_ActionBar 0x7f120156
int style Widget_AppCompat_Light_ActionBar_Solid 0x7f120157
int style Widget_AppCompat_Light_ActionBar_Solid_Inverse 0x7f120158
int style Widget_AppCompat_Light_ActionBar_TabBar 0x7f120159
int style Widget_AppCompat_Light_ActionBar_TabBar_Inverse 0x7f12015a
int style Widget_AppCompat_Light_ActionBar_TabText 0x7f12015b
int style Widget_AppCompat_Light_ActionBar_TabText_Inverse 0x7f12015c
int style Widget_AppCompat_Light_ActionBar_TabView 0x7f12015d
int style Widget_AppCompat_Light_ActionBar_TabView_Inverse 0x7f12015e
int style Widget_AppCompat_Light_ActionButton 0x7f12015f
int style Widget_AppCompat_Light_ActionButton_CloseMode 0x7f120160
int style Widget_AppCompat_Light_ActionButton_Overflow 0x7f120161
int style Widget_AppCompat_Light_ActionMode_Inverse 0x7f120162
int style Widget_AppCompat_Light_ActivityChooserView 0x7f120163
int style Widget_AppCompat_Light_AutoCompleteTextView 0x7f120164
int style Widget_AppCompat_Light_DropDownItem_Spinner 0x7f120165
int style Widget_AppCompat_Light_ListPopupWindow 0x7f120166
int style Widget_AppCompat_Light_ListView_DropDown 0x7f120167
int style Widget_AppCompat_Light_PopupMenu 0x7f120168
int style Widget_AppCompat_Light_PopupMenu_Overflow 0x7f120169
int style Widget_AppCompat_Light_SearchView 0x7f12016a
int style Widget_AppCompat_Light_Spinner_DropDown_ActionBar 0x7f12016b
int style Widget_AppCompat_ListMenuView 0x7f12016c
int style Widget_AppCompat_ListPopupWindow 0x7f12016d
int style Widget_AppCompat_ListView 0x7f12016e
int style Widget_AppCompat_ListView_DropDown 0x7f12016f
int style Widget_AppCompat_ListView_Menu 0x7f120170
int style Widget_AppCompat_PopupMenu 0x7f120171
int style Widget_AppCompat_PopupMenu_Overflow 0x7f120172
int style Widget_AppCompat_PopupWindow 0x7f120173
int style Widget_AppCompat_ProgressBar 0x7f120174
int style Widget_AppCompat_ProgressBar_Horizontal 0x7f120175
int style Widget_AppCompat_RatingBar 0x7f120176
int style Widget_AppCompat_RatingBar_Indicator 0x7f120177
int style Widget_AppCompat_RatingBar_Small 0x7f120178
int style Widget_AppCompat_SearchView 0x7f120179
int style Widget_AppCompat_SearchView_ActionBar 0x7f12017a
int style Widget_AppCompat_SeekBar 0x7f12017b
int style Widget_AppCompat_SeekBar_Discrete 0x7f12017c
int style Widget_AppCompat_Spinner 0x7f12017d
int style Widget_AppCompat_Spinner_DropDown 0x7f12017e
int style Widget_AppCompat_Spinner_DropDown_ActionBar 0x7f12017f
int style Widget_AppCompat_Spinner_Underlined 0x7f120180
int style Widget_AppCompat_TextView 0x7f120181
int style Widget_AppCompat_TextView_SpinnerItem 0x7f120182
int style Widget_AppCompat_Toolbar 0x7f120183
int style Widget_AppCompat_Toolbar_Button_Navigation 0x7f120184
int style Widget_Compat_NotificationActionContainer 0x7f120185
int style Widget_Compat_NotificationActionText 0x7f120186
int style zxing_CaptureTheme 0x7f120187
int[] styleable ActionBar { 0x7f04003b, 0x7f04003c, 0x7f04003d, 0x7f04006c, 0x7f04006d, 0x7f04006e, 0x7f04006f, 0x7f040070, 0x7f040071, 0x7f040074, 0x7f04007a, 0x7f04007b, 0x7f04008e, 0x7f0400a7, 0x7f0400a8, 0x7f0400ab, 0x7f0400ac, 0x7f0400ad, 0x7f0400b5, 0x7f0400b8, 0x7f0400ce, 0x7f0400d6, 0x7f0400e6, 0x7f0400ea, 0x7f0400eb, 0x7f040124, 0x7f040127, 0x7f040145, 0x7f04014e }
int styleable ActionBar_background 0
int styleable ActionBar_backgroundSplit 1
int styleable ActionBar_backgroundStacked 2
int styleable ActionBar_contentInsetEnd 3
int styleable ActionBar_contentInsetEndWithActions 4
int styleable ActionBar_contentInsetLeft 5
int styleable ActionBar_contentInsetRight 6
int styleable ActionBar_contentInsetStart 7
int styleable ActionBar_contentInsetStartWithNavigation 8
int styleable ActionBar_customNavigationLayout 9
int styleable ActionBar_displayOptions 10
int styleable ActionBar_divider 11
int styleable ActionBar_elevation 12
int styleable ActionBar_height 13
int styleable ActionBar_hideOnContentScroll 14
int styleable ActionBar_homeAsUpIndicator 15
int styleable ActionBar_homeLayout 16
int styleable ActionBar_icon 17
int styleable ActionBar_indeterminateProgressStyle 18
int styleable ActionBar_itemPadding 19
int styleable ActionBar_logo 20
int styleable ActionBar_navigationMode 21
int styleable ActionBar_popupTheme 22
int styleable ActionBar_progressBarPadding 23
int styleable ActionBar_progressBarStyle 24
int styleable ActionBar_subtitle 25
int styleable ActionBar_subtitleTextStyle 26
int styleable ActionBar_title 27
int styleable ActionBar_titleTextStyle 28
int[] styleable ActionBarLayout { 0x010100b3 }
int styleable ActionBarLayout_android_layout_gravity 0
int[] styleable ActionMenuItemView { 0x0101013f }
int styleable ActionMenuItemView_android_minWidth 0
int[] styleable ActionMenuView { }
int[] styleable ActionMode { 0x7f04003b, 0x7f04003c, 0x7f04005b, 0x7f0400a7, 0x7f040127, 0x7f04014e }
int styleable ActionMode_background 0
int styleable ActionMode_backgroundSplit 1
int styleable ActionMode_closeItemLayout 2
int styleable ActionMode_height 3
int styleable ActionMode_subtitleTextStyle 4
int styleable ActionMode_titleTextStyle 5
int[] styleable ActivityChooserView { 0x7f040090, 0x7f0400b6 }
int styleable ActivityChooserView_expandActivityOverflowButtonDrawable 0
int styleable ActivityChooserView_initialActivityCount 1
int[] styleable ActivityFilter { 0x7f040023, 0x7f040025 }
int styleable ActivityFilter_activityAction 0
int styleable ActivityFilter_activityName 1
int[] styleable ActivityRule { 0x7f04002f }
int styleable ActivityRule_alwaysExpand 0
int[] styleable AlertDialog { 0x010100f2, 0x7f04004c, 0x7f04004d, 0x7f0400c3, 0x7f0400c4, 0x7f0400d3, 0x7f04010a, 0x7f040115 }
int styleable AlertDialog_android_layout 0
int styleable AlertDialog_buttonIconDimen 1
int styleable AlertDialog_buttonPanelSideLayout 2
int styleable AlertDialog_listItemLayout 3
int styleable AlertDialog_listLayout 4
int styleable AlertDialog_multiChoiceItemLayout 5
int styleable AlertDialog_showTitle 6
int styleable AlertDialog_singleChoiceItemLayout 7
int[] styleable AnimatedStateListDrawableCompat { 0x0101011c, 0x01010194, 0x01010195, 0x01010196, 0x0101030c, 0x0101030d }
int styleable AnimatedStateListDrawableCompat_android_dither 0
int styleable AnimatedStateListDrawableCompat_android_visible 1
int styleable AnimatedStateListDrawableCompat_android_variablePadding 2
int styleable AnimatedStateListDrawableCompat_android_constantSize 3
int styleable AnimatedStateListDrawableCompat_android_enterFadeDuration 4
int styleable AnimatedStateListDrawableCompat_android_exitFadeDuration 5
int[] styleable AnimatedStateListDrawableItem { 0x010100d0, 0x01010199 }
int styleable AnimatedStateListDrawableItem_android_id 0
int styleable AnimatedStateListDrawableItem_android_drawable 1
int[] styleable AnimatedStateListDrawableTransition { 0x01010199, 0x01010449, 0x0101044a, 0x0101044b }
int styleable AnimatedStateListDrawableTransition_android_drawable 0
int styleable AnimatedStateListDrawableTransition_android_toId 1
int styleable AnimatedStateListDrawableTransition_android_fromId 2
int styleable AnimatedStateListDrawableTransition_android_reversible 3
int[] styleable AppCompatEmojiHelper { }
int[] styleable AppCompatImageView { 0x01010119, 0x7f04011f, 0x7f040143, 0x7f040144 }
int styleable AppCompatImageView_android_src 0
int styleable AppCompatImageView_srcCompat 1
int styleable AppCompatImageView_tint 2
int styleable AppCompatImageView_tintMode 3
int[] styleable AppCompatSeekBar { 0x01010142, 0x7f04013f, 0x7f040140, 0x7f040141 }
int styleable AppCompatSeekBar_android_thumb 0
int styleable AppCompatSeekBar_tickMark 1
int styleable AppCompatSeekBar_tickMarkTint 2
int styleable AppCompatSeekBar_tickMarkTintMode 3
int[] styleable AppCompatTextHelper { 0x01010034, 0x0101016d, 0x0101016e, 0x0101016f, 0x01010170, 0x01010392, 0x01010393 }
int styleable AppCompatTextHelper_android_textAppearance 0
int styleable AppCompatTextHelper_android_drawableTop 1
int styleable AppCompatTextHelper_android_drawableBottom 2
int styleable AppCompatTextHelper_android_drawableLeft 3
int styleable AppCompatTextHelper_android_drawableRight 4
int styleable AppCompatTextHelper_android_drawableStart 5
int styleable AppCompatTextHelper_android_drawableEnd 6
int[] styleable AppCompatTextView { 0x01010034, 0x7f040035, 0x7f040036, 0x7f040037, 0x7f040038, 0x7f040039, 0x7f04007f, 0x7f040080, 0x7f040081, 0x7f040082, 0x7f040084, 0x7f040085, 0x7f040086, 0x7f040087, 0x7f04008f, 0x7f040098, 0x7f04009a, 0x7f0400a3, 0x7f0400bb, 0x7f0400be, 0x7f04012e, 0x7f040139 }
int styleable AppCompatTextView_android_textAppearance 0
int styleable AppCompatTextView_autoSizeMaxTextSize 1
int styleable AppCompatTextView_autoSizeMinTextSize 2
int styleable AppCompatTextView_autoSizePresetSizes 3
int styleable AppCompatTextView_autoSizeStepGranularity 4
int styleable AppCompatTextView_autoSizeTextType 5
int styleable AppCompatTextView_drawableBottomCompat 6
int styleable AppCompatTextView_drawableEndCompat 7
int styleable AppCompatTextView_drawableLeftCompat 8
int styleable AppCompatTextView_drawableRightCompat 9
int styleable AppCompatTextView_drawableStartCompat 10
int styleable AppCompatTextView_drawableTint 11
int styleable AppCompatTextView_drawableTintMode 12
int styleable AppCompatTextView_drawableTopCompat 13
int styleable AppCompatTextView_emojiCompatEnabled 14
int styleable AppCompatTextView_firstBaselineToTopHeight 15
int styleable AppCompatTextView_fontFamily 16
int styleable AppCompatTextView_fontVariationSettings 17
int styleable AppCompatTextView_lastBaselineToBottomHeight 18
int styleable AppCompatTextView_lineHeight 19
int styleable AppCompatTextView_textAllCaps 20
int styleable AppCompatTextView_textLocale 21
int[] styleable AppCompatTheme { 0x01010057, 0x010100ae, 0x7f040000, 0x7f040001, 0x7f040002, 0x7f040003, 0x7f040004, 0x7f040005, 0x7f040006, 0x7f040007, 0x7f040008, 0x7f040009, 0x7f04000a, 0x7f04000b, 0x7f04000c, 0x7f04000e, 0x7f04000f, 0x7f040010, 0x7f040011, 0x7f040012, 0x7f040013, 0x7f040014, 0x7f040015, 0x7f040016, 0x7f040017, 0x7f040018, 0x7f040019, 0x7f04001a, 0x7f04001b, 0x7f04001c, 0x7f04001d, 0x7f04001e, 0x7f04001f, 0x7f040020, 0x7f040024, 0x7f040028, 0x7f040029, 0x7f04002a, 0x7f04002b, 0x7f040034, 0x7f040043, 0x7f040045, 0x7f040046, 0x7f040047, 0x7f040048, 0x7f040049, 0x7f04004f, 0x7f040050, 0x7f040056, 0x7f040057, 0x7f04005f, 0x7f040060, 0x7f040061, 0x7f040062, 0x7f040063, 0x7f040064, 0x7f040065, 0x7f040066, 0x7f040067, 0x7f040069, 0x7f040072, 0x7f040077, 0x7f040078, 0x7f040079, 0x7f04007c, 0x7f04007e, 0x7f040089, 0x7f04008a, 0x7f04008b, 0x7f04008c, 0x7f04008d, 0x7f0400ab, 0x7f0400b3, 0x7f0400bf, 0x7f0400c0, 0x7f0400c1, 0x7f0400c2, 0x7f0400c5, 0x7f0400c6, 0x7f0400c7, 0x7f0400c8, 0x7f0400c9, 0x7f0400ca, 0x7f0400cb, 0x7f0400cc, 0x7f0400cd, 0x7f0400de, 0x7f0400df, 0x7f0400e0, 0x7f0400e5, 0x7f0400e7, 0x7f0400ef, 0x7f0400f0, 0x7f0400f1, 0x7f0400f2, 0x7f040100, 0x7f040103, 0x7f040104, 0x7f040105, 0x7f040118, 0x7f040119, 0x7f04012c, 0x7f04012f, 0x7f040130, 0x7f040131, 0x7f040132, 0x7f040133, 0x7f040134, 0x7f040135, 0x7f040136, 0x7f040137, 0x7f040138, 0x7f04014f, 0x7f040150, 0x7f040151, 0x7f040152, 0x7f04015c, 0x7f04015e, 0x7f04015f, 0x7f040160, 0x7f040161, 0x7f040162, 0x7f040163, 0x7f040164, 0x7f040165, 0x7f040166, 0x7f040167 }
int styleable AppCompatTheme_android_windowIsFloating 0
int styleable AppCompatTheme_android_windowAnimationStyle 1
int styleable AppCompatTheme_actionBarDivider 2
int styleable AppCompatTheme_actionBarItemBackground 3
int styleable AppCompatTheme_actionBarPopupTheme 4
int styleable AppCompatTheme_actionBarSize 5
int styleable AppCompatTheme_actionBarSplitStyle 6
int styleable AppCompatTheme_actionBarStyle 7
int styleable AppCompatTheme_actionBarTabBarStyle 8
int styleable AppCompatTheme_actionBarTabStyle 9
int styleable AppCompatTheme_actionBarTabTextStyle 10
int styleable AppCompatTheme_actionBarTheme 11
int styleable AppCompatTheme_actionBarWidgetTheme 12
int styleable AppCompatTheme_actionButtonStyle 13
int styleable AppCompatTheme_actionDropDownStyle 14
int styleable AppCompatTheme_actionMenuTextAppearance 15
int styleable AppCompatTheme_actionMenuTextColor 16
int styleable AppCompatTheme_actionModeBackground 17
int styleable AppCompatTheme_actionModeCloseButtonStyle 18
int styleable AppCompatTheme_actionModeCloseContentDescription 19
int styleable AppCompatTheme_actionModeCloseDrawable 20
int styleable AppCompatTheme_actionModeCopyDrawable 21
int styleable AppCompatTheme_actionModeCutDrawable 22
int styleable AppCompatTheme_actionModeFindDrawable 23
int styleable AppCompatTheme_actionModePasteDrawable 24
int styleable AppCompatTheme_actionModePopupWindowStyle 25
int styleable AppCompatTheme_actionModeSelectAllDrawable 26
int styleable AppCompatTheme_actionModeShareDrawable 27
int styleable AppCompatTheme_actionModeSplitBackground 28
int styleable AppCompatTheme_actionModeStyle 29
int styleable AppCompatTheme_actionModeTheme 30
int styleable AppCompatTheme_actionModeWebSearchDrawable 31
int styleable AppCompatTheme_actionOverflowButtonStyle 32
int styleable AppCompatTheme_actionOverflowMenuStyle 33
int styleable AppCompatTheme_activityChooserViewStyle 34
int styleable AppCompatTheme_alertDialogButtonGroupStyle 35
int styleable AppCompatTheme_alertDialogCenterButtons 36
int styleable AppCompatTheme_alertDialogStyle 37
int styleable AppCompatTheme_alertDialogTheme 38
int styleable AppCompatTheme_autoCompleteTextViewStyle 39
int styleable AppCompatTheme_borderlessButtonStyle 40
int styleable AppCompatTheme_buttonBarButtonStyle 41
int styleable AppCompatTheme_buttonBarNegativeButtonStyle 42
int styleable AppCompatTheme_buttonBarNeutralButtonStyle 43
int styleable AppCompatTheme_buttonBarPositiveButtonStyle 44
int styleable AppCompatTheme_buttonBarStyle 45
int styleable AppCompatTheme_buttonStyle 46
int styleable AppCompatTheme_buttonStyleSmall 47
int styleable AppCompatTheme_checkboxStyle 48
int styleable AppCompatTheme_checkedTextViewStyle 49
int styleable AppCompatTheme_colorAccent 50
int styleable AppCompatTheme_colorBackgroundFloating 51
int styleable AppCompatTheme_colorButtonNormal 52
int styleable AppCompatTheme_colorControlActivated 53
int styleable AppCompatTheme_colorControlHighlight 54
int styleable AppCompatTheme_colorControlNormal 55
int styleable AppCompatTheme_colorError 56
int styleable AppCompatTheme_colorPrimary 57
int styleable AppCompatTheme_colorPrimaryDark 58
int styleable AppCompatTheme_colorSwitchThumbNormal 59
int styleable AppCompatTheme_controlBackground 60
int styleable AppCompatTheme_dialogCornerRadius 61
int styleable AppCompatTheme_dialogPreferredPadding 62
int styleable AppCompatTheme_dialogTheme 63
int styleable AppCompatTheme_dividerHorizontal 64
int styleable AppCompatTheme_dividerVertical 65
int styleable AppCompatTheme_dropDownListViewStyle 66
int styleable AppCompatTheme_dropdownListPreferredItemHeight 67
int styleable AppCompatTheme_editTextBackground 68
int styleable AppCompatTheme_editTextColor 69
int styleable AppCompatTheme_editTextStyle 70
int styleable AppCompatTheme_homeAsUpIndicator 71
int styleable AppCompatTheme_imageButtonStyle 72
int styleable AppCompatTheme_listChoiceBackgroundIndicator 73
int styleable AppCompatTheme_listChoiceIndicatorMultipleAnimated 74
int styleable AppCompatTheme_listChoiceIndicatorSingleAnimated 75
int styleable AppCompatTheme_listDividerAlertDialog 76
int styleable AppCompatTheme_listMenuViewStyle 77
int styleable AppCompatTheme_listPopupWindowStyle 78
int styleable AppCompatTheme_listPreferredItemHeight 79
int styleable AppCompatTheme_listPreferredItemHeightLarge 80
int styleable AppCompatTheme_listPreferredItemHeightSmall 81
int styleable AppCompatTheme_listPreferredItemPaddingEnd 82
int styleable AppCompatTheme_listPreferredItemPaddingLeft 83
int styleable AppCompatTheme_listPreferredItemPaddingRight 84
int styleable AppCompatTheme_listPreferredItemPaddingStart 85
int styleable AppCompatTheme_panelBackground 86
int styleable AppCompatTheme_panelMenuListTheme 87
int styleable AppCompatTheme_panelMenuListWidth 88
int styleable AppCompatTheme_popupMenuStyle 89
int styleable AppCompatTheme_popupWindowStyle 90
int styleable AppCompatTheme_radioButtonStyle 91
int styleable AppCompatTheme_ratingBarStyle 92
int styleable AppCompatTheme_ratingBarStyleIndicator 93
int styleable AppCompatTheme_ratingBarStyleSmall 94
int styleable AppCompatTheme_searchViewStyle 95
int styleable AppCompatTheme_seekBarStyle 96
int styleable AppCompatTheme_selectableItemBackground 97
int styleable AppCompatTheme_selectableItemBackgroundBorderless 98
int styleable AppCompatTheme_spinnerDropDownItemStyle 99
int styleable AppCompatTheme_spinnerStyle 100
int styleable AppCompatTheme_switchStyle 101
int styleable AppCompatTheme_textAppearanceLargePopupMenu 102
int styleable AppCompatTheme_textAppearanceListItem 103
int styleable AppCompatTheme_textAppearanceListItemSecondary 104
int styleable AppCompatTheme_textAppearanceListItemSmall 105
int styleable AppCompatTheme_textAppearancePopupMenuHeader 106
int styleable AppCompatTheme_textAppearanceSearchResultSubtitle 107
int styleable AppCompatTheme_textAppearanceSearchResultTitle 108
int styleable AppCompatTheme_textAppearanceSmallPopupMenu 109
int styleable AppCompatTheme_textColorAlertDialogListItem 110
int styleable AppCompatTheme_textColorSearchUrl 111
int styleable AppCompatTheme_toolbarNavigationButtonStyle 112
int styleable AppCompatTheme_toolbarStyle 113
int styleable AppCompatTheme_tooltipForegroundColor 114
int styleable AppCompatTheme_tooltipFrameBackground 115
int styleable AppCompatTheme_viewInflaterClass 116
int styleable AppCompatTheme_windowActionBar 117
int styleable AppCompatTheme_windowActionBarOverlay 118
int styleable AppCompatTheme_windowActionModeOverlay 119
int styleable AppCompatTheme_windowFixedHeightMajor 120
int styleable AppCompatTheme_windowFixedHeightMinor 121
int styleable AppCompatTheme_windowFixedWidthMajor 122
int styleable AppCompatTheme_windowFixedWidthMinor 123
int styleable AppCompatTheme_windowMinWidthMajor 124
int styleable AppCompatTheme_windowMinWidthMinor 125
int styleable AppCompatTheme_windowNoTitle 126
int[] styleable AspectRatioFrameLayout { 0x7f0400f5 }
int styleable AspectRatioFrameLayout_resize_mode 0
int[] styleable ButtonBarLayout { 0x7f04002c }
int styleable ButtonBarLayout_allowStacking 0
int[] styleable Capability { 0x7f0400ee, 0x7f040106 }
int styleable Capability_queryPatterns 0
int styleable Capability_shortcutMatchRequired 1
int[] styleable CheckedTextView { 0x01010108, 0x7f040053, 0x7f040054, 0x7f040055 }
int styleable CheckedTextView_android_checkMark 0
int styleable CheckedTextView_checkMarkCompat 1
int styleable CheckedTextView_checkMarkTint 2
int styleable CheckedTextView_checkMarkTintMode 3
int[] styleable ColorStateListItem { 0x010101a5, 0x0101031f, 0x01010647, 0x7f04002d, 0x7f0400ba }
int styleable ColorStateListItem_android_color 0
int styleable ColorStateListItem_android_alpha 1
int styleable ColorStateListItem_android_lStar 2
int styleable ColorStateListItem_alpha 3
int styleable ColorStateListItem_lStar 4
int[] styleable CompoundButton { 0x01010107, 0x7f04004a, 0x7f040051, 0x7f040052 }
int styleable CompoundButton_android_button 0
int styleable CompoundButton_buttonCompat 1
int styleable CompoundButton_buttonTint 2
int styleable CompoundButton_buttonTintMode 3
int[] styleable DefaultTimeBar { 0x7f040026, 0x7f040027, 0x7f040041, 0x7f040042, 0x7f040044, 0x7f0400e2, 0x7f0400e3, 0x7f0400f9, 0x7f0400fa, 0x7f0400fb, 0x7f0400fc, 0x7f0400fd, 0x7f040154, 0x7f040159 }
int styleable DefaultTimeBar_ad_marker_color 0
int styleable DefaultTimeBar_ad_marker_width 1
int styleable DefaultTimeBar_bar_gravity 2
int styleable DefaultTimeBar_bar_height 3
int styleable DefaultTimeBar_buffered_color 4
int styleable DefaultTimeBar_played_ad_marker_color 5
int styleable DefaultTimeBar_played_color 6
int styleable DefaultTimeBar_scrubber_color 7
int styleable DefaultTimeBar_scrubber_disabled_size 8
int styleable DefaultTimeBar_scrubber_dragged_size 9
int styleable DefaultTimeBar_scrubber_drawable 10
int styleable DefaultTimeBar_scrubber_enabled_size 11
int styleable DefaultTimeBar_touch_target_height 12
int styleable DefaultTimeBar_unplayed_color 13
int[] styleable DrawerArrowToggle { 0x7f040031, 0x7f040032, 0x7f040040, 0x7f04005e, 0x7f040083, 0x7f0400a5, 0x7f040117, 0x7f04013b }
int styleable DrawerArrowToggle_arrowHeadLength 0
int styleable DrawerArrowToggle_arrowShaftLength 1
int styleable DrawerArrowToggle_barLength 2
int styleable DrawerArrowToggle_color 3
int styleable DrawerArrowToggle_drawableSize 4
int styleable DrawerArrowToggle_gapBetweenBars 5
int styleable DrawerArrowToggle_spinBars 6
int styleable DrawerArrowToggle_thickness 7
int[] styleable FontFamily { 0x7f04009b, 0x7f04009c, 0x7f04009d, 0x7f04009e, 0x7f04009f, 0x7f0400a0, 0x7f0400a1 }
int styleable FontFamily_fontProviderAuthority 0
int styleable FontFamily_fontProviderCerts 1
int styleable FontFamily_fontProviderFetchStrategy 2
int styleable FontFamily_fontProviderFetchTimeout 3
int styleable FontFamily_fontProviderPackage 4
int styleable FontFamily_fontProviderQuery 5
int styleable FontFamily_fontProviderSystemFontFamily 6
int[] styleable FontFamilyFont { 0x01010532, 0x01010533, 0x0101053f, 0x0101056f, 0x01010570, 0x7f040099, 0x7f0400a2, 0x7f0400a3, 0x7f0400a4, 0x7f040158 }
int styleable FontFamilyFont_android_font 0
int styleable FontFamilyFont_android_fontWeight 1
int styleable FontFamilyFont_android_fontStyle 2
int styleable FontFamilyFont_android_ttcIndex 3
int styleable FontFamilyFont_android_fontVariationSettings 4
int styleable FontFamilyFont_font 5
int styleable FontFamilyFont_fontStyle 6
int styleable FontFamilyFont_fontVariationSettings 7
int styleable FontFamilyFont_fontWeight 8
int styleable FontFamilyFont_ttcIndex 9
int[] styleable Fragment { 0x01010003, 0x010100d0, 0x010100d1 }
int styleable Fragment_android_name 0
int styleable Fragment_android_id 1
int styleable Fragment_android_tag 2
int[] styleable FragmentContainerView { 0x01010003, 0x010100d1 }
int styleable FragmentContainerView_android_name 0
int styleable FragmentContainerView_android_tag 1
int[] styleable GradientColor { 0x0101019d, 0x0101019e, 0x010101a1, 0x010101a2, 0x010101a3, 0x010101a4, 0x01010201, 0x0101020b, 0x01010510, 0x01010511, 0x01010512, 0x01010513 }
int styleable GradientColor_android_startColor 0
int styleable GradientColor_android_endColor 1
int styleable GradientColor_android_type 2
int styleable GradientColor_android_centerX 3
int styleable GradientColor_android_centerY 4
int styleable GradientColor_android_gradientRadius 5
int styleable GradientColor_android_tileMode 6
int styleable GradientColor_android_centerColor 7
int styleable GradientColor_android_startX 8
int styleable GradientColor_android_startY 9
int styleable GradientColor_android_endX 10
int styleable GradientColor_android_endY 11
int[] styleable GradientColorItem { 0x010101a5, 0x01010514 }
int styleable GradientColorItem_android_color 0
int styleable GradientColorItem_android_offset 1
int[] styleable LegacyPlayerControlView { 0x7f040026, 0x7f040027, 0x7f040041, 0x7f040042, 0x7f040044, 0x7f040073, 0x7f0400e2, 0x7f0400e3, 0x7f0400f4, 0x7f0400f9, 0x7f0400fa, 0x7f0400fb, 0x7f0400fc, 0x7f0400fd, 0x7f04010c, 0x7f04010d, 0x7f04010e, 0x7f04010f, 0x7f040110, 0x7f040112, 0x7f040142, 0x7f040154, 0x7f040159 }
int styleable LegacyPlayerControlView_ad_marker_color 0
int styleable LegacyPlayerControlView_ad_marker_width 1
int styleable LegacyPlayerControlView_bar_gravity 2
int styleable LegacyPlayerControlView_bar_height 3
int styleable LegacyPlayerControlView_buffered_color 4
int styleable LegacyPlayerControlView_controller_layout_id 5
int styleable LegacyPlayerControlView_played_ad_marker_color 6
int styleable LegacyPlayerControlView_played_color 7
int styleable LegacyPlayerControlView_repeat_toggle_modes 8
int styleable LegacyPlayerControlView_scrubber_color 9
int styleable LegacyPlayerControlView_scrubber_disabled_size 10
int styleable LegacyPlayerControlView_scrubber_dragged_size 11
int styleable LegacyPlayerControlView_scrubber_drawable 12
int styleable LegacyPlayerControlView_scrubber_enabled_size 13
int styleable LegacyPlayerControlView_show_fastforward_button 14
int styleable LegacyPlayerControlView_show_next_button 15
int styleable LegacyPlayerControlView_show_previous_button 16
int styleable LegacyPlayerControlView_show_rewind_button 17
int styleable LegacyPlayerControlView_show_shuffle_button 18
int styleable LegacyPlayerControlView_show_timeout 19
int styleable LegacyPlayerControlView_time_bar_min_update_interval 20
int styleable LegacyPlayerControlView_touch_target_height 21
int styleable LegacyPlayerControlView_unplayed_color 22
int[] styleable LinearLayoutCompat { 0x010100af, 0x010100c4, 0x01010126, 0x01010127, 0x01010128, 0x7f04007b, 0x7f04007d, 0x7f0400d1, 0x7f040108 }
int styleable LinearLayoutCompat_android_gravity 0
int styleable LinearLayoutCompat_android_orientation 1
int styleable LinearLayoutCompat_android_baselineAligned 2
int styleable LinearLayoutCompat_android_baselineAlignedChildIndex 3
int styleable LinearLayoutCompat_android_weightSum 4
int styleable LinearLayoutCompat_divider 5
int styleable LinearLayoutCompat_dividerPadding 6
int styleable LinearLayoutCompat_measureWithLargestChild 7
int styleable LinearLayoutCompat_showDividers 8
int[] styleable LinearLayoutCompat_Layout { 0x010100b3, 0x010100f4, 0x010100f5, 0x01010181 }
int styleable LinearLayoutCompat_Layout_android_layout_gravity 0
int styleable LinearLayoutCompat_Layout_android_layout_width 1
int styleable LinearLayoutCompat_Layout_android_layout_height 2
int styleable LinearLayoutCompat_Layout_android_layout_weight 3
int[] styleable ListPopupWindow { 0x010102ac, 0x010102ad }
int styleable ListPopupWindow_android_dropDownHorizontalOffset 0
int styleable ListPopupWindow_android_dropDownVerticalOffset 1
int[] styleable LoadingImageView { 0x7f040058, 0x7f0400b1, 0x7f0400b2 }
int styleable LoadingImageView_circleCrop 0
int styleable LoadingImageView_imageAspectRatio 1
int styleable LoadingImageView_imageAspectRatioAdjust 2
int[] styleable MenuGroup { 0x0101000e, 0x010100d0, 0x01010194, 0x010101de, 0x010101df, 0x010101e0 }
int styleable MenuGroup_android_enabled 0
int styleable MenuGroup_android_id 1
int styleable MenuGroup_android_visible 2
int styleable MenuGroup_android_menuCategory 3
int styleable MenuGroup_android_orderInCategory 4
int styleable MenuGroup_android_checkableBehavior 5
int[] styleable MenuItem { 0x01010002, 0x0101000e, 0x010100d0, 0x01010106, 0x01010194, 0x010101de, 0x010101df, 0x010101e1, 0x010101e2, 0x010101e3, 0x010101e4, 0x010101e5, 0x0101026f, 0x7f04000d, 0x7f040021, 0x7f040022, 0x7f04002e, 0x7f04006b, 0x7f0400ae, 0x7f0400af, 0x7f0400d8, 0x7f040107, 0x7f040153 }
int styleable MenuItem_android_icon 0
int styleable MenuItem_android_enabled 1
int styleable MenuItem_android_id 2
int styleable MenuItem_android_checked 3
int styleable MenuItem_android_visible 4
int styleable MenuItem_android_menuCategory 5
int styleable MenuItem_android_orderInCategory 6
int styleable MenuItem_android_title 7
int styleable MenuItem_android_titleCondensed 8
int styleable MenuItem_android_alphabeticShortcut 9
int styleable MenuItem_android_numericShortcut 10
int styleable MenuItem_android_checkable 11
int styleable MenuItem_android_onClick 12
int styleable MenuItem_actionLayout 13
int styleable MenuItem_actionProviderClass 14
int styleable MenuItem_actionViewClass 15
int styleable MenuItem_alphabeticModifiers 16
int styleable MenuItem_contentDescription 17
int styleable MenuItem_iconTint 18
int styleable MenuItem_iconTintMode 19
int styleable MenuItem_numericModifiers 20
int styleable MenuItem_showAsAction 21
int styleable MenuItem_tooltipText 22
int[] styleable MenuView { 0x010100ae, 0x0101012c, 0x0101012d, 0x0101012e, 0x0101012f, 0x01010130, 0x01010131, 0x7f0400e8, 0x7f040122 }
int styleable MenuView_android_windowAnimationStyle 0
int styleable MenuView_android_itemTextAppearance 1
int styleable MenuView_android_horizontalDivider 2
int styleable MenuView_android_verticalDivider 3
int styleable MenuView_android_headerBackground 4
int styleable MenuView_android_itemBackground 5
int styleable MenuView_android_itemIconDisabledAlpha 6
int styleable MenuView_preserveIconSpacing 7
int styleable MenuView_subMenuArrow 8
int[] styleable PlayerControlView { 0x7f040026, 0x7f040027, 0x7f040030, 0x7f040041, 0x7f040042, 0x7f040044, 0x7f040073, 0x7f0400e2, 0x7f0400e3, 0x7f0400f4, 0x7f0400f9, 0x7f0400fa, 0x7f0400fb, 0x7f0400fc, 0x7f0400fd, 0x7f04010c, 0x7f04010d, 0x7f04010e, 0x7f04010f, 0x7f040110, 0x7f040111, 0x7f040112, 0x7f040113, 0x7f040142, 0x7f040154, 0x7f040159 }
int styleable PlayerControlView_ad_marker_color 0
int styleable PlayerControlView_ad_marker_width 1
int styleable PlayerControlView_animation_enabled 2
int styleable PlayerControlView_bar_gravity 3
int styleable PlayerControlView_bar_height 4
int styleable PlayerControlView_buffered_color 5
int styleable PlayerControlView_controller_layout_id 6
int styleable PlayerControlView_played_ad_marker_color 7
int styleable PlayerControlView_played_color 8
int styleable PlayerControlView_repeat_toggle_modes 9
int styleable PlayerControlView_scrubber_color 10
int styleable PlayerControlView_scrubber_disabled_size 11
int styleable PlayerControlView_scrubber_dragged_size 12
int styleable PlayerControlView_scrubber_drawable 13
int styleable PlayerControlView_scrubber_enabled_size 14
int styleable PlayerControlView_show_fastforward_button 15
int styleable PlayerControlView_show_next_button 16
int styleable PlayerControlView_show_previous_button 17
int styleable PlayerControlView_show_rewind_button 18
int styleable PlayerControlView_show_shuffle_button 19
int styleable PlayerControlView_show_subtitle_button 20
int styleable PlayerControlView_show_timeout 21
int styleable PlayerControlView_show_vr_button 22
int styleable PlayerControlView_time_bar_min_update_interval 23
int styleable PlayerControlView_touch_target_height 24
int styleable PlayerControlView_unplayed_color 25
int[] styleable PlayerView { 0x7f040026, 0x7f040027, 0x7f040030, 0x7f040033, 0x7f04003a, 0x7f040041, 0x7f040042, 0x7f040044, 0x7f040073, 0x7f040076, 0x7f0400a9, 0x7f0400aa, 0x7f0400b9, 0x7f0400e2, 0x7f0400e3, 0x7f0400e4, 0x7f0400f4, 0x7f0400f5, 0x7f0400f9, 0x7f0400fa, 0x7f0400fb, 0x7f0400fc, 0x7f0400fd, 0x7f04010b, 0x7f040110, 0x7f040111, 0x7f040112, 0x7f040113, 0x7f040114, 0x7f040129, 0x7f040142, 0x7f040154, 0x7f040159, 0x7f04015a, 0x7f04015b }
int styleable PlayerView_ad_marker_color 0
int styleable PlayerView_ad_marker_width 1
int styleable PlayerView_animation_enabled 2
int styleable PlayerView_artwork_display_mode 3
int styleable PlayerView_auto_show 4
int styleable PlayerView_bar_gravity 5
int styleable PlayerView_bar_height 6
int styleable PlayerView_buffered_color 7
int styleable PlayerView_controller_layout_id 8
int styleable PlayerView_default_artwork 9
int styleable PlayerView_hide_during_ads 10
int styleable PlayerView_hide_on_touch 11
int styleable PlayerView_keep_content_on_player_reset 12
int styleable PlayerView_played_ad_marker_color 13
int styleable PlayerView_played_color 14
int styleable PlayerView_player_layout_id 15
int styleable PlayerView_repeat_toggle_modes 16
int styleable PlayerView_resize_mode 17
int styleable PlayerView_scrubber_color 18
int styleable PlayerView_scrubber_disabled_size 19
int styleable PlayerView_scrubber_dragged_size 20
int styleable PlayerView_scrubber_drawable 21
int styleable PlayerView_scrubber_enabled_size 22
int styleable PlayerView_show_buffering 23
int styleable PlayerView_show_shuffle_button 24
int styleable PlayerView_show_subtitle_button 25
int styleable PlayerView_show_timeout 26
int styleable PlayerView_show_vr_button 27
int styleable PlayerView_shutter_background_color 28
int styleable PlayerView_surface_type 29
int styleable PlayerView_time_bar_min_update_interval 30
int styleable PlayerView_touch_target_height 31
int styleable PlayerView_unplayed_color 32
int styleable PlayerView_use_artwork 33
int styleable PlayerView_use_controller 34
int[] styleable PopupWindow { 0x01010176, 0x010102c9, 0x7f0400d9 }
int styleable PopupWindow_android_popupBackground 0
int styleable PopupWindow_android_popupAnimationStyle 1
int styleable PopupWindow_overlapAnchor 2
int[] styleable PopupWindowBackgroundState { 0x7f040121 }
int styleable PopupWindowBackgroundState_state_above_anchor 0
int[] styleable PreviewView { 0x7f0400b4, 0x7f0400f7 }
int styleable PreviewView_implementationMode 0
int styleable PreviewView_scaleType 1
int[] styleable RecycleListView { 0x7f0400da, 0x7f0400dd }
int styleable RecycleListView_paddingBottomNoButtons 0
int styleable RecycleListView_paddingTopNoTitle 1
int[] styleable RecyclerView { 0x010100c4, 0x010100eb, 0x010100f1, 0x7f040091, 0x7f040092, 0x7f040093, 0x7f040094, 0x7f040095, 0x7f0400bd, 0x7f0400f6, 0x7f040116, 0x7f040120 }
int styleable RecyclerView_android_orientation 0
int styleable RecyclerView_android_clipToPadding 1
int styleable RecyclerView_android_descendantFocusability 2
int styleable RecyclerView_fastScrollEnabled 3
int styleable RecyclerView_fastScrollHorizontalThumbDrawable 4
int styleable RecyclerView_fastScrollHorizontalTrackDrawable 5
int styleable RecyclerView_fastScrollVerticalThumbDrawable 6
int styleable RecyclerView_fastScrollVerticalTrackDrawable 7
int styleable RecyclerView_layoutManager 8
int styleable RecyclerView_reverseLayout 9
int styleable RecyclerView_spanCount 10
int styleable RecyclerView_stackFromEnd 11
int[] styleable SearchView { 0x010100da, 0x0101011f, 0x01010220, 0x01010264, 0x7f04005a, 0x7f04006a, 0x7f040075, 0x7f0400a6, 0x7f0400b0, 0x7f0400bc, 0x7f0400ec, 0x7f0400ed, 0x7f0400fe, 0x7f0400ff, 0x7f040123, 0x7f040128, 0x7f04015d }
int styleable SearchView_android_focusable 0
int styleable SearchView_android_maxWidth 1
int styleable SearchView_android_inputType 2
int styleable SearchView_android_imeOptions 3
int styleable SearchView_closeIcon 4
int styleable SearchView_commitIcon 5
int styleable SearchView_defaultQueryHint 6
int styleable SearchView_goIcon 7
int styleable SearchView_iconifiedByDefault 8
int styleable SearchView_layout 9
int styleable SearchView_queryBackground 10
int styleable SearchView_queryHint 11
int styleable SearchView_searchHintIcon 12
int styleable SearchView_searchIcon 13
int styleable SearchView_submitBackground 14
int styleable SearchView_suggestionRowLayout 15
int styleable SearchView_voiceIcon 16
int[] styleable SignInButton { 0x7f04004e, 0x7f040068, 0x7f0400f8 }
int styleable SignInButton_buttonSize 0
int styleable SignInButton_colorScheme 1
int styleable SignInButton_scopeUris 2
int[] styleable Spinner { 0x010100b2, 0x01010176, 0x0101017b, 0x01010262, 0x7f0400e6 }
int styleable Spinner_android_entries 0
int styleable Spinner_android_popupBackground 1
int styleable Spinner_android_prompt 2
int styleable Spinner_android_dropDownWidth 3
int styleable Spinner_popupTheme 4
int[] styleable SplitPairFilter { 0x7f0400e9, 0x7f040101, 0x7f040102 }
int styleable SplitPairFilter_primaryActivityName 0
int styleable SplitPairFilter_secondaryActivityAction 1
int styleable SplitPairFilter_secondaryActivityName 2
int[] styleable SplitPairRule { 0x7f040059, 0x7f040096, 0x7f040097, 0x7f04011a, 0x7f04011b, 0x7f04011c, 0x7f04011d }
int styleable SplitPairRule_clearTop 0
int styleable SplitPairRule_finishPrimaryWithSecondary 1
int styleable SplitPairRule_finishSecondaryWithPrimary 2
int styleable SplitPairRule_splitLayoutDirection 3
int styleable SplitPairRule_splitMinSmallestWidth 4
int styleable SplitPairRule_splitMinWidth 5
int styleable SplitPairRule_splitRatio 6
int[] styleable SplitPlaceholderRule { 0x7f0400e1, 0x7f04011a, 0x7f04011b, 0x7f04011c, 0x7f04011d }
int styleable SplitPlaceholderRule_placeholderActivityName 0
int styleable SplitPlaceholderRule_splitLayoutDirection 1
int styleable SplitPlaceholderRule_splitMinSmallestWidth 2
int styleable SplitPlaceholderRule_splitMinWidth 3
int styleable SplitPlaceholderRule_splitRatio 4
int[] styleable StateListDrawable { 0x0101011c, 0x01010194, 0x01010195, 0x01010196, 0x0101030c, 0x0101030d }
int styleable StateListDrawable_android_dither 0
int styleable StateListDrawable_android_visible 1
int styleable StateListDrawable_android_variablePadding 2
int styleable StateListDrawable_android_constantSize 3
int styleable StateListDrawable_android_enterFadeDuration 4
int styleable StateListDrawable_android_exitFadeDuration 5
int[] styleable StateListDrawableItem { 0x01010199 }
int styleable StateListDrawableItem_android_drawable 0
int[] styleable SwitchCompat { 0x01010124, 0x01010125, 0x01010142, 0x7f040109, 0x7f04011e, 0x7f04012a, 0x7f04012b, 0x7f04012d, 0x7f04013c, 0x7f04013d, 0x7f04013e, 0x7f040155, 0x7f040156, 0x7f040157 }
int styleable SwitchCompat_android_textOn 0
int styleable SwitchCompat_android_textOff 1
int styleable SwitchCompat_android_thumb 2
int styleable SwitchCompat_showText 3
int styleable SwitchCompat_splitTrack 4
int styleable SwitchCompat_switchMinWidth 5
int styleable SwitchCompat_switchPadding 6
int styleable SwitchCompat_switchTextAppearance 7
int styleable SwitchCompat_thumbTextPadding 8
int styleable SwitchCompat_thumbTint 9
int styleable SwitchCompat_thumbTintMode 10
int styleable SwitchCompat_track 11
int styleable SwitchCompat_trackTint 12
int styleable SwitchCompat_trackTintMode 13
int[] styleable TextAppearance { 0x01010095, 0x01010096, 0x01010097, 0x01010098, 0x0101009a, 0x0101009b, 0x01010161, 0x01010162, 0x01010163, 0x01010164, 0x010103ac, 0x01010585, 0x7f04009a, 0x7f0400a3, 0x7f04012e, 0x7f040139 }
int styleable TextAppearance_android_textSize 0
int styleable TextAppearance_android_typeface 1
int styleable TextAppearance_android_textStyle 2
int styleable TextAppearance_android_textColor 3
int styleable TextAppearance_android_textColorHint 4
int styleable TextAppearance_android_textColorLink 5
int styleable TextAppearance_android_shadowColor 6
int styleable TextAppearance_android_shadowDx 7
int styleable TextAppearance_android_shadowDy 8
int styleable TextAppearance_android_shadowRadius 9
int styleable TextAppearance_android_fontFamily 10
int styleable TextAppearance_android_textFontWeight 11
int styleable TextAppearance_fontFamily 12
int styleable TextAppearance_fontVariationSettings 13
int styleable TextAppearance_textAllCaps 14
int styleable TextAppearance_textLocale 15
int[] styleable Toolbar { 0x010100af, 0x01010140, 0x7f04004b, 0x7f04005c, 0x7f04005d, 0x7f04006c, 0x7f04006d, 0x7f04006e, 0x7f04006f, 0x7f040070, 0x7f040071, 0x7f0400ce, 0x7f0400cf, 0x7f0400d0, 0x7f0400d2, 0x7f0400d4, 0x7f0400d5, 0x7f0400e6, 0x7f040124, 0x7f040125, 0x7f040126, 0x7f040145, 0x7f040146, 0x7f040147, 0x7f040148, 0x7f040149, 0x7f04014a, 0x7f04014b, 0x7f04014c, 0x7f04014d }
int styleable Toolbar_android_gravity 0
int styleable Toolbar_android_minHeight 1
int styleable Toolbar_buttonGravity 2
int styleable Toolbar_collapseContentDescription 3
int styleable Toolbar_collapseIcon 4
int styleable Toolbar_contentInsetEnd 5
int styleable Toolbar_contentInsetEndWithActions 6
int styleable Toolbar_contentInsetLeft 7
int styleable Toolbar_contentInsetRight 8
int styleable Toolbar_contentInsetStart 9
int styleable Toolbar_contentInsetStartWithNavigation 10
int styleable Toolbar_logo 11
int styleable Toolbar_logoDescription 12
int styleable Toolbar_maxButtonHeight 13
int styleable Toolbar_menu 14
int styleable Toolbar_navigationContentDescription 15
int styleable Toolbar_navigationIcon 16
int styleable Toolbar_popupTheme 17
int styleable Toolbar_subtitle 18
int styleable Toolbar_subtitleTextAppearance 19
int styleable Toolbar_subtitleTextColor 20
int styleable Toolbar_title 21
int styleable Toolbar_titleMargin 22
int styleable Toolbar_titleMarginBottom 23
int styleable Toolbar_titleMarginEnd 24
int styleable Toolbar_titleMarginStart 25
int styleable Toolbar_titleMarginTop 26
int styleable Toolbar_titleMargins 27
int styleable Toolbar_titleTextAppearance 28
int styleable Toolbar_titleTextColor 29
int[] styleable View { 0x01010000, 0x010100da, 0x7f0400db, 0x7f0400dc, 0x7f04013a }
int styleable View_android_theme 0
int styleable View_android_focusable 1
int styleable View_paddingEnd 2
int styleable View_paddingStart 3
int styleable View_theme 4
int[] styleable ViewBackgroundHelper { 0x010100d4, 0x7f04003e, 0x7f04003f }
int styleable ViewBackgroundHelper_android_background 0
int styleable ViewBackgroundHelper_backgroundTint 1
int styleable ViewBackgroundHelper_backgroundTintMode 2
int[] styleable ViewStubCompat { 0x010100d0, 0x010100f2, 0x010100f3 }
int styleable ViewStubCompat_android_id 0
int styleable ViewStubCompat_android_layout 1
int styleable ViewStubCompat_android_inflatedId 2
int[] styleable zxing_camera_preview { 0x7f040168, 0x7f040169, 0x7f04016b, 0x7f04016e }
int styleable zxing_camera_preview_zxing_framing_rect_height 0
int styleable zxing_camera_preview_zxing_framing_rect_width 1
int styleable zxing_camera_preview_zxing_preview_scaling_strategy 2
int styleable zxing_camera_preview_zxing_use_texture_view 3
int[] styleable zxing_finder { 0x7f04016a, 0x7f04016c, 0x7f04016f, 0x7f040170, 0x7f040171 }
int styleable zxing_finder_zxing_possible_result_points 0
int styleable zxing_finder_zxing_result_view 1
int styleable zxing_finder_zxing_viewfinder_laser 2
int styleable zxing_finder_zxing_viewfinder_laser_visibility 3
int styleable zxing_finder_zxing_viewfinder_mask 4
int[] styleable zxing_view { 0x7f04016d }
int styleable zxing_view_zxing_scanner_layout 0
int xml backup_rules 0x7f140000
int xml data_extraction_rules 0x7f140001
int xml file_paths 0x7f140002
int xml network_security_config 0x7f140003
