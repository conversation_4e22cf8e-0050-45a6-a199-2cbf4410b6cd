package com.example.aihealthbutler.utils

import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.provider.Settings
import android.util.Log
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat

/**
 * OPPO/OnePlus设备权限处理工具类
 * 专门处理OPPO设备的权限管理问题
 */
object OppoPermissionHelper {
    
    private const val TAG = "OppoPermissionHelper"
    
    /**
     * 检查是否为OPPO/OnePlus设备
     */
    fun isOppoDevice(): Boolean {
        val manufacturer = Build.MANUFACTURER.lowercase()
        return manufacturer.contains("oppo") || 
               manufacturer.contains("oneplus") || 
               manufacturer.contains("realme")
    }
    
    /**
     * 检查录音权限状态
     */
    fun checkRecordAudioPermission(context: Context): PermissionStatus {
        val hasPermission = ContextCompat.checkSelfPermission(
            context, 
            Manifest.permission.RECORD_AUDIO
        ) == PackageManager.PERMISSION_GRANTED
        
        return when {
            hasPermission -> PermissionStatus.GRANTED
            isOppoDevice() -> PermissionStatus.OPPO_SPECIAL_HANDLING_NEEDED
            else -> PermissionStatus.DENIED
        }
    }
    
    /**
     * 请求录音权限（OPPO设备特殊处理）
     */
    fun requestRecordAudioPermission(activity: Activity, requestCode: Int = 1001) {
        if (isOppoDevice()) {
            Log.d(TAG, "Detected OPPO device, using special permission handling")
            
            // 先尝试标准权限请求
            ActivityCompat.requestPermissions(
                activity,
                arrayOf(Manifest.permission.RECORD_AUDIO),
                requestCode
            )
            
            // 如果标准请求失败，引导用户到设置页面
            if (ContextCompat.checkSelfPermission(activity, Manifest.permission.RECORD_AUDIO) 
                != PackageManager.PERMISSION_GRANTED) {
                
                // 延迟一段时间后检查权限状态
                activity.window.decorView.postDelayed({
                    if (ContextCompat.checkSelfPermission(activity, Manifest.permission.RECORD_AUDIO) 
                        != PackageManager.PERMISSION_GRANTED) {
                        openOppoPermissionSettings(activity)
                    }
                }, 2000)
            }
        } else {
            // 非OPPO设备使用标准权限请求
            ActivityCompat.requestPermissions(
                activity,
                arrayOf(Manifest.permission.RECORD_AUDIO),
                requestCode
            )
        }
    }
    
    /**
     * 打开OPPO设备的权限设置页面
     */
    fun openOppoPermissionSettings(context: Context) {
        try {
            val intent = Intent().apply {
                when {
                    Build.MANUFACTURER.equals("OPPO", ignoreCase = true) -> {
                        // OPPO ColorOS权限管理
                        action = "android.settings.APPLICATION_DETAILS_SETTINGS"
                        data = Uri.fromParts("package", context.packageName, null)
                        addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                    }
                    Build.MANUFACTURER.equals("OnePlus", ignoreCase = true) -> {
                        // OnePlus OxygenOS权限管理
                        action = Settings.ACTION_APPLICATION_DETAILS_SETTINGS
                        data = Uri.fromParts("package", context.packageName, null)
                        addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                    }
                    else -> {
                        // 通用权限设置
                        action = Settings.ACTION_APPLICATION_DETAILS_SETTINGS
                        data = Uri.fromParts("package", context.packageName, null)
                        addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                    }
                }
            }
            
            context.startActivity(intent)
            Log.d(TAG, "Opened permission settings for OPPO device")
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to open permission settings", e)
            // 备用方案：打开通用设置页面
            try {
                val fallbackIntent = Intent(Settings.ACTION_SETTINGS).apply {
                    addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                }
                context.startActivity(fallbackIntent)
            } catch (fallbackException: Exception) {
                Log.e(TAG, "Failed to open fallback settings", fallbackException)
            }
        }
    }
    
    /**
     * 获取OPPO设备权限诊断信息
     */
    fun getOppoPermissionDiagnostic(context: Context): String {
        if (!isOppoDevice()) {
            return "非OPPO设备"
        }
        
        val diagnostic = StringBuilder()
        diagnostic.append("OPPO设备权限诊断:\n")
        diagnostic.append("制造商: ${Build.MANUFACTURER}\n")
        diagnostic.append("品牌: ${Build.BRAND}\n")
        diagnostic.append("型号: ${Build.MODEL}\n")
        diagnostic.append("Android版本: ${Build.VERSION.RELEASE}\n")
        diagnostic.append("SDK版本: ${Build.VERSION.SDK_INT}\n")
        
        // 检查录音权限状态
        val recordPermission = ContextCompat.checkSelfPermission(
            context, 
            Manifest.permission.RECORD_AUDIO
        )
        diagnostic.append("录音权限状态: ${if (recordPermission == PackageManager.PERMISSION_GRANTED) "已授予" else "未授予"}\n")
        
        // 检查应用信息
        try {
            val packageInfo = context.packageManager.getPackageInfo(context.packageName, 0)
            diagnostic.append("应用版本: ${packageInfo.versionName ?: "未知"}\n")
            packageInfo.applicationInfo?.let { appInfo ->
                diagnostic.append("目标SDK: ${appInfo.targetSdkVersion}\n")
            } ?: diagnostic.append("目标SDK: 无法获取\n")
        } catch (e: Exception) {
            diagnostic.append("无法获取应用信息: ${e.message}\n")
        }
        
        return diagnostic.toString()
    }
    
    /**
     * 权限状态枚举
     */
    enum class PermissionStatus {
        GRANTED,                        // 已授予
        DENIED,                         // 被拒绝
        OPPO_SPECIAL_HANDLING_NEEDED   // OPPO设备需要特殊处理
    }
    
    /**
     * 获取权限请求的用户友好提示信息
     */
    fun getPermissionGuideMessage(): String {
        return if (isOppoDevice()) {
            """
            检测到您使用的是OPPO/OnePlus设备。
            
            由于系统限制，需要手动开启录音权限：
            
            1. 点击"去设置"按钮
            2. 找到"权限管理"或"应用权限"
            3. 找到"麦克风"或"录音"权限
            4. 开启该权限
            5. 返回应用重试
            
            如果仍有问题，请重启应用。
            """.trimIndent()
        } else {
            """
            应用需要录音权限来实现语音转文字功能。
            
            请在弹出的权限对话框中选择"允许"。
            """.trimIndent()
        }
    }
}
