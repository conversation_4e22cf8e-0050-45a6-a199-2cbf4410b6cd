package com.example.aihealthbutler.api

import android.util.Log
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import java.util.concurrent.TimeUnit
import android.os.Build
import com.example.aihealthbutler.utils.CustomDns
import java.net.Socket
import java.net.InetSocketAddress
import com.google.gson.GsonBuilder
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.io.IOException

/**
 * API客户端单例对象
 */
object ApiClient {
    // 添加详细的日志TAG前缀
    private const val TAG = "ApiClient"
    
    // API基础URL列表 - 按优先级排序
    private const val PRIMARY_URL = "https://qcx.yuneyang.top/api/"
    private const val BACKUP_URL = "https://qcx.yuneyang.top/api/"  // 统一使用同一个后端API
    private const val LOCAL_EMULATOR_URL = "https://qcx.yuneyang.top/api/"  // 模拟器也使用线上地址
    private const val LOCAL_URL = "https://qcx.yuneyang.top/api/"  // 统一使用线上地址

    // 服务器地址列表
    private val SERVER_URLS = listOf(
        PRIMARY_URL,
        BACKUP_URL,
        LOCAL_EMULATOR_URL,
        LOCAL_URL
    )

    // 当前使用的URL
    private var currentUrl = getInitialUrl()

    /**
     * 根据运行环境选择初始URL
     */
    private fun getInitialUrl(): String {
        return if (isEmulator()) {
            Log.d(TAG, "检测到模拟器环境，优先使用HTTPS")
            PRIMARY_URL
        } else {
            PRIMARY_URL
        }
    }

    /**
     * 检测是否为模拟器环境
     */
    private fun isEmulator(): Boolean {
        return (android.os.Build.FINGERPRINT.startsWith("generic")
                || android.os.Build.FINGERPRINT.startsWith("unknown")
                || android.os.Build.MODEL.contains("google_sdk")
                || android.os.Build.MODEL.contains("Emulator")
                || android.os.Build.MODEL.contains("Android SDK built for x86")
                || android.os.Build.MANUFACTURER.contains("Genymotion")
                || (android.os.Build.BRAND.startsWith("generic") && android.os.Build.DEVICE.startsWith("generic"))
                || "google_sdk" == android.os.Build.PRODUCT)
    }
    
    // 创建OkHttpClient实例
    private val client = OkHttpClient.Builder()
        .addInterceptor(HttpLoggingInterceptor().apply { 
            level = HttpLoggingInterceptor.Level.BODY 
        })
        // 添加自定义日志拦截器，记录详细的请求和响应信息
        .addInterceptor { chain ->
            val request = chain.request()
            
            // 记录详细的请求信息
            val requestStartMessage = StringBuilder().apply {
                append("发送请求: ${request.method} ${request.url}\n")
                append("请求头: \n")
                request.headers.forEach { 
                    append("  ${it.first}: ${it.second}\n") 
                }
                
                if (request.body != null) {
                    append("请求体大小: ${request.body?.contentLength() ?: "unknown"} bytes")
                    
                    if (request.method == "DELETE") {
                        append(" [注意: 这是带请求体的DELETE请求]")
                    }
                }
            }
            
            Log.d(TAG, "⬆️ $requestStartMessage")
            
            val startNs = System.nanoTime()
            try {
                // 发送请求
                val response = chain.proceed(request)
                val tookMs = (System.nanoTime() - startNs) / 1_000_000
                
                // 记录详细的响应信息
                val responseMessage = StringBuilder().apply {
                    append("收到响应: ${response.code} ${response.message} for ${request.url}\n")
                    append("耗时: ${tookMs}ms\n")
                    append("响应头: \n")
                    response.headers.forEach {
                        append("  ${it.first}: ${it.second}\n")
                    }
                    
                    if (response.body != null) {
                        append("响应体大小: ${response.body?.contentLength() ?: "unknown"} bytes")
                    }
                }
                
                val statusCode = response.code
                val logLevel = if (statusCode >= 200 && statusCode < 300) {
                    Log.d(TAG, "✅ $responseMessage")
                    "SUCCESS"
                } else if (statusCode >= 400) {
                    Log.e(TAG, "❌ $responseMessage")
                    "ERROR"
                } else {
                    Log.w(TAG, "⚠️ $responseMessage")
                    "WARNING"
                }
                
                Log.d(TAG, "[$logLevel] 响应详情: $responseMessage")
                
                response
            } catch (e: Exception) {
                val tookMs = (System.nanoTime() - startNs) / 1_000_000
                Log.e(TAG, "❌ 请求失败: ${request.url}, 耗时 ${tookMs}ms", e)
                throw e
            }
        }
        .connectTimeout(15, TimeUnit.SECONDS) // 缩短超时时间，便于快速故障转移
        .readTimeout(15, TimeUnit.SECONDS)
        .writeTimeout(15, TimeUnit.SECONDS)
        // 设置自定义的DNS解析器
        .dns(CustomDns())
        // 设置自定义的TrustManager，信任所有证书（仅用于开发环境）
        .hostnameVerifier { _, _ -> true }
        // 添加自定义拦截器以解决Android P以上的明文HTTP限制
        .addInterceptor { chain -> 
            val originalRequest = chain.request()
            val builder = originalRequest.newBuilder()
            // 添加一些可能有助于通过网络策略的请求头
            builder.addHeader("Connection", "close")
                  .addHeader("Accept", "*/*")
                  .addHeader("User-Agent", "AIHealthButler-Android")
            
            val request = builder.build()
            try {
                chain.proceed(request)
            } catch (e: Exception) {
                // 如果是SSL错误或连接问题，记录详细信息
                Log.e(TAG, "网络请求失败: ${e.message}", e)
                throw e
            }
        }
        .build()
    
    // 创建服务接口的静态实例
    val apiService: ApiService = createApiService()
    
    init {
        Log.d(TAG, "API客户端初始化，使用URL: $currentUrl")
        
        // 在后台线程中异步测试API连接
        Thread {
            testApiConnection()
        }.start()
    }
    
    // 通过反射创建API服务接口
    private fun createApiService(): ApiService {
        try {
            // 创建Retrofit.Builder
            val retrofitBuilderClass = Class.forName("retrofit2.Retrofit\$Builder")
            val builder = retrofitBuilderClass.newInstance()
            
            // 设置基础URL
            retrofitBuilderClass.getMethod("baseUrl", String::class.java)
                .invoke(builder, currentUrl)
            
            // 设置HttpClient
            retrofitBuilderClass.getMethod("client", OkHttpClient::class.java)
                .invoke(builder, client)
            
            // 创建GsonConverterFactory
            val gsonFactoryClass = Class.forName("retrofit2.converter.gson.GsonConverterFactory")
            val converterFactory = gsonFactoryClass.getMethod("create").invoke(null)
            
            // 添加转换器
            val converterFactoryClass = Class.forName("retrofit2.Converter\$Factory")
            retrofitBuilderClass.getMethod("addConverterFactory", converterFactoryClass)
                .invoke(builder, converterFactory)
            
            // 构建Retrofit
            val retrofit = retrofitBuilderClass.getMethod("build").invoke(builder)
            
            // 创建API服务
            val createMethod = retrofit.javaClass.getMethod("create", Class::class.java)
            Log.d(TAG, "成功创建API服务实例")
            return createMethod.invoke(retrofit, ApiService::class.java) as ApiService
        } catch (e: Exception) {
            Log.e(TAG, "创建API服务失败", e)
            throw RuntimeException("创建API服务失败: ${e.message}", e)
        }
    }
    
    /**
     * 使用HttpURLConnection直接测试API连接
     */
    fun testDirectConnection(): Boolean {
        for (serverUrl in SERVER_URLS) {
            try {
                Log.d(TAG, "尝试HTTPS连接: $serverUrl")
                
                // 只使用HTTPS连接
                val url = java.net.URL(serverUrl)
                val connection = url.openConnection() as javax.net.ssl.HttpsURLConnection
                
                connection.connectTimeout = 5000
                connection.readTimeout = 5000
                connection.requestMethod = "GET"
                connection.setRequestProperty("Connection", "close")
                connection.setRequestProperty("User-Agent", "AIHealthButler-Android")
                
                try {
                    Log.d(TAG, "开始HTTPS连接: $serverUrl")
                    connection.connect()
                    val responseCode = connection.responseCode
                    
                    Log.d(TAG, "✅ HTTPS连接测试成功，响应码: $responseCode")
                    
                    // 记录响应头信息
                    Log.d(TAG, "响应头信息:")
                    for (i in 0 until connection.headerFields.size) {
                        val headerName = connection.getHeaderFieldKey(i)
                        val headerValue = connection.getHeaderField(i)
                        if (headerName != null) {
                            Log.d(TAG, "  $headerName: $headerValue")
                        }
                    }
                    
                    // 连接成功，更新当前URL
                    if (currentUrl != serverUrl) {
                        Log.d(TAG, "更新当前使用的URL: $serverUrl")
                        currentUrl = serverUrl
                    }
                    
                    return true
                } finally {
                    connection.disconnect()
                }
            } catch (e: Exception) {
                Log.e(TAG, "❌ HTTPS连接失败: $serverUrl", e)
                // 继续尝试下一个URL
            }
        }
        
        // 所有URL都失败
        Log.e(TAG, "❌ 所有服务器URL连接测试均失败")
        return false
    }
    
    /**
     * 获取当前使用的基础URL
     */
    fun getBaseUrl(): String {
        return currentUrl
    }
    
    /**
     * 全面的API连接测试，包含Socket连接测试和HTTPS连接测试
     */
    fun testApiConnection(): Boolean {
        Log.d(TAG, "开始测试API连接...")
        
        // 1. 首先尝试使用主域名直接连接
        val serverDomain = CustomDns.getServerDomain()
        if (serverDomain.isNotEmpty()) {
            Log.d(TAG, "尝试使用主域名直接连接: $serverDomain")

            try {
                Log.d(TAG, "尝试Socket连接到 $serverDomain:443...")
                val socket = Socket()
                val socketAddress = InetSocketAddress(serverDomain, 443)
                socket.connect(socketAddress, 5000)
                socket.close()
                Log.d(TAG, "✅ Socket连接成功: $serverDomain:443")
                return true
            } catch (e: Exception) {
                Log.e(TAG, "❌ 域名Socket连接失败: ${e.message}", e)
            }
        }
        
        // 2. 尝试连接每个预配置的服务器URL
        for (serverUrl in SERVER_URLS) {
            Log.d(TAG, "尝试连接服务器: $serverUrl")
            
            // 解析主机名
            val hostname = try {
                val url = java.net.URL(serverUrl)
                url.host
            } catch (e: Exception) {
                Log.e(TAG, "URL解析失败: $serverUrl", e)
                continue
            }
            
            // 先尝试Socket连接测试速度更快
            try {
                Log.d(TAG, "尝试Socket连接到 $hostname:80...")
                val socket = Socket()
                val socketAddress = InetSocketAddress(hostname, 80)
                socket.connect(socketAddress, 5000)
                socket.close()
                Log.d(TAG, "✅ Socket连接成功: $hostname:80")
                
                // 更新当前URL
                currentUrl = serverUrl
                return true
            } catch (e: Exception) {
                Log.e(TAG, "❌ Socket连接失败: $hostname", e)
            }
            
            // 再尝试HTTPS连接
            try {
                Log.d(TAG, "开始HTTPS连接 $hostname...")
                val url = java.net.URL(serverUrl)
                val connection = url.openConnection() as javax.net.ssl.HttpsURLConnection
                connection.connectTimeout = 5000
                connection.readTimeout = 5000
                connection.requestMethod = "GET"
                connection.connect()
                
                val responseCode = connection.responseCode
                connection.disconnect()
                
                Log.d(TAG, "✅ HTTPS连接成功: $hostname, 响应码: $responseCode")
                
                // 更新当前URL
                currentUrl = serverUrl
                return true
            } catch (e: Exception) {
                Log.e(TAG, "❌ $hostname 连接失败: ${e.message}", e)
            }
        }
        
        Log.e(TAG, "❌ 所有服务器地址连接尝试均失败")
        return false
    }
    
    /**
     * 重置API客户端，尝试重新建立连接
     */
    fun reset() {
        Log.d(TAG, "重置API客户端")
        // 测试所有服务器连接并更新currentUrl
        if (testApiConnection()) {
            Log.d(TAG, "API客户端重置成功，当前URL: $currentUrl")
        } else {
            Log.e(TAG, "API客户端重置失败，无法连接到任何服务器")
        }
    }
    
    // 获取当前使用的URL
    fun getCurrentUrl(): String {
        return currentUrl
    }
} 