package com.example.aihealthbutler.debug

import android.os.Bundle
import android.util.Log
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.unit.dp
import androidx.lifecycle.lifecycleScope
import com.example.aihealthbutler.dify.DifyApiClient
import com.example.aihealthbutler.dify.DifyConfig
import com.example.aihealthbutler.utils.NetworkUtils
import com.example.aihealthbutler.utils.DifyConnectionTester
import kotlinx.coroutines.launch

/**
 * 网络调试Activity
 * 用于诊断网络连接问题
 */
class NetworkDebugActivity : ComponentActivity() {
    
    private lateinit var difyApiClient: DifyApiClient
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        difyApiClient = DifyApiClient(
            apiKey = DifyConfig.API_KEY,
            baseUrl = DifyConfig.BASE_URL
        )
        
        setContent {
            NetworkDebugScreen()
        }
    }
    
    @Composable
    private fun NetworkDebugScreen() {
        var debugInfo by remember { mutableStateOf("正在检测...") }
        var isLoading by remember { mutableStateOf(false) }
        
        LaunchedEffect(Unit) {
            runNetworkDiagnostics { info ->
                debugInfo = info
            }
        }
        
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
                .verticalScroll(rememberScrollState()),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = "网络诊断工具",
                style = MaterialTheme.typography.headlineMedium
            )
            
            Spacer(modifier = Modifier.height(24.dp))
            
            Button(
                onClick = {
                    isLoading = true
                    lifecycleScope.launch {
                        runNetworkDiagnostics { info ->
                            debugInfo = info
                            isLoading = false
                        }
                    }
                },
                enabled = !isLoading
            ) {
                if (isLoading) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(16.dp),
                        strokeWidth = 2.dp
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                }
                Text("重新检测")
            }
            
            Spacer(modifier = Modifier.height(24.dp))
            
            Card(
                modifier = Modifier.fillMaxWidth(),
                elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
            ) {
                Text(
                    text = debugInfo,
                    modifier = Modifier.padding(16.dp),
                    fontFamily = FontFamily.Monospace,
                    style = MaterialTheme.typography.bodySmall
                )
            }
        }
    }
    
    private suspend fun runNetworkDiagnostics(onResult: (String) -> Unit) {
        val result = buildString {
            appendLine("=== 网络诊断报告 ===")
            appendLine()

            // 使用新的Dify连接测试工具
            try {
                val difyDiagnostic = DifyConnectionTester.runFullDiagnostic(this@NetworkDebugActivity)
                append(difyDiagnostic.getDetailedReport())
                appendLine()

                // 如果连接有问题，进行传统的基础测试作为补充
                if (!difyDiagnostic.isFullyWorking()) {
                    appendLine("=== 补充诊断信息 ===")
                    appendLine()

                    // 基础网络信息
                    appendLine("1. 基础网络状态:")
                    val networkDiagnostics = NetworkUtils.getNetworkDiagnostics(this@NetworkDebugActivity)
                    append(networkDiagnostics)
                    appendLine()

                    // 服务器连接测试
                    appendLine("2. 服务器连接测试:")
                    val (host, port) = NetworkUtils.parseHostAndPort(DifyConfig.BASE_URL)
                    appendLine("目标服务器: $host:$port")

                    val serverConnectable = NetworkUtils.testServerConnection(host, port)
                    appendLine("服务器连通性: ${if (serverConnectable) "✓ 可连接" else "✗ 无法连接"}")
                    appendLine()

                    // API连接测试
                    appendLine("3. API连接测试:")
                    appendLine("API地址: ${DifyConfig.BASE_URL}")
                    appendLine("API密钥: ${DifyConfig.API_KEY.take(10)}...")

                    try {
                        val apiConnectable = difyApiClient.testConnection()
                        appendLine("API连通性: ${if (apiConnectable) "✓ 可连接" else "✗ 无法连接"}")
                    } catch (e: Exception) {
                        appendLine("API连通性: ✗ 连接异常")
                        appendLine("错误信息: ${e.message}")
                    }
                    appendLine()
                }
            } catch (e: Exception) {
                appendLine("❌ 诊断过程出现异常: ${e.message}")
                Log.e("NetworkDebug", "Diagnostic exception", e)
                appendLine()

                // 回退到基础诊断
                appendLine("=== 基础诊断信息 ===")
                val networkDiagnostics = NetworkUtils.getNetworkDiagnostics(this@NetworkDebugActivity)
                append(networkDiagnostics)
                appendLine()
            }
            
            // 详细API测试
            appendLine("4. 详细API测试:")
            try {
                val apiTestResult = difyApiClient.testApiEndpoint()
                append(apiTestResult.getDetailedReport())

                // 如果API测试失败，尝试简单的聊天请求
                if (!apiTestResult.isFullyWorking()) {
                    appendLine("\n5. 尝试简单聊天请求:")
                    try {
                        val testResponse = difyApiClient.sendChatMessage(
                            query = "Hello",
                            user = "debug-user"
                        )
                        appendLine("聊天请求: ✓ 成功")
                        appendLine("响应ID: ${testResponse.id}")
                        appendLine("响应内容: ${testResponse.answer.take(50)}...")
                    } catch (chatException: Exception) {
                        appendLine("聊天请求: ✗ 失败")
                        appendLine("错误: ${chatException.message}")

                        // 详细错误分析
                        when (chatException) {
                            is java.net.SocketException -> {
                                appendLine("分析: 网络连接被重置，可能是:")
                                appendLine("  - 服务器主动断开连接")
                                appendLine("  - 防火墙阻止连接")
                                appendLine("  - 网络不稳定")
                            }
                            is java.net.ConnectException -> {
                                appendLine("分析: 无法建立连接，可能是:")
                                appendLine("  - 服务器未运行")
                                appendLine("  - 端口被阻止")
                                appendLine("  - 网络配置问题")
                            }
                            is java.net.UnknownHostException -> {
                                appendLine("分析: 无法解析域名，可能是:")
                                appendLine("  - DNS配置问题")
                                appendLine("  - 域名不存在")
                                appendLine("  - 网络连接问题")
                            }
                            is java.io.IOException -> {
                                if (chatException.message?.contains("400") == true) {
                                    appendLine("分析: HTTP 400错误，可能是:")
                                    appendLine("  - API密钥无效")
                                    appendLine("  - 请求格式错误")
                                    appendLine("  - 缺少必要参数")
                                } else if (chatException.message?.contains("401") == true) {
                                    appendLine("分析: HTTP 401错误，可能是:")
                                    appendLine("  - API密钥无效或过期")
                                    appendLine("  - 认证头格式错误")
                                } else if (chatException.message?.contains("404") == true) {
                                    appendLine("分析: HTTP 404错误，可能是:")
                                    appendLine("  - API端点不存在")
                                    appendLine("  - URL路径错误")
                                }
                            }
                        }
                    }
                }
            } catch (e: Exception) {
                appendLine("API测试异常: ${e.message}")
                Log.e("NetworkDebug", "API test exception", e)
            }
            appendLine()
            
            // 建议和解决方案
            appendLine("\n6. 问题诊断和建议:")
            val serverConnectable = false
            if (!NetworkUtils.isNetworkAvailable(this@NetworkDebugActivity)) {
                appendLine("❌ 网络连接问题:")
                appendLine("  1. 检查WiFi/移动数据是否开启")
                appendLine("  2. 尝试访问其他网站测试网络")
                appendLine("  3. 重启网络连接")
                appendLine("  4. 检查代理设置")
            } else if (!serverConnectable) {
                appendLine("❌ 服务器连接问题:")
                appendLine("  1. 服务器地址: ${DifyConfig.BASE_URL}")
                appendLine("  2. 检查服务器是否在线")
                appendLine("  3. 尝试在浏览器中访问服务器")
                appendLine("  4. 检查防火墙/安全软件设置")
                appendLine("  5. 联系服务器管理员")
            } else {
                appendLine("⚠️ API配置问题:")
                appendLine("  1. 验证API密钥: ${DifyConfig.API_KEY.take(10)}...")
                appendLine("  2. 检查API端点是否正确")
                appendLine("  3. 确认API版本兼容性")
                appendLine("  4. 查看服务器日志")
                appendLine("  5. 尝试使用Postman等工具测试API")
            }

            appendLine("\n📋 快速修复步骤:")
            appendLine("  1. 重启应用")
            appendLine("  2. 清除应用缓存")
            appendLine("  3. 检查网络设置")
            appendLine("  4. 联系技术支持")
            
            appendLine()
            appendLine("=== 诊断完成 ===")
        }
        
        onResult(result)
    }
    
    override fun onDestroy() {
        super.onDestroy()
        difyApiClient.release()
    }
}
