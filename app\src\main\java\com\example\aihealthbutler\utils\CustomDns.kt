package com.example.aihealthbutler.utils

import android.os.Build
import android.util.Log
import okhttp3.Dns
import java.net.InetAddress
import java.net.UnknownHostException
import java.util.concurrent.TimeUnit
import java.util.concurrent.ConcurrentHashMap

/**
 * 自定义DNS解析类，用于解决Android网络问题
 * 此类可以为特定域名硬编码IP地址，避免DNS解析问题
 */
class CustomDns : Dns {
    private val TAG = "CustomDns"
    
    // 域名到IP的映射表，可以硬编码一些常用的域名
    private val hostIpMap = mapOf(
        "qcx.yuneyang.top" to "qcx.yuneyang.top" // 使用域名解析，不硬编码IP
    )
    
    // 缓存解析结果
    private val dnsCache = ConcurrentHashMap<String, List<InetAddress>>()
    
    override fun lookup(hostname: String): List<InetAddress> {
        // 记录设备信息，便于调试
        val deviceInfo = "Android ${Build.VERSION.RELEASE} (API ${Build.VERSION.SDK_INT}) - ${Build.MANUFACTURER} ${Build.MODEL}"
        Log.d(TAG, "设备信息: $deviceInfo")
        
        // 检查是否有硬编码的IP地址
        return when {
            hostIpMap.containsKey(hostname) -> {
                val hardcodedIp = hostIpMap[hostname]!!
                Log.d(TAG, "✅ 使用硬编码IP: $hostname -> $hardcodedIp")
                
                // 将字符串IP转换为InetAddress
                try {
                    listOf(InetAddress.getByName(hardcodedIp))
                } catch (e: UnknownHostException) {
                    Log.e(TAG, "❌ 硬编码IP地址无效: $hardcodedIp", e)
                    // 降级到系统DNS
                    Dns.SYSTEM.lookup(hostname)
                }
            }
            
            // 检查缓存
            dnsCache.containsKey(hostname) -> {
                Log.d(TAG, "✅ 使用缓存DNS结果: $hostname -> ${dnsCache[hostname]!!.first().hostAddress}")
                dnsCache[hostname]!!
            }
            
            // 使用系统DNS解析
            else -> try {
                val startTime = System.currentTimeMillis()
                val addresses = Dns.SYSTEM.lookup(hostname)
                val endTime = System.currentTimeMillis()
                
                // 缓存结果
                if (addresses.isNotEmpty()) {
                    dnsCache[hostname] = addresses
                    Log.d(TAG, "✅ DNS解析成功: ${addresses.first().hostAddress}, 耗时: ${endTime - startTime}ms")
                } else {
                    Log.w(TAG, "⚠️ DNS解析返回空结果")
                }
                
                addresses
            } catch (e: UnknownHostException) {
                Log.e(TAG, "❌ DNS解析失败: $hostname", e)
                
                // 检查是否有备用的IP地址策略
                if (hostname.startsWith("api.") || hostname.contains("api")) {
                    Log.d(TAG, "尝试使用备用IP地址策略")
                    // 这里可以添加备用解析策略
                }
                throw e
            }
        }
    }
    
    // 清除DNS缓存
    fun clearCache() {
        dnsCache.clear()
        Log.d(TAG, "DNS缓存已清除")
    }
    
    companion object {
        // 已知服务器域名列表
        private val SERVER_DOMAINS = arrayOf(
            "qcx.yuneyang.top" // 主服务器域名
        )

        /**
         * 获取服务器域名
         */
        fun getServerDomain(): String {
            return "qcx.yuneyang.top"
        }
        
        /**
         * 获取服务器域名
         * @return 服务器域名，如果无可用域名则返回空字符串
         */
        fun getServerUrl(): String {
            // 优先返回主域名
            return SERVER_DOMAINS.firstOrNull() ?: ""
        }
        
        /**
         * 检查设备是否运行Android 15或更高版本
         */
        fun isAndroid15OrAbove(): Boolean {
            return Build.VERSION.SDK_INT >= 35
        }
        
        /**
         * 获取特定域名解析错误的可能原因
         */
        fun getDnsErrorExplanation(): String {
            val explanation = StringBuilder()
            explanation.append("可能的DNS解析错误原因:\n")
            
            if (isAndroid15OrAbove()) {
                explanation.append("• Android 15引入了新的网络安全策略，可能导致DNS解析问题\n")
                explanation.append("• 可能需要在系统设置中允许应用程序访问网络\n")
                explanation.append("• 检查VPN或私有DNS设置是否干扰了网络连接\n")
            }
            
            explanation.append("• 网络连接问题: 请确保设备已连接到互联网\n")
            explanation.append("• DNS服务器可能不可用或响应慢\n")
            explanation.append("• 域名可能配置错误\n")
            
            return explanation.toString()
        }
        
        /**
         * 清除DNS缓存
         */
        fun clearDnsCache() {
            try {
                val dnsClass = Class.forName("java.net.InetAddress")
                val cacheField = dnsClass.getDeclaredField("addressCache")
                cacheField.isAccessible = true
                val cache = cacheField.get(null)
                
                val cacheClass = cache.javaClass
                val clearMethod = cacheClass.getDeclaredMethod("clear")
                clearMethod.isAccessible = true
                clearMethod.invoke(cache)
                
                Log.d("CustomDns", "✅ 成功清除系统DNS缓存")
            } catch (e: Exception) {
                Log.e("CustomDns", "❌ 清除DNS缓存失败: ${e.message}", e)
            }
        }
    }
}