# 构建错误修复说明

## 🔧 问题描述
构建失败，主要错误：
```
java.nio.file.NoSuchFileException: F:\5-AndroidProjects\AlHealthButler\app\libs\Msc.jar
File/directory does not exist: F:\5-AndroidProjects\AlHealthButler\app\libs\Msc.jar
```

## ✅ 修复内容

### 1. 注释掉缺失的JAR文件依赖
在 `app/build.gradle.kts` 中注释掉了以下依赖：

```kotlin
// 修复前：
implementation(files("libs/Msc.jar"))
implementation(fileTree(mapOf("dir" to "libs", "include" to listOf("*.jar"))))

// 修复后：
// implementation(files("libs/Msc.jar"))
// implementation(fileTree(mapOf("dir" to "libs", "include" to listOf("*.jar"))))
```

### 2. 修复原因
- `libs/Msc.jar` 文件不存在，但在构建配置中被引用
- `libs` 目录为空，但配置了通用JAR文件引用
- 讯飞语音SDK的实际调用代码都已被注释掉，使用模拟实现

## 🚀 后续步骤

### 选项1：继续使用模拟实现（推荐）
- 当前讯飞语音引擎使用模拟实现，功能正常
- 可以正常构建和运行应用
- 语音识别主要依赖Google语音服务

### 选项2：集成真实的讯飞SDK
如果需要真实的讯飞语音识别功能：

1. **注册讯飞开放平台**
   - 访问 https://www.xfyun.cn/
   - 注册账号并创建应用
   - 获取APPID

2. **下载SDK文件**
   - 下载Android SDK
   - 将 `Msc.jar` 放入 `app/libs/` 目录
   - 将so库文件放入 `app/src/main/jniLibs/` 目录

3. **恢复依赖配置**
   ```kotlin
   implementation(files("libs/Msc.jar"))
   ```

4. **配置代码**
   - 在 `XunfeiSpeechEngine.kt` 中替换APPID
   - 取消注释讯飞SDK相关代码

## 📋 验证修复
1. 清理项目：`./gradlew clean`
2. 构建项目：`./gradlew build`
3. 运行应用验证语音功能

## 🔍 相关文件
- `app/build.gradle.kts` - 构建配置
- `app/src/main/java/com/example/aihealthbutler/speech/XunfeiSpeechEngine.kt` - 讯飞引擎实现
- `SPEECH_ENGINES_SETUP.md` - 语音引擎集成指南
