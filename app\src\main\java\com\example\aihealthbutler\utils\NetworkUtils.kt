package com.example.aihealthbutler.utils

import android.content.Context
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.os.Build
import android.util.Log
import android.provider.Settings
import kotlinx.coroutines.withContext
import kotlinx.coroutines.Dispatchers
import java.net.InetSocketAddress
import java.net.Socket

/**
 * 网络工具类，用于检测网络连接状态
 */
object NetworkUtils {

    /**
     * 检查是否为模拟器环境
     */
    private fun isEmulator(): Boolean {
        return (Build.FINGERPRINT.startsWith("generic")
                || Build.FINGERPRINT.startsWith("unknown")
                || Build.MODEL.contains("google_sdk")
                || Build.MODEL.contains("Emulator")
                || Build.MODEL.contains("Android SDK built for x86")
                || Build.MANUFACTURER.contains("Genymotion")
                || (Build.BRAND.startsWith("generic") && Build.DEVICE.startsWith("generic"))
                || "google_sdk" == Build.PRODUCT)
    }

    /**
     * 检查是否有网络连接
     * 在模拟器环境中放宽检查条件
     */
    fun isNetworkAvailable(context: Context): Boolean {
        // 在模拟器中直接返回true，因为模拟器网络状态检测不准确
        if (isEmulator()) {
            Log.d("NetworkUtils", "检测到模拟器环境，默认认为网络可用")
            return true
        }
        
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val network = connectivityManager.activeNetwork ?: return false
            val capabilities = connectivityManager.getNetworkCapabilities(network) ?: return false
            
            // 简化网络能力检查，只要有网络能力即可
            capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
        } else {
            @Suppress("DEPRECATION")
            val networkInfo = connectivityManager.activeNetworkInfo
            networkInfo != null && networkInfo.isConnected
        }
    }
    
    /**
     * 获取当前网络类型名称
     */
    fun getNetworkTypeName(context: Context): String {
        // 如果是模拟器，直接返回模拟器网络
        if (isEmulator()) {
            return "模拟器网络"
        }
        
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val network = connectivityManager.activeNetwork ?: return "无网络连接"
            val capabilities = connectivityManager.getNetworkCapabilities(network) ?: return "无网络功能"
            
            when {
                capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) -> "WiFi"
                capabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) -> "蜂窝网络"
                capabilities.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET) -> "以太网"
                capabilities.hasTransport(NetworkCapabilities.TRANSPORT_BLUETOOTH) -> "蓝牙"
                capabilities.hasTransport(NetworkCapabilities.TRANSPORT_VPN) -> "VPN"
                else -> "未知网络类型"
            }
        } else {
            @Suppress("DEPRECATION")
            val networkInfo = connectivityManager.activeNetworkInfo
            when (networkInfo?.type) {
                ConnectivityManager.TYPE_WIFI -> "WiFi"
                ConnectivityManager.TYPE_MOBILE -> "蜂窝网络"
                ConnectivityManager.TYPE_ETHERNET -> "以太网"
                ConnectivityManager.TYPE_BLUETOOTH -> "蓝牙"
                ConnectivityManager.TYPE_VPN -> "VPN"
                else -> "未知网络类型"
            }
        }
    }

    /**
     * 测试服务器连接
     */
    suspend fun testServerConnection(
        host: String,
        port: Int = 80,
        timeoutMs: Int = 5000
    ): Boolean = withContext(Dispatchers.IO) {
        try {
            Socket().use { socket ->
                socket.connect(InetSocketAddress(host, port), timeoutMs)
                Log.d("NetworkUtils", "服务器连接测试成功: $host:$port")
                true
            }
        } catch (e: Exception) {
            Log.e("NetworkUtils", "服务器连接测试失败: $host:$port", e)
            false
        }
    }

    /**
     * 解析URL获取主机名和端口
     */
    fun parseHostAndPort(url: String): Pair<String, Int> {
        return try {
            val cleanUrl = url.replace("http://", "").replace("https://", "")
            val parts = cleanUrl.split(":")
            val host = parts[0].split("/")[0] // 移除路径部分
            val port = if (parts.size > 1) {
                parts[1].split("/")[0].toIntOrNull() ?: 80
            } else {
                if (url.startsWith("https://")) 443 else 80
            }
            Log.d("NetworkUtils", "解析URL: $url -> $host:$port")
            Pair(host, port)
        } catch (e: Exception) {
            Log.e("NetworkUtils", "解析URL失败: $url", e)
            Pair("", 80)
        }
    }

    /**
     * 检查Dify服务器连通性
     */
    suspend fun checkDifyServerConnection(baseUrl: String): Boolean {
        val (host, port) = parseHostAndPort(baseUrl)
        return if (host.isNotEmpty()) {
            testServerConnection(host, port)
        } else {
            false
        }
    }

    /**
     * 获取网络诊断信息
     */
    fun getNetworkDiagnostics(context: Context): String {
        val isAvailable = isNetworkAvailable(context)
        val networkType = getNetworkTypeName(context)
        val isEmulator = isEmulator()

        return buildString {
            appendLine("网络状态: ${if (isAvailable) "可用" else "不可用"}")
            appendLine("网络类型: $networkType")
            appendLine("运行环境: ${if (isEmulator) "模拟器" else "真机"}")
        }
    }
}