package com.example.aihealthbutler.utils

import android.content.Context
import android.content.SharedPreferences
import android.util.Log

/**
 * 图片缓存管理器
 * 用于保存和获取本地图片URI，解决服务器返回本地路径无法访问的问题
 */
object ImageCacheManager {
    private const val PREFS_NAME = "image_cache"
    private const val KEY_PREFIX = "folder_"
    
    /**
     * 保存图片URI到缓存
     * @param context 上下文
     * @param folderId 资料夹ID
     * @param imageUri 图片URI字符串
     */
    fun saveImageUri(context: Context, folderId: Int, imageUri: String) {
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        prefs.edit().putString("$KEY_PREFIX$folderId", imageUri).apply()
        Log.d("ImageCacheManager", "保存图片URI: folderId=$folderId, uri=$imageUri")
    }
    
    /**
     * 从缓存获取图片URI
     * @param context 上下文
     * @param folderId 资料夹ID
     * @return 缓存的图片URI，如果没有则返回null
     */
    fun getImageUri(context: Context, folderId: Int): String? {
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        val uri = prefs.getString("$KEY_PREFIX$folderId", null)
        Log.d("ImageCacheManager", "获取图片URI: folderId=$folderId, uri=$uri")
        return uri
    }
    
    /**
     * 从缓存删除图片URI
     * @param context 上下文
     * @param folderId 资料夹ID
     */
    fun removeImageUri(context: Context, folderId: Int) {
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        prefs.edit().remove("$KEY_PREFIX$folderId").apply()
        Log.d("ImageCacheManager", "删除图片URI: folderId=$folderId")
    }
}
