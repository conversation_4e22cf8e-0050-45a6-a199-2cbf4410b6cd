-- 创建数据库
CREATE DATABASE IF NOT EXISTS aie;
USE aie;

-- 创建user表
CREATE TABLE IF NOT EXISTS `user` (
    `account` VARCHAR(255) PRIMARY KEY,
    `password` VARCHAR(255) NOT NULL,
    `default_id` VARCHAR(255),
    `fixed_id` VARCHAR(255) UNIQUE
);

-- 创建family表
CREATE TABLE IF NOT EXISTS `family` (
    `account` VARCHAR(255) NOT NULL,
    `default_id` VARCHAR(255) PRIMARY KEY,
    `role` VARCHAR(255)
);

-- 创建person表
CREATE TABLE IF NOT EXISTS `person` (
    `default_id` VARCHAR(255) PRIMARY KEY,
    `name` VARCHAR(255),
    `sex` INT,
    `birthday` DATE,
    `height` INT,
    `weight` FLOAT,
    `BMI` FLOAT,
    `account` VARCHAR(255) NOT NULL,
    `role` VARCHAR(255),
    `consultants` INT,
    <PERSON>OR<PERSON><PERSON><PERSON> KEY (`default_id`) REFERENCES `family`(`default_id`),
    <PERSON>OR<PERSON><PERSON><PERSON> KEY (`account`) REFERENCES `user`(`account`)
);

-- 创建health表
CREATE TABLE IF NOT EXISTS `health` (
    `health_id` INT PRIMARY KEY AUTO_INCREMENT,
    `excondition` VARCHAR(255),
    `surgery` DATE,
    `allergic` VARCHAR(255),
    `vaccination` VARCHAR(255),
    `smoke` INT,
    `drink` INT,
    `family` VARCHAR(255),
    `period` VARCHAR(255),
    `pregnant` VARCHAR(255),
    `default_id` VARCHAR(255),
    FOREIGN KEY (`default_id`) REFERENCES `person`(`default_id`)
);

-- 创建mplan表
CREATE TABLE IF NOT EXISTS `mplan` (
    `mplan_id` INT PRIMARY KEY AUTO_INCREMENT,
    `pill_name` VARCHAR(255),
    `start_date` DATE,
    `duration` INT,
    `method` VARCHAR(255),
    `frequency` VARCHAR(255),
    `frequency_detail` INT,
    `first_time` DATETIME,
    `second_time` DATETIME,
    `third_time` DATETIME,
    `dosage` VARCHAR(255),
    `guide` VARCHAR(255),
    `note` VARCHAR(255),
    `default_id` VARCHAR(255),
    FOREIGN KEY (`default_id`) REFERENCES `person`(`default_id`)
);

-- 创建folder表
CREATE TABLE IF NOT EXISTS `folder` (
    `folder_id` INT PRIMARY KEY AUTO_INCREMENT,
    `picture` VARCHAR(255),
    `default_id` VARCHAR(255),
    FOREIGN KEY (`default_id`) REFERENCES `person`(`default_id`)
); 