package com.example.aihealthbutler

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Person
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.lifecycle.viewmodel.compose.viewModel
import com.example.aihealthbutler.api.AddMemberRequest
import com.example.aihealthbutler.api.Repository
import com.example.aihealthbutler.api.SessionManager
import com.example.aihealthbutler.qr.QRInviteData
import com.example.aihealthbutler.ui.theme.AIHealthButlerTheme
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.launch

/**
 * 添加成员确认页面
 */
class AddMemberConfirmActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        
        // 获取扫码数据
        val qrData = intent.getSerializableExtra("qr_data") as? QRInviteData
        
        if (qrData == null) {
            finish()
            return
        }
        
        setContent {
            AIHealthButlerTheme {
                val sessionManager = remember { SessionManager(applicationContext) }
                val repository = remember { Repository() }
                val viewModel = remember { AddMemberConfirmViewModel(repository, sessionManager) }
                
                AddMemberConfirmScreen(
                    qrData = qrData,
                    viewModel = viewModel,
                    onBack = { finish() },
                    onConfirm = { memberData ->
                        // 跳转到关系设置页面，不要立即finish
                        val intent = Intent(this@AddMemberConfirmActivity, RelationshipSettingActivity::class.java)
                        intent.putExtra("member_data", memberData)
                        intent.putExtra("from_qr_scan", true) // 标记来源
                        startActivityForResult(intent, 1001)
                    }
                )
            }
        }
    }

    @Deprecated("使用新的Activity Result API")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == 1001) {
            if (resultCode == Activity.RESULT_OK) {
                // 关系设置成功，返回主页面
                Toast.makeText(this, "添加家庭成员成功", Toast.LENGTH_SHORT).show()
                val intent = Intent(this, MainActivity::class.java)
                intent.flags = Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_NEW_TASK
                startActivity(intent)
            }
            finish()
        }
    }
}

/**
 * 添加成员确认ViewModel
 */
class AddMemberConfirmViewModel(
    private val repository: Repository,
    private val sessionManager: SessionManager
) : ViewModel() {
    private val TAG = "AddMemberConfirmVM"
    
    private val _uiState = MutableStateFlow(AddMemberConfirmUiState())
    val uiState: StateFlow<AddMemberConfirmUiState> = _uiState
    
    /**
     * 添加家庭成员
     */
    fun addFamilyMember(qrData: QRInviteData) {
        viewModelScope.launch {
            try {
                Log.d(TAG, "开始添加家庭成员: ${qrData.account} (${qrData.name})")
                _uiState.value = _uiState.value.copy(isLoading = true, error = null)

                val token = sessionManager.token.firstOrNull() ?: throw Exception("未登录")
                val currentAccount = sessionManager.account.value ?: throw Exception("未获取到当前账号")

                Log.d(TAG, "当前账号: $currentAccount, 要添加的账号: ${qrData.account}")

                // 检查是否添加自己
                if (qrData.account == currentAccount) {
                    Log.w(TAG, "尝试添加自己为家庭成员")
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = "不能添加自己为家庭成员"
                    )
                    return@launch
                }

                // 检查是否已经是家庭成员
                Log.d(TAG, "检查是否已经是家庭成员")
                val familyMembers = repository.getAllFamilyMembers(token, currentAccount)
                familyMembers.fold(
                    onSuccess = { response ->
                        Log.d(TAG, "当前家庭成员数量: ${response.data.size}")
                        val existingMember = response.data.find { it.account == qrData.account }
                        if (existingMember != null) {
                            Log.w(TAG, "用户已经是家庭成员: ${existingMember.name}")
                            _uiState.value = _uiState.value.copy(
                                isLoading = false,
                                error = "该用户已经是您的家庭成员"
                            )
                            return@launch
                        }
                        Log.d(TAG, "用户不在家庭成员列表中，可以添加")
                    },
                    onFailure = { e ->
                        Log.w(TAG, "检查家庭成员失败，继续添加流程: ${e.message}")
                    }
                )

                // 调用添加成员API - 注意：这里应该传入当前账号，而不是扫码的账号
                Log.d(TAG, "调用添加成员API")
                val addRequest = AddMemberRequest(account = currentAccount)
                Log.d(TAG, "添加成员请求: $addRequest (在账号 $currentAccount 下创建新成员)")
                val result = repository.addMember(token, addRequest)
                
                result.fold(
                    onSuccess = { response ->
                        Log.d(TAG, "添加成员API响应: code=${response.code}, message=${response.message}")
                        Log.d(TAG, "添加成员API数据: ${response.data}")
                        if (response.code == 200 && response.data.default_id != null) {
                            Log.d(TAG, "创建新成员成功，defaultId: ${response.data.default_id}")

                            // 创建成员数据，包含扫码用户的信息
                            val memberData = AddedMemberData(
                                defaultId = response.data.default_id!!,
                                account = qrData.account,
                                name = qrData.name,
                                originalUserId = qrData.userId
                            )

                            Log.d(TAG, "准备设置成员信息: ${memberData.name} (来自扫码用户: ${qrData.account})")

                            _uiState.value = _uiState.value.copy(
                                isLoading = false,
                                addedMember = memberData,
                                error = null
                            )
                        } else {
                            Log.e(TAG, "创建新成员失败: ${response.message}")
                            _uiState.value = _uiState.value.copy(
                                isLoading = false,
                                error = response.message ?: "添加成员失败"
                            )
                        }
                    },
                    onFailure = { e ->
                        Log.e(TAG, "添加成员API调用失败", e)
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            error = "添加成员失败: ${e.message}"
                        )
                    }
                )
            } catch (e: Exception) {
                Log.e(TAG, "添加家庭成员失败", e)
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "添加成员失败: ${e.message}"
                )
            }
        }
    }
}

/**
 * UI状态
 */
data class AddMemberConfirmUiState(
    val isLoading: Boolean = false,
    val addedMember: AddedMemberData? = null,
    val error: String? = null
)

/**
 * 添加的成员数据
 */
data class AddedMemberData(
    val defaultId: String,
    val account: String,
    val name: String,
    val originalUserId: String
) : java.io.Serializable

/**
 * 添加成员确认界面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AddMemberConfirmScreen(
    qrData: QRInviteData,
    viewModel: AddMemberConfirmViewModel,
    onBack: () -> Unit,
    onConfirm: (AddedMemberData) -> Unit
) {
    val uiState by viewModel.uiState.collectAsState()
    val context = LocalContext.current
    
    Scaffold(
        topBar = {
            CenterAlignedTopAppBar(
                title = {
                    Text(
                        "确认添加成员",
                        fontSize = 20.sp,
                        fontWeight = FontWeight.Bold
                    )
                },
                navigationIcon = {
                    IconButton(onClick = onBack) {
                        Icon(
                            painter = painterResource(id = R.drawable.left_arrow),
                            contentDescription = "返回",
                            modifier = Modifier.size(24.dp)
                        )
                    }
                },
                colors = TopAppBarDefaults.centerAlignedTopAppBarColors(
                    containerColor = Color.White,
                    titleContentColor = Color.Black,
                    navigationIconContentColor = Color.Black
                )
            )
        },
        containerColor = Color.White
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .background(Color.White)
                .padding(paddingValues)
                .verticalScroll(rememberScrollState())
                .padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // 用户信息预览
            UserInfoPreview(qrData = qrData)
            
            Spacer(modifier = Modifier.height(24.dp))
            
            // 确认提示
            ConfirmationPrompt()
            
            Spacer(modifier = Modifier.height(32.dp))
            
            // 操作按钮
            ActionButtons(
                isLoading = uiState.isLoading,
                onCancel = onBack,
                onConfirm = { viewModel.addFamilyMember(qrData) }
            )
            
            // 错误提示
            uiState.error?.let { error ->
                Spacer(modifier = Modifier.height(16.dp))
                Card(
                    colors = CardDefaults.cardColors(containerColor = Color(0xFFFFEBEE)),
                    shape = RoundedCornerShape(8.dp)
                ) {
                    Text(
                        text = error,
                        color = Color.Red,
                        modifier = Modifier.padding(16.dp)
                    )
                }
            }
        }
    }
    
    // 处理添加成功
    LaunchedEffect(uiState.addedMember) {
        uiState.addedMember?.let { memberData ->
            onConfirm(memberData)
        }
    }
}

/**
 * 用户信息预览
 */
@Composable
private fun UserInfoPreview(qrData: QRInviteData) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = Color(0xFFF8F9FA)),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(20.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // 头像占位符
            Card(
                modifier = Modifier.size(80.dp),
                colors = CardDefaults.cardColors(containerColor = Color(0xFF22C1C3)),
                shape = RoundedCornerShape(40.dp)
            ) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = Icons.Default.Person,
                        contentDescription = "用户头像",
                        tint = Color.White,
                        modifier = Modifier.size(40.dp)
                    )
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            Text(
                text = qrData.name,
                fontSize = 20.sp,
                fontWeight = FontWeight.Bold,
                color = Color.Black
            )

            Spacer(modifier = Modifier.height(8.dp))

            Text(
                text = "账号: ${qrData.account}",
                fontSize = 14.sp,
                color = Color.Gray
            )

            Spacer(modifier = Modifier.height(4.dp))

            Text(
                text = "用户ID: ${qrData.userId}",
                fontSize = 12.sp,
                color = Color.Gray
            )
        }
    }
}

/**
 * 确认提示
 */
@Composable
private fun ConfirmationPrompt() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = Color(0xFFF0F8FF)),
        elevation = CardDefaults.cardElevation(defaultElevation = 1.dp),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "确认添加此用户为家庭成员？",
                fontSize = 16.sp,
                fontWeight = FontWeight.Bold,
                color = Color(0xFF22C1C3)
            )
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = "添加后，您可以为该成员设置家庭关系，并管理其健康信息。",
                fontSize = 14.sp,
                color = Color(0xFF424242)
            )
        }
    }
}

/**
 * 操作按钮
 */
@Composable
private fun ActionButtons(
    isLoading: Boolean,
    onCancel: () -> Unit,
    onConfirm: () -> Unit
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceEvenly
    ) {
        OutlinedButton(
            onClick = onCancel,
            modifier = Modifier.weight(1f),
            enabled = !isLoading,
            colors = ButtonDefaults.outlinedButtonColors(
                contentColor = Color.Gray
            )
        ) {
            Text("取消")
        }

        Spacer(modifier = Modifier.width(16.dp))

        Button(
            onClick = onConfirm,
            modifier = Modifier.weight(1f),
            enabled = !isLoading,
            colors = ButtonDefaults.buttonColors(
                containerColor = Color(0xFF22C1C3)
            )
        ) {
            if (isLoading) {
                CircularProgressIndicator(
                    modifier = Modifier.size(16.dp),
                    color = Color.White,
                    strokeWidth = 2.dp
                )
                Spacer(modifier = Modifier.width(8.dp))
            }
            Text("确认添加")
        }
    }
}
