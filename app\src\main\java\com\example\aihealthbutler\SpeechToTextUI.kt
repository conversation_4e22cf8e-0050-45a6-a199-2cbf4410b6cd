package com.example.aihealthbutler

import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import kotlinx.coroutines.delay
import com.example.aihealthbutler.speech.SpeechToTextState
import com.example.aihealthbutler.speech.SpeechToTextResult

/**
 * 语音转文字结果显示组件
 */
@Composable
fun SpeechToTextResultCard(
    result: SpeechToTextResult,
    state: SpeechToTextState,
    errorMessage: String,
    onDismiss: () -> Unit = {},
    onSendMessage: (String) -> Unit = {},
    modifier: Modifier = Modifier,
    speechToTextManager: SpeechToTextManager? = null
) {
    var showInstallGuideDialog by remember { mutableStateOf(false) }
    var showEngineSelectionDialog by remember { mutableStateOf(false) }
    Card(
        modifier = modifier
            .fillMaxWidth()
            .padding(16.dp),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = 8.dp
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(20.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // 标题
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "语音转文字",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF333333)
                )
                
                TextButton(onClick = onDismiss) {
                    Text(
                        text = "关闭",
                        color = Color(0xFF666666),
                        fontSize = 14.sp
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 状态显示
            when (state) {
                SpeechToTextState.INITIALIZING -> {
                    ProcessingIndicator()
                    Spacer(modifier = Modifier.height(12.dp))
                    Text(
                        text = "正在初始化...",
                        color = Color(0xFF007AFF),
                        fontSize = 14.sp
                    )
                }

                SpeechToTextState.LISTENING -> {
                    ListeningIndicator()
                    Spacer(modifier = Modifier.height(12.dp))
                    Text(
                        text = "正在监听...",
                        color = Color(0xFF007AFF),
                        fontSize = 14.sp
                    )
                }

                SpeechToTextState.PROCESSING -> {
                    ProcessingIndicator()
                    Spacer(modifier = Modifier.height(12.dp))
                    Text(
                        text = "正在处理...",
                        color = Color(0xFF007AFF),
                        fontSize = 14.sp
                    )
                }

                SpeechToTextState.SUCCESS -> {
                    CompletedIndicator()
                    Spacer(modifier = Modifier.height(12.dp))
                    Text(
                        text = "转换完成",
                        color = Color(0xFF34C759),
                        fontSize = 14.sp
                    )
                }

                SpeechToTextState.ERROR -> {
                    ErrorIndicator()
                    Spacer(modifier = Modifier.height(12.dp))
                    Text(
                        text = "转换失败",
                        color = Color(0xFFFF3B30),
                        fontSize = 14.sp
                    )
                }

                SpeechToTextState.IDLE -> {
                    // 空闲状态不显示指示器
                }
            }
            
            // 结果文本显示
            if (result.text.isNotEmpty()) {
                Spacer(modifier = Modifier.height(16.dp))
                
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    shape = RoundedCornerShape(12.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = Color(0xFFF8F9FA)
                    )
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp)
                    ) {
                        Text(
                            text = "识别结果:",
                            fontSize = 12.sp,
                            color = Color(0xFF666666),
                            fontWeight = FontWeight.Medium
                        )
                        
                        Spacer(modifier = Modifier.height(8.dp))
                        
                        Text(
                            text = result.text,
                            fontSize = 16.sp,
                            color = Color(0xFF333333),
                            lineHeight = 24.sp
                        )
                        
                        if (result.confidence > 0f) {
                            Spacer(modifier = Modifier.height(8.dp))
                            Text(
                                text = "置信度: ${(result.confidence * 100).toInt()}%",
                                fontSize = 12.sp,
                                color = Color(0xFF999999)
                            )
                        }
                        
                        if (result.isPartial) {
                            Spacer(modifier = Modifier.height(4.dp))
                            Text(
                                text = "实时识别中...",
                                fontSize = 12.sp,
                                color = Color(0xFF007AFF),
                                fontStyle = androidx.compose.ui.text.font.FontStyle.Italic
                            )
                        }
                    }
                }

                // 发送按钮（仅在转换完成且有文本时显示）
                if (state == SpeechToTextState.SUCCESS && result.text.isNotEmpty() && !result.isPartial) {
                    Spacer(modifier = Modifier.height(16.dp))

                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(12.dp)
                    ) {
                        // 取消按钮
                        OutlinedButton(
                            onClick = onDismiss,
                            modifier = Modifier.weight(1f),
                            colors = ButtonDefaults.outlinedButtonColors(
                                contentColor = Color(0xFFC75D5D)
                            )
                        ) {
                            Text("取消")
                        }

                        // 发送按钮
                        Button(
                            onClick = { onSendMessage(result.text) },
                            modifier = Modifier.weight(1f),
                            colors = ButtonDefaults.buttonColors(
                                containerColor = Color(0xFF74B5AF)
                            )
                        ) {
                            Text("发送", color = Color.White)
                        }
                    }
                }
            }
            
            // 错误信息显示
            if (errorMessage.isNotEmpty() && state == SpeechToTextState.ERROR) {
                Spacer(modifier = Modifier.height(16.dp))

                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .heightIn(max = 300.dp), // 限制最大高度
                    shape = RoundedCornerShape(12.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = Color(0xFFFFEBEE)
                    )
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        // 错误图标
                        Text(
                            text = "❌",
                            fontSize = 32.sp,
                            modifier = Modifier.padding(bottom = 8.dp)
                        )

                        // 错误标题
                        Text(
                            text = "转换失败",
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Bold,
                            color = Color(0xFFD32F2F),
                            modifier = Modifier.padding(bottom = 8.dp)
                        )

                        // 错误详情（限制行数）
                        Text(
                            text = errorMessage,
                            fontSize = 13.sp,
                            color = Color(0xFFD32F2F),
                            textAlign = TextAlign.Center,
                            lineHeight = 18.sp,
                            maxLines = 6, // 限制最大行数
                            modifier = Modifier.padding(bottom = 12.dp)
                        )


                    }
                }

                        // 操作按钮
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.spacedBy(6.dp)
                        ) {
                            // 安装指导按钮（针对设备不支持的情况）
                            if (errorMessage.contains("设备不支持语音识别功能")) {
                                Button(
                                    onClick = {
                                        showInstallGuideDialog = true
                                    },
                                    modifier = Modifier.weight(1f),
                                    colors = ButtonDefaults.buttonColors(
                                        containerColor = Color(0xFF4CAF50)
                                    ),
                                    shape = RoundedCornerShape(8.dp),
                                    contentPadding = PaddingValues(vertical = 6.dp)
                                ) {
                                    Text(
                                        text = "安装指导",
                                        color = Color.White,
                                        fontSize = 12.sp
                                    )
                                }
                            }

                            // 切换引擎按钮
                            Button(
                                onClick = {
                                    showEngineSelectionDialog = true
                                },
                                modifier = Modifier.weight(1f),
                                colors = ButtonDefaults.buttonColors(
                                    containerColor = Color(0xFFFF9800)
                                ),
                                shape = RoundedCornerShape(8.dp),
                                contentPadding = PaddingValues(vertical = 6.dp)
                            ) {
                                Text(
                                    text = "切换引擎",
                                    color = Color.White,
                                    fontSize = 12.sp
                                )
                            }

                            // 重试按钮
                            Button(
                                onClick = {
                                    // 强制重新初始化
                                    speechToTextManager?.forceReinitialize()
                                    onDismiss()
                                },
                                modifier = Modifier.weight(1f),
                                colors = ButtonDefaults.buttonColors(
                                    containerColor = Color(0xFF2196F3)
                                ),
                                shape = RoundedCornerShape(8.dp),
                                contentPadding = PaddingValues(vertical = 6.dp)
                            ) {
                                Text(
                                    text = "重试",
                                    color = Color.White,
                                    fontSize = 12.sp
                                )
                            }
                        }
            }
        }
    }

    // 安装指导对话框
    if (showInstallGuideDialog && speechToTextManager != null) {
        InstallGuideDialog(
            speechToTextManager = speechToTextManager,
            onDismiss = { showInstallGuideDialog = false }
        )
    }

    // 引擎选择对话框
    if (showEngineSelectionDialog && speechToTextManager != null) {
        SpeechEngineSelectionDialog(
            speechToTextManager = speechToTextManager,
            onDismiss = { showEngineSelectionDialog = false }
        )
    }
}

/**
 * 安装指导对话框
 */
@Composable
fun InstallGuideDialog(
    speechToTextManager: SpeechToTextManager?,
    onDismiss: () -> Unit
) {
    if (speechToTextManager == null) {
        onDismiss()
        return
    }

    val installGuide = speechToTextManager.getGoogleServiceInstallGuide()
    val serviceStatus = speechToTextManager.checkGoogleSpeechService()

    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = "语音服务设置指南",
                fontWeight = FontWeight.Bold,
                color = Color(0xFF2196F3)
            )
        },
        text = {
            LazyColumn(
                modifier = Modifier
                    .fillMaxWidth()
                    .heightIn(max = 400.dp),
                verticalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                // 设备信息
                item {
                    Card(
                        modifier = Modifier.fillMaxWidth(),
                        colors = CardDefaults.cardColors(
                            containerColor = Color(0xFFF5F5F5)
                        )
                    ) {
                        Column(
                            modifier = Modifier.padding(12.dp)
                        ) {
                            Text(
                                text = "📱 设备信息",
                                fontWeight = FontWeight.Bold,
                                fontSize = 14.sp,
                                color = Color(0xFF666666)
                            )
                            Spacer(modifier = Modifier.height(4.dp))
                            Text(
                                text = "${android.os.Build.MANUFACTURER} ${android.os.Build.MODEL}\nAndroid ${android.os.Build.VERSION.RELEASE}",
                                fontSize = 12.sp,
                                color = Color(0xFF888888)
                            )
                        }
                    }
                }

                // 服务状态
                item {
                    Card(
                        modifier = Modifier.fillMaxWidth(),
                        colors = CardDefaults.cardColors(
                            containerColor = Color(0xFFFFF3E0)
                        )
                    ) {
                        Column(
                            modifier = Modifier.padding(12.dp)
                        ) {
                            Text(
                                text = "🔍 服务检测结果",
                                fontWeight = FontWeight.Bold,
                                fontSize = 14.sp,
                                color = Color(0xFFFF8F00)
                            )
                            Spacer(modifier = Modifier.height(4.dp))
                            Text(
                                text = serviceStatus,
                                fontSize = 12.sp,
                                color = Color(0xFFE65100),
                                lineHeight = 16.sp
                            )
                        }
                    }
                }

                // 安装指导
                item {
                    Card(
                        modifier = Modifier.fillMaxWidth(),
                        colors = CardDefaults.cardColors(
                            containerColor = Color(0xFFE8F5E8)
                        )
                    ) {
                        Column(
                            modifier = Modifier.padding(12.dp)
                        ) {
                            Text(
                                text = "🛠️ 解决方案",
                                fontWeight = FontWeight.Bold,
                                fontSize = 14.sp,
                                color = Color(0xFF4CAF50)
                            )
                            Spacer(modifier = Modifier.height(4.dp))
                            Text(
                                text = installGuide,
                                fontSize = 12.sp,
                                color = Color(0xFF2E7D32),
                                lineHeight = 16.sp
                            )
                        }
                    }
                }
            }
        },
        confirmButton = {
            Button(
                onClick = onDismiss,
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color(0xFF74B5AF)
                )
            ) {
                Text("知道了", color = Color.White)
            }
        },
        dismissButton = {
            TextButton(
                onClick = {
                    // 尝试打开系统设置
                    try {
                        val intent = android.content.Intent(android.provider.Settings.ACTION_APPLICATION_SETTINGS)
                        intent.addFlags(android.content.Intent.FLAG_ACTIVITY_NEW_TASK)
                        speechToTextManager.context.startActivity(intent)
                    } catch (e: Exception) {
                        android.util.Log.w("InstallGuide", "Failed to open settings", e)
                    }
                }
            ) {
                Text("打开设置", color = Color(0xFF2196F3))
            }
        }
    )
}

/**
 * 监听状态指示器
 */
@Composable
private fun ListeningIndicator() {
    val infiniteTransition = rememberInfiniteTransition(label = "listening")
    val scale by infiniteTransition.animateFloat(
        initialValue = 0.8f,
        targetValue = 1.2f,
        animationSpec = infiniteRepeatable(
            animation = tween(1000, easing = EaseInOutSine),
            repeatMode = RepeatMode.Reverse
        ),
        label = "scale"
    )
    
    Box(
        modifier = Modifier
            .size(60.dp)
            .clip(RoundedCornerShape(30.dp))
            .background(Color(0xFF007AFF).copy(alpha = 0.1f))
            .border(2.dp, Color(0xFF007AFF), RoundedCornerShape(30.dp)),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = "🎤",
            fontSize = (24 * scale).sp
        )
    }
}

/**
 * 处理状态指示器
 */
@Composable
private fun ProcessingIndicator() {
    val infiniteTransition = rememberInfiniteTransition(label = "processing")
    val rotation by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 360f,
        animationSpec = infiniteRepeatable(
            animation = tween(1500, easing = LinearEasing),
            repeatMode = RepeatMode.Restart
        ),
        label = "rotation"
    )
    
    Box(
        modifier = Modifier
            .size(60.dp)
            .clip(RoundedCornerShape(30.dp))
            .background(Color(0xFF007AFF).copy(alpha = 0.1f)),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = "⚙️",
            fontSize = 24.sp,
            modifier = Modifier.graphicsLayer {
                rotationZ = rotation
            }
        )
    }
}

/**
 * 完成状态指示器
 */
@Composable
private fun CompletedIndicator() {
    var showCheckmark by remember { mutableStateOf(false) }
    
    LaunchedEffect(Unit) {
        delay(100)
        showCheckmark = true
    }
    
    val scale by animateFloatAsState(
        targetValue = if (showCheckmark) 1f else 0f,
        animationSpec = spring(
            dampingRatio = Spring.DampingRatioMediumBouncy,
            stiffness = Spring.StiffnessLow
        ),
        label = "checkmark_scale"
    )
    
    Box(
        modifier = Modifier
            .size(60.dp)
            .clip(RoundedCornerShape(30.dp))
            .background(Color(0xFF34C759).copy(alpha = 0.1f))
            .border(2.dp, Color(0xFF34C759), RoundedCornerShape(30.dp)),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = "✓",
            fontSize = (24 * scale).sp,
            color = Color(0xFF34C759),
            fontWeight = FontWeight.Bold
        )
    }
}

/**
 * 错误状态指示器
 */
@Composable
private fun ErrorIndicator() {
    Box(
        modifier = Modifier
            .size(60.dp)
            .clip(RoundedCornerShape(30.dp))
            .background(Color(0xFFFF3B30).copy(alpha = 0.1f))
            .border(2.dp, Color(0xFFFF3B30), RoundedCornerShape(30.dp)),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = "✕",
            fontSize = 24.sp,
            color = Color(0xFFFF3B30),
            fontWeight = FontWeight.Bold
        )
    }
}

/**
 * 简化的语音转文字状态指示器（用于覆盖层）
 */
@Composable
fun SpeechToTextOverlayIndicator(
    state: SpeechToTextState,
    result: SpeechToTextResult,
    modifier: Modifier = Modifier
) {
    if (state != SpeechToTextState.IDLE) {
        Card(
            modifier = modifier,
            shape = RoundedCornerShape(20.dp),
            colors = CardDefaults.cardColors(
                containerColor = Color.White.copy(alpha = 0.95f)
            ),
            elevation = CardDefaults.cardElevation(
                defaultElevation = 8.dp
            )
        ) {
            Row(
                modifier = Modifier.padding(16.dp),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                // 状态图标
                when (state) {
                    SpeechToTextState.INITIALIZING -> {
                        Text(text = "⚙️", fontSize = 20.sp)
                        Text(
                            text = "正在初始化...",
                            fontSize = 14.sp,
                            color = Color(0xFF5C93D1)
                        )
                    }
                    SpeechToTextState.LISTENING -> {
                        Text(text = "🎤", fontSize = 20.sp)
                        Text(
                            text = "正在监听...",
                            fontSize = 14.sp,
                            color = Color(0xFF5C93D1)
                        )
                    }
                    SpeechToTextState.PROCESSING -> {
                        Text(text = "⚙️", fontSize = 20.sp)
                        Text(
                            text = "正在转换...",
                            fontSize = 14.sp,
                            color = Color(0xFF5C93D1)
                        )
                    }
                    SpeechToTextState.SUCCESS -> {
                        Text(text = "✓", fontSize = 20.sp, color = Color(0xFF74B5AF))
                        Text(
                            text = "转换完成",
                            fontSize = 14.sp,
                            color = Color(0xFF74B5AF)
                        )
                    }
                    SpeechToTextState.ERROR -> {
                        Text(text = "✕", fontSize = 20.sp, color = Color(0xFFC75D5D))
                        Text(
                            text = "转换失败",
                            fontSize = 14.sp,
                            color = Color(0xFFC75D5D)
                        )
                    }
                    else -> {}
                }
            }
        }
    }
}
