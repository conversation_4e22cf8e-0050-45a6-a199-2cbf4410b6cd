package com.example.aihealthbutler

import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.text.font.FontWeight
import com.example.aihealthbutler.ui.theme.AIHealthButlerTheme
import androidx.compose.runtime.*
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Locale
import java.util.Date
import androidx.compose.foundation.clickable
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Text
import androidx.compose.ui.draw.clip
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.ui.platform.LocalContext
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.aihealthbutler.api.Repository
import com.example.aihealthbutler.api.SessionManager
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.launch
import android.util.Log
import android.widget.Toast
import com.example.aihealthbutler.api.MedicationPlanRequest
import kotlin.fold

// 药品计划ViewModel
class MedicationPlanViewModel(
    private val repository: Repository,
    private val sessionManager: SessionManager
) : ViewModel() {
    
    private val TAG = "MedicationPlanVM"
    
    private val _savingState = MutableStateFlow<SavingState>(SavingState.Idle)
    val savingState: StateFlow<SavingState> = _savingState
    
    // 时间格式化函数，确保HH:MM格式（只保留一个）
    private fun formatTime(time: String): String {
        if (time.isBlank()) return ""
        val parts = time.split(":")
        if (parts.size != 2) return time
        
        val hour = parts[0].toIntOrNull() ?: return time
        val minute = parts[1].toIntOrNull() ?: return time
        
        return String.format("%02d:%02d", hour, minute)
    }
    
    // 保存用药计划
    fun saveMedicationPlan(
        pillName: String,
        startDate: String,
        duration: String,
        method: String,
        frequency: String,
        frequencyDetail: Int,
        firstTime: String,
        secondTime: String,
        thirdTime: String,
        dosage: String,
        guide: String,
        note: String
    ) {
        // 基本输入验证
        if (pillName.isBlank()) {
            _savingState.value = SavingState.Error("请输入药品名称")
            return
        }

        if (duration.isBlank() || duration.toIntOrNull() == null || duration.toIntOrNull()!! <= 0) {
            _savingState.value = SavingState.Error("请输入有效的持续时间（天数）")
            return
        }

        if (method.isBlank()) {
            _savingState.value = SavingState.Error("请输入用药方法")
            return
        }

        if (frequency.isBlank()) {
            _savingState.value = SavingState.Error("请输入用药频次")
            return
        }

        if (dosage.isBlank()) {
            _savingState.value = SavingState.Error("请输入用药剂量")
            return
        }

        viewModelScope.launch {
            try {
                _savingState.value = SavingState.Loading

                val token = sessionManager.token.firstOrNull() ?: throw Exception("未登录")
                val defaultId = sessionManager.defaultId.firstOrNull() ?: throw Exception("未设置默认成员")

                // 添加详细日志
                Log.d(TAG, "开始创建用药计划: pillName=$pillName, defaultId=$defaultId")

                // 构造时间字符串，确保格式正确
                val firstTimeFormatted = if (firstTime.isNotBlank()) {
                    "${startDate}T${formatTime(firstTime)}:00"
                } else {
                    "${startDate}T08:00:00"
                }

                val secondTimeFormatted = if (secondTime.isNotBlank()) {
                     "${startDate}T${formatTime(secondTime)}:00"
                } else {
                    "${startDate}T12:00:00"
                }

                val thirdTimeFormatted = if (thirdTime.isNotBlank()) {
                     "${startDate}T${formatTime(thirdTime)}:00"
                } else {
                    "" // 改为空字符串而不是null
                }

                

                // 构造请求对象
                val request = MedicationPlanRequest(
                    pillName = pillName,
                    startDate = startDate,
                    duration = duration.toInt(),
                    method = method,
                    frequency = frequency,
                    frequencyDetail = frequencyDetail,
                    firstTime = firstTimeFormatted,
                    secondTime = secondTimeFormatted,
                    thirdTime = thirdTimeFormatted,
                    dosage = dosage,
                    guide = guide.takeIf { it.isNotBlank() },
                    note = note.takeIf { it.isNotBlank() },
                    defaultId = defaultId
                )

                Log.d(TAG, "发送请求: $request")
                Log.d(TAG, "时间格式 - firstTime: $firstTimeFormatted, secondTime: $secondTimeFormatted, thirdTime: $thirdTimeFormatted")

                val result = repository.createMedicationPlan(token, request)

                result.fold(
                    onSuccess = { response ->
                        Log.d(TAG, "创建成功: $response")
                        if (response.code == 200 && response.data != null) {
                            _savingState.value = SavingState.Success("用药计划添加成功")
                        } else {
                            _savingState.value = SavingState.Error(response.message ?: "添加失败")
                        }
                    },
                    onFailure = { e ->
                        Log.e(TAG, "创建失败: ${e.message}", e)
                        val errorMessage = when {
                            e.message?.contains("500") == true -> "服务器内部错误，请稍后重试"
                            e.message?.contains("401") == true -> "登录已过期，请重新登录"
                            e.message?.contains("400") == true -> "请求参数错误，请检查输入"
                            else -> e.message ?: "添加失败"
                        }
                        _savingState.value = SavingState.Error(errorMessage)
                    }
                )
            } catch (e: Exception) {
                Log.e(TAG, "保存用药计划异常", e)
                _savingState.value = SavingState.Error("保存失败: ${e.message}")
            }
        }
    }

    // 更新用药计划
    fun updateMedicationPlan(
        mplanId: Int,
        pillName: String,
        startDate: String,
        duration: String,
        method: String,
        frequency: String,
        frequencyDetail: Int,
        firstTime: String,
        secondTime: String,
        thirdTime: String,
        dosage: String,
        guide: String,
        note: String
    ) {
        // 基本输入验证
        if (pillName.isBlank()) {
            _savingState.value = SavingState.Error("请输入药品名称")
            return
        }

        if (duration.isBlank() || duration.toIntOrNull() == null || duration.toIntOrNull()!! <= 0) {
            _savingState.value = SavingState.Error("请输入有效的持续时间（天数）")
            return
        }

        if (method.isBlank()) {
            _savingState.value = SavingState.Error("请输入用药方法")
            return
        }

        if (frequency.isBlank()) {
            _savingState.value = SavingState.Error("请输入用药频次")
            return
        }

        if (dosage.isBlank()) {
            _savingState.value = SavingState.Error("请输入用药剂量")
            return
        }

        viewModelScope.launch {
            try {
                _savingState.value = SavingState.Loading

                val token = sessionManager.token.firstOrNull() ?: throw Exception("未登录")
                val defaultId = sessionManager.defaultId.firstOrNull() ?: throw Exception("未设置默认成员")

                // 格式化时间为ISO 8601格式
                val firstTimeFormatted = if (firstTime.isNotBlank()) {
                    "${startDate}T${firstTime}:00"
                } else {
                    "${startDate}T08:00:00"
                }

                val secondTimeFormatted = if (secondTime.isNotBlank()) {
                    "${startDate}T${secondTime}:00"
                } else {
                    "${startDate}T12:00:00"
                }

                val thirdTimeFormatted = if (thirdTime.isNotBlank()) {
                    "${startDate}T${thirdTime}:00"
                } else null

                // 构造请求对象
                val request = MedicationPlanRequest(
                    pillName = pillName,
                    startDate = startDate,
                    duration = duration.toInt(),
                    method = method,
                    frequency = frequency,
                    frequencyDetail = frequencyDetail,
                    firstTime = firstTimeFormatted,
                    secondTime = secondTimeFormatted,
                    thirdTime = thirdTimeFormatted,
                    dosage = dosage,
                    guide = guide.takeIf { it.isNotBlank() },
                    note = note.takeIf { it.isNotBlank() },
                    defaultId = defaultId
                )

                // 更新用药计划
                val result = repository.updateMedicationPlan(token, mplanId, request)

                result.fold(
                    onSuccess = { medicationPlan ->
                        Log.d(TAG, "更新成功: $medicationPlan")
                        _savingState.value = SavingState.Success("用药计划更新成功")
                    },
                    onFailure = { e ->
                        _savingState.value = SavingState.Error(e.message ?: "更新失败")
                        Log.e(TAG, "更新用药计划失败", e)
                    }
                )
            } catch (e: Exception) {
                Log.e(TAG, "更新用药计划失败", e)
                _savingState.value = SavingState.Error(e.message ?: "更新失败")
            }
        }
    }

    // 保存状态
    sealed class SavingState {
        object Idle : SavingState()
        object Loading : SavingState()
        data class Success(val message: String) : SavingState()
        data class Error(val message: String) : SavingState()
    }
}

class AddnewmedicationplanActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()

        // 获取传递的药品计划数据
        val isEdit = intent.getBooleanExtra("isEdit", false)
        val planId = intent.getIntExtra("planId", -1)
        val pillName = intent.getStringExtra("pillName") ?: ""
        val startDate = intent.getStringExtra("startDate") ?: ""
        val duration = intent.getIntExtra("duration", 0)
        val method = intent.getStringExtra("method") ?: ""
        val frequency = intent.getStringExtra("frequency") ?: ""
        val dosage = intent.getStringExtra("dosage") ?: ""
        val guide = intent.getStringExtra("guide") ?: ""
        val note = intent.getStringExtra("note") ?: ""
        val firstTime = intent.getStringExtra("firstTime") ?: ""  // 修正字段名
        val secondTime = intent.getStringExtra("secondTime") ?: ""  // 修正字段名
        val thirdTime = intent.getStringExtra("thirdTime") ?: ""   // 修正字段名

        setContent {
            AIHealthButlerTheme {
                val sessionManager = remember { SessionManager(applicationContext) }
                val repository = remember { Repository() }
                val viewModel = remember { MedicationPlanViewModel(repository, sessionManager) }

                AddnewmedicationplanScreen(
                    viewModel = viewModel,
                    onBack = { finish() },
                    onSuccess = {
                        startActivity(Intent(this, MedicationPlanActivity::class.java))
                        finish()
                    },
                    isEdit = isEdit,
                    planId = planId,
                    initialPillName = pillName,
                    initialStartDate = startDate,
                    initialDuration = if (duration > 0) duration.toString() else "",
                    initialMethod = method,
                    initialFrequency = frequency,
                    initialDosage = dosage,
                    initialGuide = guide,
                    initialNote = note,
                    initialFirstTime = firstTime,
                    initialSecondTime = secondTime,
                    initialThirdTime = thirdTime
                )
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AddnewmedicationplanScreen(
    viewModel: MedicationPlanViewModel,
    onBack: () -> Unit,
    onSuccess: () -> Unit,
    isEdit: Boolean,
    planId: Int,
    initialPillName: String,
    initialStartDate: String,
    initialDuration: String,
    initialMethod: String,
    initialFrequency: String,
    initialDosage: String,
    initialGuide: String,
    initialNote: String,
    initialFirstTime: String,
    initialSecondTime: String,
    initialThirdTime: String
) {
    val context = LocalContext.current
    val savingState by viewModel.savingState.collectAsState()

    // 监听保存状态
    LaunchedEffect(savingState) {
        when (savingState) {
            is MedicationPlanViewModel.SavingState.Success -> {
                val message = (savingState as MedicationPlanViewModel.SavingState.Success).message
                Toast.makeText(context, message, Toast.LENGTH_SHORT).show()
                onSuccess()
            }
            is MedicationPlanViewModel.SavingState.Error -> {
                val message = (savingState as MedicationPlanViewModel.SavingState.Error).message
                Toast.makeText(context, message, Toast.LENGTH_SHORT).show()
            }
            else -> {}
        }
    }

    var isLoading by remember { mutableStateOf(false) }

    // 监听加载状态
    LaunchedEffect(savingState) {
        isLoading = savingState is MedicationPlanViewModel.SavingState.Loading
    }

    Scaffold(
        modifier = Modifier
            .fillMaxSize()
            .background(Color(0xFFF4F6F8)),
        topBar = {
            CenterAlignedTopAppBar(
                title = {
                    Text(
                        text = if(isEdit) "修改用药计划" else "新增用药计划",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold
                    )
                },
                navigationIcon = {
                    IconButton(onClick = onBack) {
                        Icon(
                            painter = painterResource(R.drawable.left_arrow),
                            contentDescription = "返回",
                            modifier = Modifier.size(24.dp)
                        )
                    }
                },
                colors = TopAppBarDefaults.centerAlignedTopAppBarColors(
                    containerColor = Color(0xFFF5F6F7)
                )
            )
        },
        containerColor = Color(0xFFF4F6F8)
    ) { innerPadding ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(innerPadding)
                .verticalScroll(rememberScrollState())
                .padding(16.dp)
        ) {
            // Pill 标签：改为渐变背景
            Box(
                modifier = Modifier
                    .wrapContentSize()
                    .background(
                        brush = Brush.horizontalGradient(
                            colors = listOf(
                                Color(0xFF33B3CC),  // 渐变起始色
                                Color(0xFF56D1CC)   // 渐变结束色
                            )
                        ),
                        shape = RoundedCornerShape(20.dp)
                    )
                    .padding(horizontal = 16.dp, vertical = 8.dp)
            ) {
                Row(verticalAlignment = Alignment.CenterVertically) {
                    Icon(
                        painter = painterResource(R.drawable.ic_pill),
                        contentDescription = null,
                        tint = Color.White,
                        modifier = Modifier.size(22.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(
                        text = "填写药品信息",
                        color = Color.White,
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold
                    )
                }
            }
            Spacer(modifier = Modifier.height(16.dp))

            // 从返回的frequency中提取频次详情
            val initialFreqDetails = if (initialFrequency.contains("-")) {
                val parts = initialFrequency.split("-")[1].trim()
                val times = parts.split(",").size
                times.toString()
            } else {
                "1"
            }

            // 状态变量
            var pillName by remember { mutableStateOf(initialPillName) }
            var duration by remember { mutableStateOf(initialDuration) }
            var method by remember { mutableStateOf(initialMethod) }
            var frequency by remember {
                // 如果包含 "-"，则只保留前半部分作为基本频次
                if (initialFrequency.contains("-")) {
                    mutableStateOf(initialFrequency.split("-")[0].trim())
                } else {
                    mutableStateOf(initialFrequency)
                }
            }
            var frequencyDetail by remember { mutableStateOf(initialFreqDetails) }
            var firstTime by remember { mutableStateOf(initialFirstTime) }
            var secondTime by remember { mutableStateOf(initialSecondTime) }
            var thirdTime by remember { mutableStateOf(initialThirdTime) }
            var dosage by remember { mutableStateOf(initialDosage) }
            var guide by remember { mutableStateOf(initialGuide) }
            var note by remember { mutableStateOf(initialNote) }

            // 日期选择器状态
            var showDatePicker by remember { mutableStateOf(false) }
            val calendar = remember { Calendar.getInstance() }
            val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
            var startDate by remember {
                // 如果有初始日期则使用，否则使用当前日期
                mutableStateOf(
                    if (initialStartDate.isNotBlank()) initialStartDate
                    else dateFormat.format(Calendar.getInstance().time)
                )
            }

            // 当前选择的年月
            var currentYear by remember {
                mutableStateOf(
                    if (initialStartDate.isNotBlank()) {
                        try {
                            val date = dateFormat.parse(initialStartDate)
                            val cal = Calendar.getInstance()
                            cal.time = date
                            cal.get(Calendar.YEAR)
                        } catch (e: Exception) {
                            Calendar.getInstance().get(Calendar.YEAR)
                        }
                    } else {
                        Calendar.getInstance().get(Calendar.YEAR)
                    }
                )
            }
            var currentMonth by remember {
                mutableStateOf(
                    if (initialStartDate.isNotBlank()) {
                        try {
                            val date = dateFormat.parse(initialStartDate)
                            val cal = Calendar.getInstance()
                            cal.time = date
                            cal.get(Calendar.MONTH)
                        } catch (e: Exception) {
                            Calendar.getInstance().get(Calendar.MONTH)
                        }
                    } else {
                        Calendar.getInstance().get(Calendar.MONTH)
                    }
                )
            }

            // 获取当前月的天数
            val daysInMonth = remember(currentYear, currentMonth) {
                val cal = Calendar.getInstance()
                cal.set(currentYear, currentMonth, 1)
                cal.getActualMaximum(Calendar.DAY_OF_MONTH)
            }

            // 获取当前月第一天是星期几
            val firstDayOfMonth = remember(currentYear, currentMonth) {
                val cal = Calendar.getInstance()
                cal.set(currentYear, currentMonth, 1)
                cal.get(Calendar.DAY_OF_WEEK) - 1 // 调整为从0开始
            }

            // 日历选择器对话框
            if (showDatePicker) {
                Dialog(
                    onDismissRequest = { showDatePicker = false },
                    properties = DialogProperties(dismissOnClickOutside = true)
                ) {
                    Card(
                        modifier = Modifier
                            .fillMaxWidth(0.95f)  // 更宽的卡片
                            .padding(horizontal = 0.dp, vertical = 8.dp),
                        shape = RoundedCornerShape(16.dp),
                        colors = CardDefaults.cardColors(containerColor = Color.White)
                    ) {
                        Column(
                            modifier = Modifier
                                .padding(horizontal = 12.dp, vertical = 12.dp)  // 减少垂直内边距
                        ) {
                            // 月份导航
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                verticalAlignment = Alignment.CenterVertically,
                                horizontalArrangement = Arrangement.Center
                            ) {
                                IconButton(onClick = {
                                    if (currentMonth == 0) {
                                        currentMonth = 11
                                        currentYear--
                                    } else {
                                        currentMonth--
                                    }
                                }) {
                                    Text("<", fontSize = 16.sp, color = Color(0xFF505050))
                                }

                                Spacer(modifier = Modifier.width(30.dp))

                                Text(
                                    text = "${currentYear}年${currentMonth + 1}月",
                                    fontSize = 18.sp,
                                    fontWeight = FontWeight.Normal,
                                    color = Color(0xFF303030)
                                )

                                Spacer(modifier = Modifier.width(30.dp))

                                IconButton(onClick = {
                                    if (currentMonth == 11) {
                                        currentMonth = 0
                                        currentYear++
                                    } else {
                                        currentMonth++
                                    }
                                }) {
                                    Text(">", fontSize = 16.sp, color = Color(0xFF505050))
                                }
                            }

                            Spacer(modifier = Modifier.height(12.dp))

                            // 星期标题
                            Row(modifier = Modifier.fillMaxWidth()) {
                                listOf("日", "一", "二", "三", "四", "五", "六").forEach { day ->
                                    Box(
                                        modifier = Modifier.weight(1f),
                                        contentAlignment = Alignment.Center
                                    ) {
                                        Text(
                                            text = day,
                                            fontSize = 14.sp,
                                            color = Color(0xFF505050)
                                        )
                                    }
                                }
                            }

                            // 添加分隔线
                            Divider(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(vertical = 6.dp),
                                color = Color(0xFFE0E0E0),  // 浅灰色分隔线
                                thickness = 1.dp
                            )

                            // 日期网格
                            val rows = (daysInMonth + firstDayOfMonth + 6) / 7
                            for (row in 0 until rows) {
                                Row(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(vertical = 0.dp)  // 减少行间距
                                ) {
                                    for (col in 0 until 7) {
                                        val day = row * 7 + col - firstDayOfMonth + 1
                                        Box(
                                            modifier = Modifier
                                                .weight(1f)
                                                .padding(2.dp)  // 减少内边距
                                                .height(32.dp),  // 减少高度
                                            contentAlignment = Alignment.Center
                                        ) {
                                            if (day in 1..daysInMonth) {
                                                // 解析当前选中的日期
                                                val selectedCalendar = Calendar.getInstance().apply {
                                                    // 添加空字符串检查，防止解析错误
                                                    if (startDate.isNotBlank()) {
                                                        try {
                                                            time = dateFormat.parse(startDate) ?: Date()
                                                        } catch (e: Exception) {
                                                            Log.e("AddnewMedicationPlan", "日期解析错误: $startDate", e)
                                                            time = Date()
                                                        }
                                                    } else {
                                                        time = Date()
                                                    }
                                                }
                                                val selectedDay = selectedCalendar.get(Calendar.DAY_OF_MONTH)
                                                val selectedMonth = selectedCalendar.get(Calendar.MONTH)
                                                val selectedYear = selectedCalendar.get(Calendar.YEAR)

                                                // 判断是否是选中的日期
                                                val isSelected = day == selectedDay &&
                                                        currentMonth == selectedMonth &&
                                                        currentYear == selectedYear

                                                Box(
                                                    modifier = Modifier
                                                        .size(30.dp)
                                                        .aspectRatio(1f)  // 确保是正圆形
                                                        .clip(CircleShape)
                                                        .background(
                                                            if (isSelected) Color(0xFF81BEC7) else Color.Transparent
                                                        )
                                                        .clickable {
                                                            calendar.set(currentYear, currentMonth, day)
                                                            startDate = dateFormat.format(calendar.time)
                                                            showDatePicker = false
                                                        },
                                                    contentAlignment = Alignment.Center
                                                ) {
                                                    Text(
                                                        text = day.toString(),
                                                        color = if (isSelected) Color.White else Color(0xFF303030),  // 非选中日期使用更深的文字颜色
                                                        fontSize = 14.sp,
                                                        textAlign = TextAlign.Center
                                                    )
                                                }
                                            }
                                        }
                                    }
                                }
                            }

                            // 底部空间
                            Spacer(modifier = Modifier.height(2.dp))  // 减少底部空间
                        }
                    }
                }
            }

            //对于较旧版本的Compose
            val customTextFieldColors = OutlinedTextFieldDefaults.colors(
                focusedBorderColor = Color(0xFF33B3CC),
                unfocusedBorderColor = Color(0xFF81BEC7)
            )

            // 在药品输入框处添加自定义颜色
            OutlinedTextField(
                value = pillName,
                onValueChange = { pillName = it },
                label = { Text("药品") },
                placeholder = { Text("请输入药品名称") },
                modifier = Modifier.fillMaxWidth(),
                colors = customTextFieldColors
            )
            Spacer(Modifier.height(8.dp))

            // 用药开始时间
            Box(modifier = Modifier.fillMaxWidth()) {
                OutlinedTextField(
                    value = startDate,
                    onValueChange = { },
                    readOnly = true,
                    label = { Text("用药开始时间") },
                    placeholder = { Text("格式：YYYY-MM-DD，例如：2025-05-15") },
                    trailingIcon = {
                        Icon(
                            painter = painterResource(R.drawable.right_arrow),
                            contentDescription = "选择日期",
                            tint = Color.Gray,
                            modifier = Modifier.size(16.dp)
                        )
                    },
                    modifier = Modifier.fillMaxWidth(),
                    colors = customTextFieldColors
                )

                // 在TextField上方添加一个透明的可点击层
                Box(
                    modifier = Modifier
                        .matchParentSize()
                        .clickable(
                            indication = null,  // 移除点击时的视觉反馈
                            interactionSource = remember { MutableInteractionSource() }
                        ) {
                            showDatePicker = true  // 点击时显示日历
                        }
                )
            }

            // 用药持续时间
            OutlinedTextField(
                value = duration,
                onValueChange = { duration = it },
                label = { Text("用药持续时间") },
                placeholder = { Text("天数，例如：7") },
                modifier = Modifier.fillMaxWidth(),
                colors = customTextFieldColors
            )
            Spacer(Modifier.height(8.dp))

            // 用法
            OutlinedTextField(
                value = method,
                onValueChange = { method = it },
                label = { Text("用法") },
                placeholder = { Text("例如：口服") },
                modifier = Modifier.fillMaxWidth(),
                colors = customTextFieldColors
            )
            Spacer(Modifier.height(8.dp))

            // 用药频次
            OutlinedTextField(
                value = frequency,
                onValueChange = { frequency = it },
                label = { Text("用药频次") },
                placeholder = { Text("例如：每日3次") },
                modifier = Modifier.fillMaxWidth(),
                colors = customTextFieldColors
            )
            Spacer(Modifier.height(8.dp))

            // 频次详情
            OutlinedTextField(
                value = frequencyDetail,
                onValueChange = {
                    // 只允许1-3之间的数字
                    val num = it.toIntOrNull()
                    if (it.isEmpty() || (num != null && num in 1..3)) {
                        frequencyDetail = it
                    }
                },
                label = { Text("频次详情（次数）") },
                placeholder = { Text("1-3次之间") },
                modifier = Modifier.fillMaxWidth(),
                colors = customTextFieldColors
            )
            Spacer(Modifier.height(8.dp))

            // 第一次用药时间
            OutlinedTextField(
                value = firstTime,
                onValueChange = { firstTime = it },
                label = { Text("第一次用药时间") },
                placeholder = { Text("格式：HH:MM，例如：08:00") },
                modifier = Modifier.fillMaxWidth(),
                colors = customTextFieldColors
            )
            Spacer(Modifier.height(8.dp))

            // 第二次用药时间 (如果需要)
            if (frequencyDetail.toIntOrNull() ?: 0 >= 2) {
                OutlinedTextField(
                    value = secondTime,
                    onValueChange = { secondTime = it },
                    label = { Text("第二次用药时间") },
                    placeholder = { Text("格式：HH:MM，例如：12:00") },
                    modifier = Modifier.fillMaxWidth(),
                    colors = customTextFieldColors
                )
                Spacer(Modifier.height(8.dp))
            }

            // 第三次用药时间 (如果需要)
            if (frequencyDetail.toIntOrNull() ?: 0 >= 3) {
                OutlinedTextField(
                    value = thirdTime,
                    onValueChange = { thirdTime = it },
                    label = { Text("第三次用药时间") },
                    placeholder = { Text("格式：HH:MM，例如：18:00") },
                    modifier = Modifier.fillMaxWidth(),
                    colors = customTextFieldColors
                )
                Spacer(Modifier.height(8.dp))
            }

            // 剂量
            OutlinedTextField(
                value = dosage,
                onValueChange = { dosage = it },
                label = { Text("剂量") },
                placeholder = { Text("例如：1片") },
                modifier = Modifier.fillMaxWidth(),
                colors = customTextFieldColors
            )
            Spacer(Modifier.height(8.dp))

            // 用药指导
            OutlinedTextField(
                value = guide,
                onValueChange = { guide = it },
                label = { Text("用药指导") },
                placeholder = { Text("例如：饭后") },
                modifier = Modifier.fillMaxWidth(),
                colors = customTextFieldColors
            )
            Spacer(Modifier.height(8.dp))

            // 备注
            OutlinedTextField(
                value = note,
                onValueChange = { note = it },
                label = { Text("备注") },
                placeholder = { Text("可选") },
                modifier = Modifier.fillMaxWidth(),
                colors = customTextFieldColors
            )
            Spacer(Modifier.height(24.dp))

            // 确认按钮
            Button(
                onClick = {
                    // 验证输入
                    if (pillName.isBlank()) {
                        Toast.makeText(context, "请输入药品名称", Toast.LENGTH_SHORT).show()
                        return@Button
                    }

                    if (duration.isBlank() || duration.toIntOrNull() == null || duration.toIntOrNull()!! <= 0) {
                        Toast.makeText(context, "请输入有效的用药持续时间", Toast.LENGTH_SHORT).show()
                        return@Button
                    }

                    if (method.isBlank()) {
                        Toast.makeText(context, "请输入用药方法", Toast.LENGTH_SHORT).show()
                        return@Button
                    }

                    if (frequency.isBlank()) {
                        Toast.makeText(context, "请输入用药频次", Toast.LENGTH_SHORT).show()
                        return@Button
                    }

                    // 验证频次详情和时间
                    val freqDetail = frequencyDetail.toIntOrNull() ?: 1

                    if (freqDetail >= 1 && firstTime.isBlank()) {
                        Toast.makeText(context, "请输入第一次用药时间", Toast.LENGTH_SHORT).show()
                        return@Button
                    }

                    if (freqDetail >= 2 && secondTime.isBlank()) {
                        Toast.makeText(context, "请输入第二次用药时间", Toast.LENGTH_SHORT).show()
                        return@Button
                    }

                    if (freqDetail >= 3 && thirdTime.isBlank()) {
                        Toast.makeText(context, "请输入第三次用药时间", Toast.LENGTH_SHORT).show()
                        return@Button
                    }

                    if (dosage.isBlank()) {
                        Toast.makeText(context, "请输入用药剂量", Toast.LENGTH_SHORT).show()
                        return@Button
                    }

                    // 调用ViewModel保存数据
                    if (isEdit) {
                        viewModel.updateMedicationPlan(
                            mplanId = planId,
                            pillName = pillName,
                            startDate = startDate,
                            duration = duration,
                            method = method,
                            frequency = frequency,
                            frequencyDetail = freqDetail,
                            firstTime = firstTime,
                            secondTime = secondTime,
                            thirdTime = thirdTime,
                            dosage = dosage,
                            guide = guide,
                            note = note
                        )
                    } else {
                        viewModel.saveMedicationPlan(
                            pillName = pillName,
                            startDate = startDate,
                            duration = duration,
                            method = method,
                            frequency = frequency,
                            frequencyDetail = freqDetail,
                            firstTime = firstTime,
                            secondTime = secondTime,
                            thirdTime = thirdTime,
                            dosage = dosage,
                            guide = guide,
                            note = note
                        )
                    }
                },
                modifier = Modifier
                    .fillMaxWidth()
                    .height(48.dp),
                contentPadding = PaddingValues(0.dp),
                colors = ButtonDefaults.buttonColors(containerColor = Color.Transparent),
                enabled = !isLoading
            ) {
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(
                            brush = Brush.horizontalGradient(
                                colors = listOf(Color(0xFF70E1C7), Color(0xFF3AABE2))
                            ),
                            shape = RoundedCornerShape(24.dp)
                        ),
                    contentAlignment = Alignment.Center
                ) {
                    if (isLoading) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(24.dp),
                            color = Color.White,
                            strokeWidth = 2.dp
                        )
                    } else {
                        Text(
                            text = if (isEdit) "更新" else "确定",
                            color = Color.White,
                            fontSize = 18.sp,
                            fontWeight = FontWeight.Bold
                        )
                    }
                }
            }
        }
    }
}
