package com.example.aihealthbutler

import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.Person
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.CenterAlignedTopAppBar
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.OutlinedTextFieldDefaults
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.ui.window.Dialog
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

import com.example.aihealthbutler.api.Repository
import com.example.aihealthbutler.api.SessionManager
import com.example.aihealthbutler.ui.theme.AIHealthButlerTheme
import com.example.aihealthbutler.ui.components.CustomDeleteDialog
import kotlinx.coroutines.flow.firstOrNull
import java.text.SimpleDateFormat
import java.util.Locale
import java.util.Calendar
import kotlinx.coroutines.launch
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.CoroutineScope
import androidx.lifecycle.viewModelScope
import androidx.lifecycle.ViewModel
import androidx.compose.runtime.MutableState
import com.example.aihealthbutler.api.SetFamilyRelationshipRequest
import com.example.aihealthbutler.api.SwitchMemberRequest
import com.example.aihealthbutler.api.FamilyMemberData

private const val TAG = "FamilyMemberSwitch"

class FamilyMemberSwitchViewModel(
    private val repository: Repository,
    private val sessionManager: SessionManager
) : ViewModel() {
    private val TAG = "FamilyMemberSwitchVM"
    
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading
    
    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error

    private val _members = MutableStateFlow<List<FamilyMemberData>>(emptyList())
    val members: StateFlow<List<FamilyMemberData>> = _members
    
    init {
        loadFamilyMembers()
    }
    
    fun loadFamilyMembers() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                _error.value = null

                val token = sessionManager.token.firstOrNull() ?: throw Exception("未登录")
                val account = sessionManager.account.value ?: throw Exception("未登录")
                val currentDefaultId = sessionManager.defaultId.firstOrNull() ?: throw Exception("获取当前用户ID失败")

                // 获取家庭成员列表
                val result = repository.getAllFamilyMembers(token, account)

                result.fold(
                    onSuccess = { response ->
                        if (response.code == 200) {
                            Log.d(TAG, "获取家庭成员成功: ${response.data.size}个成员")

                            // 为每个成员设置显示信息
                            val membersWithRelations = response.data.map { member ->
                                if (member.defaultId == currentDefaultId) {
                                    // 当前用户显示"你"
                                    member.copy(role = "你")
                                } else {
                                    // 其他成员暂时显示"加载中..."，提升用户体验
                                    member.copy(role = "加载中...")
                                }
                            }

                            // 立即显示基本信息
                            _members.value = membersWithRelations
                            _error.value = null

                            // 并发获取关系信息，提升加载速度
                            loadRelationships(token, currentDefaultId, membersWithRelations)
                        } else {
                            _error.value = response.message
                        }
                    },
                    onFailure = { e ->
                        _error.value = "获取家庭成员列表失败: ${e.message}"
                        Log.e(TAG, "获取家庭成员列表失败", e)
                    }
                )
            } catch (e: Exception) {
                _error.value = "获取家庭成员列表异常: ${e.message}"
                Log.e(TAG, "获取家庭成员列表异常", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    // 并发加载关系信息 - 提升实时性
    private fun loadRelationships(token: String, currentDefaultId: String, members: List<FamilyMemberData>) {
        viewModelScope.launch {
            try {
                Log.d(TAG, "开始并发加载关系信息，成员数量: ${members.size}")

                // 使用并发方式同时获取所有成员的关系信息
                val updatedMembers = members.map { member ->
                    <EMAIL><FamilyMemberData> {
                        if (member.defaultId == currentDefaultId) {
                            member // 当前用户不需要获取关系
                        } else {
                            try {
                                Log.d(TAG, "获取成员${member.name}(${member.defaultId})的关系信息")
                                // 获取该成员与当前用户的关系
                                val relationshipResult = repository.getAllRelationships(token, member.defaultId)
                                relationshipResult.fold(
                                    onSuccess = { relationResponse ->
                                        Log.d(TAG, "关系API响应: code=${relationResponse.code}, message=${relationResponse.message}")
                                        Log.d(TAG, "关系数据: ${relationResponse.data}")
                                        if (relationResponse.code == 200 && relationResponse.data.isNotEmpty()) {
                                            val relationship = relationResponse.data[currentDefaultId]
                                            Log.d(TAG, "成员${member.name}与当前用户(${currentDefaultId})的关系: $relationship")
                                            if (relationship != null) {
                                                member.copy(role = relationship)
                                            } else {
                                                Log.d(TAG, "未找到成员${member.name}与当前用户的关系")
                                                member.copy(role = "未设置关系")
                                            }
                                        } else {
                                            Log.w(TAG, "获取成员${member.name}关系失败: code=${relationResponse.code}, message=${relationResponse.message}")
                                            member.copy(role = "未设置关系")
                                        }
                                    },
                                    onFailure = { e ->
                                        Log.e(TAG, "获取成员${member.name}关系异常", e)
                                        member.copy(role = "未设置关系")
                                    }
                                )
                            } catch (e: Exception) {
                                Log.e(TAG, "处理成员${member.name}关系时异常", e)
                                member.copy(role = "未设置关系")
                            }
                        }
                    }
                }.awaitAll() // 等待所有并发任务完成

                Log.d(TAG, "关系信息加载完成，更新成员列表")
                // 立即更新成员列表
                _members.value = updatedMembers
            } catch (e: Exception) {
                Log.e(TAG, "加载关系信息异常", e)
            }
        }
    }
    
    fun switchMember(defaultId: String) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                _error.value = null
                
                val token = sessionManager.token.firstOrNull() ?: throw Exception("未登录")
                val account = sessionManager.account.value ?: throw Exception("未登录")
                
                val request = SwitchMemberRequest(account = account, defaultId = defaultId)
                val result = repository.switchMember(token, request)
                
                result.fold(
                    onSuccess = { response ->
                        if (response.code == 200 && response.data != null) {
                            sessionManager.saveToken(response.data.token)
                            sessionManager.saveDefaultId(response.data.defaultId)
                            _error.value = null
                        } else {
                            _error.value = response.message
                        }
                    },
                    onFailure = { e ->
                        _error.value = "切换家庭成员失败: ${e.message}"
                        Log.e(TAG, "切换家庭成员失败", e)
                    }
                )
            } catch (e: Exception) {
                _error.value = "切换家庭成员异常: ${e.message}"
                Log.e(TAG, "切换家庭成员异常", e)
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    fun deleteFamilyMember(defaultId: String) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                _error.value = null

                val token = sessionManager.token.firstOrNull() ?: throw Exception("未登录")

                Log.d(TAG, "开始删除家庭成员，defaultId: $defaultId")
                Log.d(TAG, "请求URL: https://qcx.yuneyang.top/api/family/member/$defaultId")

                // 使用新的删除家庭成员接口
                val deleteFamilyMemberResult = repository.deleteFamilyMember(token, defaultId)

                deleteFamilyMemberResult.fold(
                    onSuccess = { response ->
                        if (response.code == 200 && response.data == true) {
                            Log.d(TAG, "删除家庭成员成功: ${response.message}")
                            // 重新加载家庭成员列表
                            loadFamilyMembers()
                            _error.value = null
                        } else {
                            _error.value = response.message ?: "删除家庭成员失败"
                            Log.w(TAG, "删除家庭成员失败: ${response.message}")
                        }
                    },
                    onFailure = { e ->
                        _error.value = "删除家庭成员失败: ${e.message}"
                        Log.e(TAG, "删除家庭成员失败", e)
                    }
                )

            } catch (e: Exception) {
                _error.value = "删除家庭成员失败: ${e.message}"
                Log.e(TAG, "删除家庭成员异常", e)
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    fun updateFamilyRelation(defaultId: String, role: String) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                _error.value = null

                val token = sessionManager.token.firstOrNull() ?: throw Exception("未登录")
                val currentDefaultId = sessionManager.defaultId.firstOrNull() ?: throw Exception("获取当前用户ID失败")
                val account = sessionManager.account.value ?: throw Exception("获取账号失败")

                Log.d(TAG, "设置关系参数:")
                Log.d(TAG, "  - 当前用户ID: $currentDefaultId")
                Log.d(TAG, "  - 目标成员ID: $defaultId")
                Log.d(TAG, "  - 关系: $role")
                Log.d(TAG, "  - 账号: $account")
                Log.d(TAG, "  - Token: ${token.take(20)}...")

                // 使用新的家庭关系接口
                val request = SetFamilyRelationshipRequest(
                    fromMemberId = defaultId,
                    toMemberId = currentDefaultId,
                    relationship = role
                )

                Log.d(TAG, "发送家庭关系请求: $request")
                Log.d(TAG, "请求URL: https://qcx.yuneyang.top/api/family-relationships/set")

                val result = repository.setFamilyRelationship(token, request)

                result.fold(
                    onSuccess = { response ->
                        Log.d(TAG, "家庭关系API响应成功: code=${response.code}, message=${response.message}")
                        if (response.code == 200) {
                            Log.d(TAG, "更新家庭关系成功")

                            // 立即更新本地数据，提升实时性
                            updateMemberRelationshipLocally(defaultId, role)

                            // 异步重新加载完整数据以确保数据一致性
                            launch {
                                delay(100) // 短暂延时确保服务端数据已更新
                                loadFamilyMembers()
                            }

                            _error.value = null
                        } else if (response.code == 403) {
                            Log.w(TAG, "权限不足，可能需要检查API权限或用户角色")
                            _error.value = "权限不足，请检查是否有设置关系的权限"
                        } else {
                            Log.w(TAG, "设置关系失败: code=${response.code}, message=${response.message}")
                            _error.value = response.message ?: "更新家庭关系失败"
                        }
                    },
                    onFailure = { e ->
                        Log.e(TAG, "家庭关系API调用失败", e)
                        _error.value = "更新家庭关系失败: ${e.message}"
                    }
                )
            } catch (e: Exception) {
                _error.value = "更新家庭关系异常: ${e.message}"
                Log.e(TAG, "更新家庭关系异常", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    // 立即更新本地成员关系，提升实时性
    private fun updateMemberRelationshipLocally(memberId: String, newRole: String) {
        try {
            val currentMembers = _members.value
            val updatedMembers = currentMembers.map { member ->
                if (member.defaultId == memberId) {
                    Log.d(TAG, "立即更新成员${member.name}的关系: $newRole")
                    member.copy(role = newRole)
                } else {
                    member
                }
            }

            // 立即更新UI
            _members.value = updatedMembers
            Log.d(TAG, "本地关系更新完成，UI已刷新")
        } catch (e: Exception) {
            Log.e(TAG, "本地更新关系失败", e)
        }
    }
}

class FamilyMemberSwitchActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        
        setContent {
            AIHealthButlerTheme {
                val sessionManager = remember { SessionManager(applicationContext) }
                val repository = remember { Repository() }
                val viewModel = remember { FamilyMemberSwitchViewModel(repository, sessionManager) }
                
                FamilyMemberSwitchScreen(viewModel)
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun FamilyMemberSwitchScreen(viewModel: FamilyMemberSwitchViewModel) {
    val context = LocalContext.current
    
    val members by viewModel.members.collectAsState()
    val isLoading by viewModel.isLoading.collectAsState()
    val error by viewModel.error.collectAsState()
    
    var showDeleteConfirmDialog by remember { mutableStateOf(false) }
    var memberToDelete by remember { mutableStateOf<FamilyMemberData?>(null) }
    
    var showEditDialog by remember { mutableStateOf(false) }
    var memberToEdit by remember { mutableStateOf<FamilyMemberData?>(null) }
    
    val expandedMemberId = remember { mutableStateOf<String?>(null) }
    
    LaunchedEffect(error) {
        error?.let {
            Toast.makeText(context, it, Toast.LENGTH_SHORT).show()
        }
    }
    
    Scaffold(
        topBar = {
            CenterAlignedTopAppBar(
                title = { Text("切换成员", fontSize = 20.sp, fontWeight = FontWeight.Bold) },
                navigationIcon = {
                    IconButton(onClick = { (context as? ComponentActivity)?.finish() }) {
                        Icon(
                            painter = painterResource(id = R.drawable.left_arrow),
                            contentDescription = "返回",
                            modifier = Modifier.size(24.dp)
                        )
                    }
                },
                colors = TopAppBarDefaults.centerAlignedTopAppBarColors(
                    containerColor = Color(0xFFF5F6F7)
                )
            )
        },
        bottomBar = {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(Color(0xFFF5F6F7))
                    .padding(16.dp)
            ) {
                Button(
                    onClick = {
                        context.startActivity(Intent(context, AddNewFamilyMember::class.java))
                    },
                    modifier = Modifier.fillMaxWidth(),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = Color(0xFF2EB0AC)
                    )
                ) {
                    Text("添加新成员")
                }
            }
        },
        containerColor = Color(0xFFF5F6F7)
    ) { innerPadding ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(innerPadding)
        ) {
            if (isLoading) {
                CircularProgressIndicator(
                    modifier = Modifier.align(Alignment.Center),
                    color = Color(0xFF2EB0AC)
                )
            } else {
                LazyColumn(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(horizontal = 0.dp, vertical = 8.dp)
                ) {
                    items(members) { member ->
                        FamilyMemberListItem(
                            member = member,
                            isSelected = false,
                            expandedMemberId = expandedMemberId,
                            onClick = {
                                viewModel.switchMember(member.defaultId)
                                (context as? ComponentActivity)?.finish()
                            },
                            onEdit = {
                                memberToEdit = member
                                showEditDialog = true
                            },
                            onDelete = if (member.role != null && member.role != "你") {
                                {
                                    memberToDelete = member
                                    showDeleteConfirmDialog = true
                                }
                            } else null
                        )
                    }
                }
            }
        }
    }
    
    if (showDeleteConfirmDialog && memberToDelete != null) {
        CustomDeleteDialog(
            title = "确认删除",
            message = "确定要删除${memberToDelete?.name}吗？",
            onDismiss = { showDeleteConfirmDialog = false },
            onConfirm = {
                memberToDelete?.let { member ->
                    viewModel.deleteFamilyMember(member.defaultId)
                }
                showDeleteConfirmDialog = false
            }
        )
    }
    
    if (showEditDialog && memberToEdit != null) {
        EditMemberDialog(
            member = memberToEdit!!,
            onDismiss = { showEditDialog = false },
            onConfirm = { newRole ->
                viewModel.updateFamilyRelation(memberToEdit!!.defaultId, newRole)
                showEditDialog = false
            }
        )
    }
}

@Composable
fun EditMemberDialog(
    member: FamilyMemberData,
    onDismiss: () -> Unit,
    onConfirm: (String) -> Unit
) {
    var newRole by remember { mutableStateOf(member.role ?: "") }

    Dialog(onDismissRequest = onDismiss) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .background(Color.White, RoundedCornerShape(16.dp))
                .padding(20.dp)
        ) {
            Text(
                "编辑成员关系",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                color = Color.Black,
                modifier = Modifier.padding(bottom = 16.dp)
            )

            OutlinedTextField(
                value = newRole,
                onValueChange = { newRole = it },
                label = { Text("角色", color = Color.Gray) },
                placeholder = { Text("姐姐", color = Color.Gray) },
                colors = OutlinedTextFieldDefaults.colors(
                    focusedBorderColor = Color(0xFF2EB0AC),
                    unfocusedBorderColor = Color.Gray,
                    focusedLabelColor = Color(0xFF2EB0AC),
                    cursorColor = Color(0xFF2EB0AC),
                    unfocusedContainerColor = Color.White,
                    focusedContainerColor = Color.White
                ),
                shape = RoundedCornerShape(8.dp),
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 20.dp)
            )

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                Button(
                    onClick = onDismiss,
                    colors = ButtonDefaults.buttonColors(
                        containerColor = Color.Gray
                    ),
                    shape = RoundedCornerShape(8.dp),
                    modifier = Modifier.weight(1f)
                ) {
                    Text("取消", color = Color.White)
                }

                Spacer(modifier = Modifier.width(16.dp))

                Button(
                    onClick = {
                        if (newRole.isNotBlank()) {
                            onConfirm(newRole.trim())
                        }
                    },
                    enabled = newRole.isNotBlank(),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = Color(0xFF2EB0AC),
                        disabledContainerColor = Color.Gray
                    ),
                    shape = RoundedCornerShape(8.dp),
                    modifier = Modifier.weight(1f)
                ) {
                    Text("确定", color = Color.White)
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun FamilyMemberListItem(
    member: FamilyMemberData,
    isSelected: Boolean,
    expandedMemberId: MutableState<String?>,
    onClick: () -> Unit,
    onEdit: () -> Unit,
    onDelete: (() -> Unit)?
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 6.dp)
            .clickable { onClick() },
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        ),
        shape = RoundedCornerShape(12.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 左侧：头像和信息
            Row(verticalAlignment = Alignment.CenterVertically) {
                Icon(
                    imageVector = Icons.Default.Person,
                    contentDescription = null,
                    tint = if (member.sex == 1) Color(0xFF2196F3) else Color(0xFFE91E63),
                    modifier = Modifier.size(32.dp)
                )

                Spacer(modifier = Modifier.width(12.dp))

                Column {
                    // 姓名和角色
                    val displayName = if (member.name.isNullOrEmpty()) {
                        "未设置姓名"
                    } else {
                        member.name
                    }

                    val displayRole = if (member.role.isNullOrEmpty()) {
                        "未设置关系"
                    } else {
                        member.role
                    }

                    Text(
                        text = "$displayName ($displayRole)",
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Medium,
                        color = Color.Black
                    )

                    // 详细信息
                    Text(
                        text = "${calculateAge(member.birthday)}岁 | " +
                               "${member.height}cm | " +
                               "${String.format("%.1f", member.weight)}kg | " +
                               "BMI: ${String.format("%.1f", member.bmi)}",
                        fontSize = 12.sp,
                        color = Color.Gray
                    )
                }
            }

            // 右侧：操作按钮
            Row {
                IconButton(onClick = onEdit) {
                    Icon(
                        imageVector = Icons.Default.Edit,
                        contentDescription = "编辑",
                        tint = Color(0xFF2EB0AC),
                        modifier = Modifier.size(20.dp)
                    )
                }

                if (onDelete != null && member.role != null && member.role != "你") {
                    IconButton(onClick = onDelete) {
                        Icon(
                            imageVector = Icons.Default.Delete,
                            contentDescription = "删除",
                            tint = Color.Red,
                            modifier = Modifier.size(20.dp)
                        )
                    }
                }
            }
        }
    }
}

// 计算年龄的工具函数
fun calculateAge(birthday: String?): Int {
    if (birthday.isNullOrEmpty()) return 0
    
    return try {
        val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
        val birthDate = dateFormat.parse(birthday)
        val today = Calendar.getInstance()
        val birthCalendar = Calendar.getInstance().apply { time = birthDate!! }
        
        var age = today.get(Calendar.YEAR) - birthCalendar.get(Calendar.YEAR)
        if (today.get(Calendar.DAY_OF_YEAR) < birthCalendar.get(Calendar.DAY_OF_YEAR)) {
            age--
        }
        age
    } catch (e: Exception) {
        0
    }
}















