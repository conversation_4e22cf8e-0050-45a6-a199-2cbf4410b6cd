package com.example.aihealthbutler.qr

import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Rect
import android.util.Log
import com.google.gson.Gson
import java.security.MessageDigest

/**
 * 二维码生成工具类
 */
object QRCodeGenerator {
    private const val TAG = "QRCodeGenerator"
    private val gson = Gson()
    
    /**
     * 生成邀请二维码
     */
    fun generateInviteQRCode(
        inviteData: QRInviteData,
        config: QRCodeConfig = QRCodeConfig()
    ): QRCodeResult {
        return try {
            // 添加签名
            val dataWithSignature = inviteData.copy(
                signature = generateSignature(inviteData)
            )
            
            val jsonData = gson.toJson(dataWithSignature)
            Log.d(TAG, "生成二维码数据: $jsonData")
            
            val bitmap = generateQRCodeBitmap(jsonData, config)
            QRCodeResult.Success(bitmap)
        } catch (e: Exception) {
            Log.e(TAG, "生成二维码失败", e)
            QRCodeResult.Error("生成二维码失败: ${e.message}", e)
        }
    }
    
    /**
     * 生成二维码位图
     */
    private fun generateQRCodeBitmap(
        content: String,
        config: QRCodeConfig
    ): Bitmap {
        return try {
            // 使用简单的二维码生成器
            SimpleQRGenerator.generateQRBitmap(content, config.width)
        } catch (e: Exception) {
            Log.e(TAG, "生成二维码位图失败", e)
            createErrorBitmap(config)
        }
    }

    
    /**
     * 创建错误位图
     */
    private fun createErrorBitmap(config: QRCodeConfig): Bitmap {
        val bitmap = Bitmap.createBitmap(config.width, config.height, Bitmap.Config.RGB_565)
        val canvas = Canvas(bitmap)

        // 填充背景
        canvas.drawColor(config.backgroundColor)

        // 绘制错误提示
        val paint = Paint().apply {
            color = 0xFFFF0000.toInt() // 红色
            textSize = 32f
            textAlign = Paint.Align.CENTER
        }

        canvas.drawText(
            "生成失败",
            config.width / 2f,
            config.height / 2f,
            paint
        )

        return bitmap
    }
    
    /**
     * 生成数据签名（简单的MD5签名）
     */
    private fun generateSignature(inviteData: QRInviteData): String {
        val dataToSign = "${inviteData.userId}${inviteData.account}${inviteData.timestamp}"
        return try {
            val md = MessageDigest.getInstance("MD5")
            val digest = md.digest(dataToSign.toByteArray())
            digest.joinToString("") { "%02x".format(it) }
        } catch (e: Exception) {
            Log.w(TAG, "生成签名失败，使用默认签名", e)
            "default_signature"
        }
    }
    
    /**
     * 验证二维码数据
     */
    fun validateQRInviteData(qrContent: String): QRValidationResult {
        return try {
            val inviteData = gson.fromJson(qrContent, QRInviteData::class.java)
            
            // 检查类型
            if (inviteData.type != "user_invite") {
                return QRValidationResult.Invalid("不是有效的用户邀请二维码")
            }
            
            // 检查必要字段
            if (inviteData.userId.isBlank() || inviteData.account.isBlank()) {
                return QRValidationResult.Invalid("二维码数据不完整")
            }
            
            // 检查时间戳（24小时有效期）
            val currentTime = System.currentTimeMillis()
            val validDuration = 24 * 60 * 60 * 1000L // 24小时
            if (currentTime - inviteData.timestamp > validDuration) {
                return QRValidationResult.Expired(inviteData.timestamp)
            }
            
            // 验证签名
            val expectedSignature = generateSignature(inviteData.copy(signature = null))
            if (inviteData.signature != expectedSignature) {
                Log.w(TAG, "签名验证失败，但继续处理")
                // 注意：在实际应用中，签名验证失败应该拒绝处理
            }
            
            QRValidationResult.Valid
        } catch (e: Exception) {
            Log.e(TAG, "验证二维码数据失败", e)
            QRValidationResult.Invalid("二维码格式错误: ${e.message}")
        }
    }
    
    /**
     * 解析二维码数据
     */
    fun parseQRInviteData(qrContent: String): QRInviteData? {
        return try {
            gson.fromJson(qrContent, QRInviteData::class.java)
        } catch (e: Exception) {
            Log.e(TAG, "解析二维码数据失败", e)
            null
        }
    }

    /**
     * 生成测试二维码（简单文本）
     */
    fun generateTestQRCode(text: String = "https://example.com/test"): QRCodeResult {
        return try {
            Log.d(TAG, "生成测试二维码: $text")
            val bitmap = SimpleQRGenerator.generateQRBitmap(text, 512)
            QRCodeResult.Success(bitmap)
        } catch (e: Exception) {
            Log.e(TAG, "生成测试二维码失败", e)
            QRCodeResult.Error("生成测试二维码失败: ${e.message}", e)
        }
    }
}
