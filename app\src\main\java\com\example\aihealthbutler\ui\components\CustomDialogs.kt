package com.example.aihealthbutler.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog

/**
 * 统一的删除确认弹窗组件
 *
 * @param title 弹窗标题
 * @param message 弹窗消息内容
 * @param onDismiss 取消/关闭回调
 * @param onConfirm 确认删除回调
 */
@Composable
fun CustomDeleteDialog(
    title: String,
    message: String,
    onDismiss: () -> Unit,
    onConfirm: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        containerColor = Color(0xFFF0F0F0),
        shape = RoundedCornerShape(16.dp),
        title = {
            Text(
                title,
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                color = Color.Black
            )
        },
        text = {
            Column {
                Text(
                    message,
                    fontSize = 16.sp,
                    color = Color.Black
                )
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    "删除后数据将无法恢复",
                    fontSize = 14.sp,
                    color = Color.Gray
                )
            }
        },
        confirmButton = {
            Button(
                onClick = onConfirm,
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color(0xFF2EB0AC)
                ),
                shape = RoundedCornerShape(8.dp)
            ) {
                Text("确定", color = Color.White)
            }
        },
        dismissButton = {
            Button(
                onClick = onDismiss,
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color.Gray
                ),
                shape = RoundedCornerShape(8.dp)
            ) {
                Text("取消", color = Color.White)
            }
        }
    )
}

/**
 * 性别选择弹窗组件
 *
 * @param onDismiss 取消/关闭回调
 * @param onGenderSelected 性别选择回调
 */
@Composable
fun GenderSelectionDialog(
    onDismiss: () -> Unit,
    onGenderSelected: (String) -> Unit
) {
    Dialog(onDismissRequest = onDismiss) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .background(Color.White, RoundedCornerShape(16.dp))
                .padding(20.dp)
        ) {
            Text(
                "选择性别",
                fontWeight = FontWeight.Bold,
                fontSize = 18.sp,
                color = Color.Black,
                modifier = Modifier.padding(bottom = 20.dp)
            )

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                // 男性选项
                Box(
                    modifier = Modifier
                        .size(80.dp, 40.dp)
                        .background(
                            Color(0xFFE3F2FD), // 浅蓝色
                            RoundedCornerShape(8.dp)
                        )
                        .clickable {
                            onGenderSelected("男")
                        },
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        "男",
                        fontSize = 16.sp,
                        color = Color(0xFF1976D2),
                        fontWeight = FontWeight.Medium
                    )
                }

                // 女性选项
                Box(
                    modifier = Modifier
                        .size(80.dp, 40.dp)
                        .background(
                            Color(0xFFFCE4EC), // 浅粉色
                            RoundedCornerShape(8.dp)
                        )
                        .clickable {
                            onGenderSelected("女")
                        },
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        "女",
                        fontSize = 16.sp,
                        color = Color(0xFFE91E63),
                        fontWeight = FontWeight.Medium
                    )
                }
            }
        }
    }
}
