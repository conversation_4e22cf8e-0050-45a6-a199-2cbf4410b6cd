package com.example.aihealthbutler.utils

import android.util.Log
import java.net.URL
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * 调试工具类，用于开发阶段诊断问题
 */
object DebugUtils {

    /**
     * 尝试连接服务器并获取基本信息
     */
    suspend fun checkServerConnection(serverUrl: String = "https://qcx.yuneyang.top/api/"): String {
        return withContext(Dispatchers.IO) {
            try {
                val url = URL(serverUrl)
                val connection = url.openConnection() as java.net.HttpURLConnection
                connection.connectTimeout = 5000
                connection.connect()
                
                val responseCode = connection.responseCode
                val responseMessage = connection.responseMessage
                val contentType = connection.contentType ?: "未知"
                
                // 读取响应内容
                val inputStream = if (responseCode < 400) connection.inputStream else connection.errorStream
                val content = inputStream.bufferedReader().use { it.readText() }
                
                val result = """
                    服务器连接测试结果:
                    URL: $serverUrl
                    响应码: $responseCode
                    响应消息: $responseMessage
                    内容类型: $contentType
                    响应内容: ${content.take(200)}${if (content.length > 200) "..." else ""}
                """.trimIndent()
                
                Log.d("DebugUtils", result)
                result
            } catch (e: Exception) {
                val errorMsg = "服务器连接失败: ${e.message}"
                Log.e("DebugUtils", errorMsg, e)
                errorMsg
            }
        }
    }
    
    /**
     * 格式化HTTP请求信息，便于调试
     */
    fun formatRequestInfo(
        url: String,
        method: String = "GET",
        headers: Map<String, String> = emptyMap(),
        body: String? = null
    ): String {
        val headersFormatted = headers.entries.joinToString("\n") { "${it.key}: ${it.value}" }
        
        return """
            请求信息:
            URL: $url
            方法: $method
            头部:
            $headersFormatted
            ${if (body != null) "请求体:\n$body" else ""}
        """.trimIndent()
    }
} 