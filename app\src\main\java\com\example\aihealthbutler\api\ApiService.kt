package com.example.aihealthbutler.api

// 添加以下Retrofit相关导入
import retrofit2.http.FormUrlEncoded
import retrofit2.http.POST
import retrofit2.http.Field
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.Header
import retrofit2.http.PUT
import retrofit2.http.DELETE
import retrofit2.http.Path
import retrofit2.http.PATCH
import retrofit2.http.HTTP
import retrofit2.http.Query
import retrofit2.Response
import okhttp3.ResponseBody

// 简化版API响应包装类
class ApiResponseWrapper<T>(
    val code: Int = 0,
    val isSuccessful: Boolean = false,
    val data: T? = null,
    val message: String? = null,
    val msg: String? = null,
    val errorMessage: String? = null
)

/**
 * API服务接口
 * 完全简化版本，不依赖Retrofit
 */

// 认证相关接口
interface ApiService {
    
    //用户注册
    @POST("register")
    suspend fun register(@Body request: RegisterRequest): RegisterResponse

    //用户登录
    @POST("login")
    suspend fun login(@Body request: LoginRequest): LoginResponse

    // 家庭成员相关接口
    // 添加账号下家庭成员
    @POST("add-member")
    suspend fun addMember(
        @Header("Authorization") token: String,
        @Body request: AddMemberRequest
    ): AddMemberResponse

    // 切换家庭成员
    @POST("switch-member")
    suspend fun switchMember(
        @Header("Authorization") token: String,
        @Body request: SwitchMemberRequest
    ): SwitchMemberResponse

    // 创建个人信息
    @POST("person/user")
    suspend fun createPersonInfo(
        @Header("Authorization") token: String,
        @Body request: PersonInfoRequest
    ): PersonInfoResponse

    // 删除个人信息
    @DELETE("person/{defaultId}")
    suspend fun deletePersonInfo(
        @Header("Authorization") token: String,
        @Path("defaultId") defaultId: String
    ): BaseResponse

    // 删除家庭关系
    @DELETE("family/{defaultId}")
    suspend fun deleteFamilyRelation(
        @Header("Authorization") token: String,
        @Path("defaultId") defaultId: String
    ): DeleteFamilyResponse

    // 删除家庭成员
    @DELETE("family/member/{defaultId}")
    suspend fun deleteFamilyMember(
        @Header("Authorization") token: String,
        @Path("defaultId") defaultId: String
    ): DeleteFamilyResponse

    // 更新个人信息
    @PUT("person/{defaultId}")
    suspend fun updatePersonInfo(
        @Header("Authorization") token: String,
        @Path("defaultId") defaultId: String,
        @Body request: PersonInfoRequest
    ): PersonInfoResponse

    // 获取个人信息
    @GET("person/{defaultId}")
    suspend fun getPersonInfo(
        @Header("Authorization") token: String,
        @Path("defaultId") defaultId: String
    ): PersonInfoResponse

    // 家庭关系相关接口 - 新接口格式
    // 设置家庭关系 - 返回纯文本
    @POST("family-relationships/set")
    suspend fun setFamilyRelationship(
        @Header("Authorization") token: String,
        @Body request: SetFamilyRelationshipRequest
    ): retrofit2.Response<okhttp3.ResponseBody>

    // 获取当前用户存在的所有关系 - 返回原始JSON对象
    @GET("family-relationships/member/{defaultId}")
    suspend fun getAllRelationships(
        @Header("Authorization") token: String,
        @Path("defaultId") defaultId: String
    ): retrofit2.Response<okhttp3.ResponseBody>

    // 保持向后兼容的旧接口（标记为废弃）
    @Deprecated("使用新的家庭关系接口")
    @POST("family/user")
    suspend fun createFamilyRelation(
        @Header("Authorization") token: String,
        @Body request: CreateFamilyRequest
    ): CreateFamilyResponse

    @Deprecated("使用新的家庭关系接口")
    @PUT("family/{defaultId}")
    suspend fun updateFamilyRelation(
        @Header("Authorization") token: String,
        @Path("defaultId") defaultId: String,
        @Body request: UpdateFamilyRequest
    ): UpdateFamilyResponse

    @Deprecated("使用新的家庭关系接口")
    @GET("family/{defaultId}")
    suspend fun getFamilyRelation(
        @Header("Authorization") token: String,
        @Path("defaultId") defaultId: String
    ): GetFamilyResponse

    //获取所有家庭成员信息
    @GET("family/account/{account}")
    suspend fun getAllFamilyMembers(
        @Header("Authorization") token: String,
        @Path("account") account: String
    ): GetAllFamilyMembersResponse

    // 健康信息相关接口
    // 创建健康信息
    @POST("health/create")
    suspend fun createHealthInfo(
        @Header("Authorization") token: String,
        @Body request: CreateHealthRequest
    ): BaseResponse

    // 获取健康信息
    @GET("health/{defaultId}")
    suspend fun getHealthInfo(
        @Header("Authorization") token: String,
        @Path("defaultId") defaultId: String
    ): HealthInfoResponse

    //删除健康信息
    @DELETE("health/{healthId}")
    suspend fun deleteHealthInfo(
        @Header("Authorization") token: String,
        @Path("healthId") healthId: Int
    ): BaseResponse

    // 更新健康信息
    @PUT("health")
    suspend fun updateHealthInfo(
        @Header("Authorization") token: String,
        @Body request: UpdateHealthRequest
    ): BaseResponse

    // 手术史记录相关接口
    @POST("health/surgery")
    suspend fun createSurgeryHistory(
        @Header("Authorization") token: String,
        @Body request: CreateSurgeryRequest
    ): BaseResponse

    @DELETE("health/surgery/{surgeryId}")
    suspend fun deleteSurgeryHistory(
        @Header("Authorization") token: String,
        @Path("surgeryId") surgeryId: Int
    ): BaseResponse

    @PUT("health/surgery")
    suspend fun updateSurgeryHistory(
        @Header("Authorization") token: String,
        @Body request: UpdateSurgeryRequest
    ): BaseResponse

    @GET("health/surgery/{healthId}")
    suspend fun getSurgeryHistories(
        @Header("Authorization") token: String,
        @Path("healthId") healthId: Int
    ): SurgeryHistoryListResponse

    //用药计划相关接口
    //创建用药计划 - 返回包装响应
    @POST("mplans")
    suspend fun createMedicationPlan(
        @Header("Authorization") token: String,
        @Body request: MedicationPlanRequest
    ): MedicationPlanResponse  // 返回包装响应

    @DELETE("mplans/{mplanId}")
    suspend fun deleteMedicationPlan(
        @Header("Authorization") token: String,
        @Path("mplanId") mplanId: Int
    ): BaseResponse

    //更新用药计划 - 返回包装响应
    @PUT("mplans/{mplanId}")
    suspend fun updateMedicationPlan(
        @Header("Authorization") token: String,
        @Path("mplanId") mplanId: Int,
        @Body request: MedicationPlanRequest
    ): MedicationPlanResponse  // 返回包装响应

    //获取用药计划 - 直接返回对象
    @GET("mplans/{mplanId}")
    suspend fun getMedicationPlan(
        @Header("Authorization") token: String,
        @Path("mplanId") mplanId: Int
    ): MedicationPlanDetail  // 直接返回对象

    //获取用户所有用药计划
    @GET("mplans/user/{defaultId}")
    suspend fun getMedicationPlans(
        @Header("Authorization") token: String,
        @Path("defaultId") defaultId: String
    ): MedicationPlanListResponse

    //资料夹相关接口
    @POST("folder")
    suspend fun createFolder(
        @Header("Authorization") token: String,
        @Body request: CreateFolderRequest
    ): StandardFolderResponse

    @DELETE("folder/{folderId}")
    suspend fun deleteFolder(
        @Header("Authorization") token: String,
        @Path("folderId") folderId: Int
    ): FolderDeleteResponse

    @DELETE("folder/user/{defaultId}")
    suspend fun deleteFolderByUserId(
        @Header("Authorization") token: String,
        @Path("defaultId") defaultId: String
    ): FolderDeleteResponse

    @PUT("folder/{folderId}")
    suspend fun updateFolder(
        @Header("Authorization") token: String,
        @Path("folderId") folderId: Int,
        @Body request: CreateFolderRequest
    ): StandardFolderResponse

    @GET("folder/{folderId}")
    suspend fun getFolder(
        @Header("Authorization") token: String,
        @Path("folderId") folderId: Int
    ): StandardFolderResponse

    @GET("folder/user/{defaultId}")
    suspend fun getFoldersByUserId(
        @Header("Authorization") token: String,
        @Path("defaultId") defaultId: String
    ): StandardFolderResponse

    // AI相关接口 - 暂未实现
//    @GET("ai/data")
//    suspend fun getAiData(@Header("Authorization") token: String): AiDataResponse

}



