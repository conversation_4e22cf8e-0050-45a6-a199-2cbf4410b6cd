package com.example.aihealthbutler

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import com.example.aihealthbutler.speech.SpeechEngineType

/**
 * 语音识别引擎选择对话框
 */
@Composable
fun SpeechEngineSelectionDialog(
    speechToTextManager: SpeechToTextManager,
    onDismiss: () -> Unit
) {
    var currentEngineType by remember { mutableStateOf(speechToTextManager.getCurrentEngineType()) }
    val availableEngines by speechToTextManager.currentEngineInfo.collectAsState()
    val engineStatus = speechToTextManager.getAvailableEngines()

    Dialog(onDismissRequest = onDismiss) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = Color.White
            )
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(20.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // 标题
                Text(
                    text = "选择语音识别引擎",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF2C3E50),
                    modifier = Modifier.padding(bottom = 16.dp)
                )

                // 当前引擎信息
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 16.dp),
                    shape = RoundedCornerShape(8.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = Color(0xFFF8F9FA)
                    )
                ) {
                    Column(
                        modifier = Modifier.padding(12.dp)
                    ) {
                        Text(
                            text = "当前引擎:",
                            fontSize = 12.sp,
                            color = Color(0xFF6C757D),
                            fontWeight = FontWeight.Medium
                        )
                        Text(
                            text = availableEngines.ifEmpty { "未选择引擎" },
                            fontSize = 14.sp,
                            color = Color(0xFF2C3E50),
                            modifier = Modifier.padding(top = 4.dp)
                        )
                    }
                }

                // 引擎列表
                LazyColumn(
                    modifier = Modifier.fillMaxWidth(),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    items(getEngineDisplayList()) { engineInfo ->
                        EngineSelectionItem(
                            engineInfo = engineInfo,
                            isSelected = engineInfo.type == currentEngineType,
                            isAvailable = engineStatus[engineInfo.type] == true,
                            onSelect = {
                                if (speechToTextManager.switchEngine(engineInfo.type)) {
                                    currentEngineType = engineInfo.type
                                }
                            }
                        )
                    }
                }

                Spacer(modifier = Modifier.height(16.dp))

                // 诊断信息按钮
                OutlinedButton(
                    onClick = {
                        // 显示详细诊断信息
                    },
                    modifier = Modifier.fillMaxWidth(),
                    colors = ButtonDefaults.outlinedButtonColors(
                        contentColor = Color(0xFF2196F3)
                    )
                ) {
                    Text("查看引擎诊断信息")
                }

                Spacer(modifier = Modifier.height(12.dp))

                // 关闭按钮
                Button(
                    onClick = onDismiss,
                    modifier = Modifier.fillMaxWidth(),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = Color(0xFF2196F3)
                    ),
                    shape = RoundedCornerShape(8.dp)
                ) {
                    Text(
                        text = "确定",
                        color = Color.White,
                        fontSize = 14.sp
                    )
                }
            }
        }
    }
}

/**
 * 引擎选择项
 */
@Composable
private fun EngineSelectionItem(
    engineInfo: EngineDisplayInfo,
    isSelected: Boolean,
    isAvailable: Boolean,
    onSelect: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth(),
        shape = RoundedCornerShape(8.dp),
        colors = CardDefaults.cardColors(
            containerColor = when {
                isSelected -> Color(0xFFE3F2FD)
                isAvailable -> Color.White
                else -> Color(0xFFF5F5F5)
            }
        ),
        border = if (isSelected) {
            androidx.compose.foundation.BorderStroke(2.dp, Color(0xFF2196F3))
        } else null
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 状态图标
            Text(
                text = when {
                    isSelected -> "✅"
                    isAvailable -> "🟢"
                    else -> "🔴"
                },
                fontSize = 16.sp,
                modifier = Modifier.padding(end = 12.dp)
            )

            // 引擎信息
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = engineInfo.name,
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                    color = if (isAvailable) Color(0xFF2C3E50) else Color(0xFF6C757D)
                )
                Text(
                    text = engineInfo.description,
                    fontSize = 12.sp,
                    color = Color(0xFF6C757D),
                    modifier = Modifier.padding(top = 2.dp)
                )
                Text(
                    text = when {
                        isSelected -> "当前使用"
                        isAvailable -> "可用"
                        else -> "不可用"
                    },
                    fontSize = 11.sp,
                    color = when {
                        isSelected -> Color(0xFF2196F3)
                        isAvailable -> Color(0xFF4CAF50)
                        else -> Color(0xFFFF5722)
                    },
                    modifier = Modifier.padding(top = 2.dp)
                )
            }

            // 选择按钮
            if (isAvailable && !isSelected) {
                TextButton(
                    onClick = onSelect,
                    colors = ButtonDefaults.textButtonColors(
                        contentColor = Color(0xFF2196F3)
                    )
                ) {
                    Text(
                        text = "选择",
                        fontSize = 12.sp
                    )
                }
            }
        }
    }
}

/**
 * 引擎显示信息
 */
private data class EngineDisplayInfo(
    val type: SpeechEngineType,
    val name: String,
    val description: String
)

/**
 * 获取引擎显示列表
 */
private fun getEngineDisplayList(): List<EngineDisplayInfo> {
    return listOf(
        EngineDisplayInfo(
            type = SpeechEngineType.GOOGLE,
            name = "Google语音识别",
            description = "Google语音服务，需要网络连接，识别准确率高"
        ),
        EngineDisplayInfo(
            type = SpeechEngineType.XUNFEI,
            name = "讯飞语音识别",
            description = "科大讯飞语音服务，中文识别效果好，支持离线"
        ),
        EngineDisplayInfo(
            type = SpeechEngineType.VOSK,
            name = "Vosk离线识别",
            description = "开源离线语音识别，完全本地处理，保护隐私"
        ),
        EngineDisplayInfo(
            type = SpeechEngineType.BAIDU,
            name = "百度语音识别",
            description = "百度语音服务，支持多种语言和方言"
        )
    )
}
