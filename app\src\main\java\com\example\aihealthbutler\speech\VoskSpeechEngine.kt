package com.example.aihealthbutler.speech

import android.content.Context
import android.media.AudioFormat
import android.media.AudioRecord
import android.media.MediaRecorder
import android.util.Log
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import org.json.JSONObject
import java.io.File
import java.io.IOException

/**
 * Vosk离线语音识别引擎
 * 完全离线，无需网络连接
 */
class VoskSpeechEngine(
    private val context: Context,
    private val config: SpeechRecognitionConfig = SpeechRecognitionConfig()
) : SpeechRecognitionEngine {

    companion object {
        private const val TAG = "VoskSpeechEngine"
        private const val SAMPLE_RATE = 16000
        private const val BUFFER_SIZE = 4096
        
        // 模型文件路径
        private const val MODEL_PATH = "vosk-model-small-cn-0.22" // 中文小模型
    }

    // 状态流
    private val _state = MutableStateFlow(SpeechToTextState.IDLE)
    override val state: StateFlow<SpeechToTextState> = _state

    private val _result = MutableStateFlow(SpeechToTextResult())
    override val result: StateFlow<SpeechToTextResult> = _result

    private val _errorMessage = MutableStateFlow("")
    override val errorMessage: StateFlow<String> = _errorMessage

    // Vosk相关
    private var model: Any? = null // 实际类型为Model
    private var recognizer: Any? = null // 实际类型为KaldiRecognizer
    private var audioRecord: AudioRecord? = null
    private var isInitialized = false
    private var isListeningFlag = false
    private var recordingJob: Job? = null

    override suspend fun initialize(): Boolean = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Initializing Vosk Speech Engine")
            _state.value = SpeechToTextState.INITIALIZING

            // 检查模型文件是否存在
            val modelDir = File(context.filesDir, MODEL_PATH)
            if (!modelDir.exists()) {
                _errorMessage.value = "Vosk模型文件不存在\n" +
                        "请下载中文模型文件:\n" +
                        "1. 从 https://alphacephei.com/vosk/models 下载\n" +
                        "2. 解压到 ${context.filesDir}/$MODEL_PATH"
                _state.value = SpeechToTextState.ERROR
                return@withContext false
            }

            // 初始化Vosk模型
            // 注意：这里需要实际的Vosk SDK集成
            // LibVosk.setLogLevel(LogLevel.WARNINGS)
            // model = Model(modelDir.absolutePath)
            // recognizer = KaldiRecognizer(model, SAMPLE_RATE.toFloat())

            // 模拟初始化
            model = "mock_model"
            recognizer = "mock_recognizer"

            isInitialized = true
            _state.value = SpeechToTextState.IDLE
            Log.d(TAG, "Vosk Speech Engine initialized successfully")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Failed to initialize Vosk Speech Engine", e)
            _errorMessage.value = "Vosk语音引擎初始化失败: ${e.message}"
            _state.value = SpeechToTextState.ERROR
            false
        }
    }

    override fun startListening() {
        if (!isInitialized) {
            _errorMessage.value = "Vosk语音引擎未初始化"
            _state.value = SpeechToTextState.ERROR
            return
        }

        if (isListeningFlag) {
            Log.d(TAG, "Already listening")
            return
        }

        try {
            Log.d(TAG, "Starting Vosk speech recognition")
            
            // 初始化AudioRecord
            val bufferSize = AudioRecord.getMinBufferSize(
                SAMPLE_RATE,
                AudioFormat.CHANNEL_IN_MONO,
                AudioFormat.ENCODING_PCM_16BIT
            )

            audioRecord = AudioRecord(
                MediaRecorder.AudioSource.MIC,
                SAMPLE_RATE,
                AudioFormat.CHANNEL_IN_MONO,
                AudioFormat.ENCODING_PCM_16BIT,
                bufferSize
            )

            if (audioRecord?.state != AudioRecord.STATE_INITIALIZED) {
                _errorMessage.value = "音频录制器初始化失败"
                _state.value = SpeechToTextState.ERROR
                return
            }

            _state.value = SpeechToTextState.LISTENING
            isListeningFlag = true
            _result.value = SpeechToTextResult()
            _errorMessage.value = ""

            // 开始录音
            audioRecord?.startRecording()

            // 启动识别协程
            recordingJob = CoroutineScope(Dispatchers.IO).launch {
                processAudio()
            }

        } catch (e: Exception) {
            Log.e(TAG, "Failed to start listening", e)
            _errorMessage.value = "开始语音识别失败: ${e.message}"
            _state.value = SpeechToTextState.ERROR
            isListeningFlag = false
        }
    }

    override fun stopListening() {
        if (!isListeningFlag) return

        try {
            Log.d(TAG, "Stopping Vosk speech recognition")
            isListeningFlag = false
            recordingJob?.cancel()
            audioRecord?.stop()
            _state.value = SpeechToTextState.PROCESSING

            // 获取最终结果
            // val finalResult = recognizer?.getFinalResult()
            // processFinalResult(finalResult)
            
            // 模拟最终结果
            simulateFinalResult()

        } catch (e: Exception) {
            Log.e(TAG, "Failed to stop listening", e)
            _errorMessage.value = "停止语音识别失败: ${e.message}"
            _state.value = SpeechToTextState.ERROR
        }
    }

    override fun cancelListening() {
        if (!isListeningFlag) return

        try {
            Log.d(TAG, "Cancelling Vosk speech recognition")
            isListeningFlag = false
            recordingJob?.cancel()
            audioRecord?.stop()
            audioRecord?.release()
            audioRecord = null
            _state.value = SpeechToTextState.IDLE
            _result.value = SpeechToTextResult()
        } catch (e: Exception) {
            Log.e(TAG, "Failed to cancel listening", e)
        }
    }

    override fun isAvailable(): Boolean {
        return isInitialized && model != null && recognizer != null
    }

    override fun isListening(): Boolean {
        return isListeningFlag
    }

    override fun release() {
        try {
            cancelListening()
            // model?.close()
            // recognizer?.close()
            model = null
            recognizer = null
            isInitialized = false
            _state.value = SpeechToTextState.IDLE
        } catch (e: Exception) {
            Log.e(TAG, "Failed to release resources", e)
        }
    }

    override fun reset() {
        cancelListening()
        _result.value = SpeechToTextResult()
        _errorMessage.value = ""
        _state.value = SpeechToTextState.IDLE
    }

    override fun getEngineName(): String = "Vosk离线识别"

    override fun getEngineDescription(): String = "Vosk开源离线语音识别引擎，无需网络连接"

    /**
     * 处理音频数据
     */
    private suspend fun processAudio() = withContext(Dispatchers.IO) {
        val buffer = ShortArray(BUFFER_SIZE)
        
        while (isListeningFlag && audioRecord?.recordingState == AudioRecord.RECORDSTATE_RECORDING) {
            try {
                val bytesRead = audioRecord?.read(buffer, 0, buffer.size) ?: 0
                
                if (bytesRead > 0) {
                    // 将音频数据发送给Vosk识别器
                    // val result = recognizer?.acceptWaveForm(buffer, bytesRead)
                    // if (result == true) {
                    //     val partialResult = recognizer?.getResult()
                    //     processPartialResult(partialResult)
                    // } else {
                    //     val partialResult = recognizer?.getPartialResult()
                    //     processPartialResult(partialResult)
                    // }
                    
                    // 模拟处理过程
                    delay(100)
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error processing audio", e)
                break
            }
        }
    }

    /**
     * 处理部分识别结果
     */
    private fun processPartialResult(result: String?) {
        if (result.isNullOrEmpty()) return
        
        try {
            val json = JSONObject(result)
            val text = json.optString("partial", "")
            
            if (text.isNotEmpty()) {
                _result.value = SpeechToTextResult(
                    text = text,
                    confidence = 0.8f,
                    isPartial = true
                )
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error parsing partial result", e)
        }
    }

    /**
     * 处理最终识别结果
     */
    private fun processFinalResult(result: String?) {
        if (result.isNullOrEmpty()) {
            _state.value = SpeechToTextState.ERROR
            _errorMessage.value = "未识别到语音内容"
            return
        }
        
        try {
            val json = JSONObject(result)
            val text = json.optString("text", "")
            
            if (text.isNotEmpty()) {
                _result.value = SpeechToTextResult(
                    text = text,
                    confidence = 0.9f,
                    isPartial = false
                )
                _state.value = SpeechToTextState.SUCCESS
            } else {
                _state.value = SpeechToTextState.ERROR
                _errorMessage.value = "未识别到有效语音内容"
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error parsing final result", e)
            _errorMessage.value = "解析识别结果失败"
            _state.value = SpeechToTextState.ERROR
        }
    }

    /**
     * 模拟最终结果（仅用于演示）
     */
    private fun simulateFinalResult() {
        _result.value = SpeechToTextResult(
            text = "这是Vosk离线识别的模拟结果",
            confidence = 0.85f,
            isPartial = false
        )
        _state.value = SpeechToTextState.SUCCESS
    }
}
