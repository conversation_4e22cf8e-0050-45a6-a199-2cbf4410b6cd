package com.example.aihealthbutler

import android.os.Bundle
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import androidx.lifecycle.viewmodel.compose.viewModel
import com.example.aihealthbutler.api.FamilyMemberData
import com.example.aihealthbutler.ui.components.GenderSelectionDialog
import com.example.aihealthbutler.ui.theme.AIHealthButlerTheme
import java.text.SimpleDateFormat
import java.util.*
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.launch
import com.example.aihealthbutler.api.UpdateFamilyRequest
import com.example.aihealthbutler.api.SetFamilyRelationshipRequest
import com.example.aihealthbutler.api.SessionManager
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import android.util.Log
import com.example.aihealthbutler.api.PersonInfoRequest
import com.example.aihealthbutler.api.Repository
import com.example.aihealthbutler.ui.components.GenderSelectionDialog

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun EditFamilyMemberScreen(
    memberData: FamilyMemberData
) {
    val context = LocalContext.current
    val scrollState = rememberScrollState()
    
    // 创建ViewModel实例
    val sessionManager = remember { SessionManager(context) }
    val repository = remember { Repository() }
    val viewModel = remember { EditFamilyMemberViewModel(repository, sessionManager) }
    
    var nickname by remember { mutableStateOf(memberData.name ?: "") }
    var gender by remember { mutableStateOf(when(memberData.sex) {
        1 -> "男"
        0 -> "女"
        else -> "男"
    }) }
    var birthdate by remember { mutableStateOf(memberData.birthday ?: "") }
    var height by remember { mutableStateOf(memberData.height.toString()) }
    var weight by remember { mutableStateOf(memberData.weight.toString()) }
    var relationship by remember { mutableStateOf(memberData.role ?: "") }
    
    var showGenderDialog by remember { mutableStateOf(false) }
    var showRelationshipDialog by remember { mutableStateOf(false) }
    var showDatePickerDialog by remember { mutableStateOf(false) }
    
    val savingState by viewModel.savingState.collectAsState()
    
    LaunchedEffect(savingState) {
        when (val state = savingState) {
            is EditFamilyMemberViewModel.SavingState.Success -> {
                Toast.makeText(context, state.message, Toast.LENGTH_SHORT).show()
                (context as? ComponentActivity)?.finish()
            }
            is EditFamilyMemberViewModel.SavingState.Error -> {
                Toast.makeText(context, state.message, Toast.LENGTH_SHORT).show()
            }
            else -> {}
        }
    }
    
    Scaffold(
        topBar = {
            CenterAlignedTopAppBar(
                title = { Text("编辑家庭成员", fontSize = 20.sp, fontWeight = FontWeight.Bold) },
                navigationIcon = {
                    IconButton(onClick = { (context as? ComponentActivity)?.finish() }) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                    }
                },
                colors = TopAppBarDefaults.centerAlignedTopAppBarColors(
                    containerColor = Color(0xFFF5F6F7)
                )
            )
        },
        containerColor = Color(0xFFF5F6F7)
    ) { innerPadding ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(innerPadding)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(horizontal = 24.dp)
                    .background(Color(0xFFF5F6F7))
            ) {
                Column(
                    modifier = Modifier
                        .weight(1f)
                        .verticalScroll(scrollState)
                ) {
                    // 与本人的家庭关系
                    Text(
                        text = "与本人的家庭关系",
                        color = Color.Gray,
                        fontSize = 15.sp,
                        modifier = Modifier.padding(top = 16.dp, bottom = 8.dp)
                    )
                    Box(modifier = Modifier.fillMaxWidth()) {
                        OutlinedTextField(
                            value = relationship,
                            onValueChange = { },
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(52.dp),
                            shape = RoundedCornerShape(8.dp),
                            colors = OutlinedTextFieldDefaults.colors(
                                unfocusedBorderColor = Color.LightGray,
                                focusedBorderColor = Color(0xFF2EB0AC),
                                unfocusedContainerColor = Color.White,
                                focusedContainerColor = Color.White
                            ),
                            trailingIcon = {
                                Icon(
                                    painter = painterResource(id = R.drawable.ic_dropdown),
                                    contentDescription = "选择关系",
                                    tint = Color.Gray,
                                    modifier = Modifier.size(18.dp)
                                )
                            },
                            readOnly = true,
                            singleLine = true
                        )
                        
                        Box(
                            modifier = Modifier
                                .matchParentSize()
                                .clickable { showRelationshipDialog = true }
                        )
                    }

                    // 昵称
                    Text(
                        text = "昵称",
                        color = Color.Gray,
                        fontSize = 15.sp,
                        modifier = Modifier.padding(top = 16.dp, bottom = 8.dp)
                    )
                    OutlinedTextField(
                        value = nickname,
                        onValueChange = { nickname = it },
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(52.dp),
                        shape = RoundedCornerShape(8.dp),
                        colors = OutlinedTextFieldDefaults.colors(
                            unfocusedBorderColor = Color.LightGray,
                            focusedBorderColor = Color(0xFF2EB0AC),
                            unfocusedContainerColor = Color.White,
                            focusedContainerColor = Color.White
                        ),
                        singleLine = true
                    )

                    // 性别
                    Text(
                        text = "性别",
                        color = Color.Gray,
                        fontSize = 15.sp,
                        modifier = Modifier.padding(top = 16.dp, bottom = 8.dp)
                    )
                    Box(modifier = Modifier.fillMaxWidth()) {
                        OutlinedTextField(
                            value = gender,
                            onValueChange = { },
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(52.dp),
                            shape = RoundedCornerShape(8.dp),
                            colors = OutlinedTextFieldDefaults.colors(
                                unfocusedBorderColor = Color.LightGray,
                                focusedBorderColor = Color(0xFF2EB0AC),
                                unfocusedContainerColor = Color.White,
                                focusedContainerColor = Color.White
                            ),
                            trailingIcon = {
                                Icon(
                                    painter = painterResource(id = R.drawable.ic_dropdown),
                                    contentDescription = "选择性别",
                                    tint = Color.Gray,
                                    modifier = Modifier.size(18.dp)
                                )
                            },
                            readOnly = true,
                            singleLine = true
                        )
                        
                        Box(
                            modifier = Modifier
                                .matchParentSize()
                                .clickable { showGenderDialog = true }
                        )
                    }

                    // 出生日期
                    Text(
                        text = "出生日期",
                        color = Color.Gray,
                        fontSize = 15.sp,
                        modifier = Modifier.padding(top = 16.dp, bottom = 8.dp)
                    )
                    Box(modifier = Modifier.fillMaxWidth()) {
                        OutlinedTextField(
                            value = birthdate,
                            onValueChange = { },
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(52.dp),
                            shape = RoundedCornerShape(8.dp),
                            colors = OutlinedTextFieldDefaults.colors(
                                unfocusedBorderColor = Color.LightGray,
                                focusedBorderColor = Color(0xFF2EB0AC),
                                unfocusedContainerColor = Color.White,
                                focusedContainerColor = Color.White
                            ),
                            readOnly = true,
                            singleLine = true
                        )
                        
                        Box(
                            modifier = Modifier
                                .matchParentSize()
                                .clickable { showDatePickerDialog = true }
                        )
                    }

                    // 身高和体重
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(16.dp)
                    ) {
                        Column(modifier = Modifier.weight(1f)) {
                            Text(
                                text = "身高(cm)",
                                color = Color.Gray,
                                fontSize = 15.sp,
                                modifier = Modifier.padding(top = 16.dp, bottom = 8.dp)
                            )
                            OutlinedTextField(
                                value = height,
                                onValueChange = { height = it },
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .height(52.dp),
                                shape = RoundedCornerShape(8.dp),
                                colors = OutlinedTextFieldDefaults.colors(
                                    unfocusedBorderColor = Color.LightGray,
                                    focusedBorderColor = Color(0xFF2EB0AC),
                                    unfocusedContainerColor = Color.White,
                                    focusedContainerColor = Color.White
                                ),
                                singleLine = true
                            )
                        }

                        Column(modifier = Modifier.weight(1f)) {
                            Text(
                                text = "体重(kg)",
                                color = Color.Gray,
                                fontSize = 15.sp,
                                modifier = Modifier.padding(top = 16.dp, bottom = 8.dp)
                            )
                            OutlinedTextField(
                                value = weight,
                                onValueChange = { weight = it },
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .height(52.dp),
                                shape = RoundedCornerShape(8.dp),
                                colors = OutlinedTextFieldDefaults.colors(
                                    unfocusedBorderColor = Color.LightGray,
                                    focusedBorderColor = Color(0xFF2EB0AC),
                                    unfocusedContainerColor = Color.White,
                                    focusedContainerColor = Color.White
                                ),
                                singleLine = true
                            )
                        }
                    }
                }

                // 保存按钮
                Button(
                    onClick = {
                        viewModel.updateFamilyMember(
                            defaultId = memberData.defaultId,
                            nickname = nickname,
                            gender = gender,
                            birthdate = birthdate,
                            heightValue = height.toIntOrNull() ?: 0,
                            weightValue = weight.toFloatOrNull() ?: 0.0f,
                            relationship = relationship
                        )
                    },
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 16.dp)
                        .height(48.dp),
                    shape = RoundedCornerShape(8.dp),
                    colors = ButtonDefaults.buttonColors(containerColor = Color(0xFF2EB0AC)),
                    enabled = savingState !is EditFamilyMemberViewModel.SavingState.Loading
                ) {
                    val currentState = savingState
                    if (currentState is EditFamilyMemberViewModel.SavingState.Loading) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(20.dp),
                            color = Color.White
                        )
                    } else {
                        Text("保存修改", fontSize = 16.sp)
                    }
                }
            }
        }
    }

    // 对话框代码与AddNewFamilyMember相同...
    if (showRelationshipDialog) {
        Dialog(onDismissRequest = { showRelationshipDialog = false }) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(Color.White, RoundedCornerShape(8.dp))
                    .padding(16.dp)
            ) {
                Text("选择关系", fontWeight = FontWeight.Bold, fontSize = 16.sp)
                
                val relationships = listOf("父亲", "母亲", "配偶", "子女", "兄弟", "姐妹", "爷爷/外公", "奶奶/外婆", "其他")
                relationships.forEach { rel ->
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .clickable {
                                relationship = rel
                                showRelationshipDialog = false
                            }
                            .padding(vertical = 12.dp),
                        horizontalArrangement = Arrangement.Center
                    ) {
                        Text(rel, fontSize = 15.sp)
                    }
                }
            }
        }
    }

    if (showGenderDialog) {
        GenderSelectionDialog(
            onDismiss = { showGenderDialog = false },
            onGenderSelected = { selectedGender ->
                gender = selectedGender
                showGenderDialog = false
            }
        )
    }
}

class EditFamilyMemberActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        val memberData = intent.getSerializableExtra("memberData") as? FamilyMemberData
            ?: return finish()

        setContent {
            AIHealthButlerTheme {
                EditFamilyMemberScreen(memberData = memberData)
            }
        }
    }
}

class EditFamilyMemberViewModel(
    private val repository: Repository,
    private val sessionManager: SessionManager
) : ViewModel() {

    private val _savingState = MutableStateFlow<SavingState>(SavingState.Idle)
    val savingState: StateFlow<SavingState> = _savingState.asStateFlow()

    fun updateFamilyMember(
        defaultId: String,
        nickname: String,
        gender: String,
        birthdate: String,
        heightValue: Int,
        weightValue: Float,
        relationship: String
    ) {
        viewModelScope.launch {
            try {
                _savingState.value = SavingState.Loading

                val token = sessionManager.token.firstOrNull() ?: throw Exception("未登录")

                // 1. 更新个人信息
                val personInfoRequest = PersonInfoRequest(
                    name = nickname,
                    sex = when(gender) {
                        "男" -> 1
                        "女" -> 0
                        else -> 1
                    },
                    birthday = birthdate,
                    height = heightValue,
                    weight = weightValue
                )

                val updatePersonResult = repository.updatePersonInfo(token, defaultId, personInfoRequest)
                if (updatePersonResult.isFailure) {
                    throw Exception(updatePersonResult.exceptionOrNull()?.message ?: "更新个人信息失败")
                }

                // 2. 更新家庭关系 - 使用新接口
                val currentDefaultId = sessionManager.defaultId.firstOrNull() ?: throw Exception("获取当前用户ID失败")

                val setRelationshipRequest = SetFamilyRelationshipRequest(
                    fromMemberId = defaultId,
                    toMemberId = currentDefaultId,
                    relationship = relationship
                )

                val setRelationshipResult = repository.setFamilyRelationship(token, setRelationshipRequest)

                if (setRelationshipResult.isFailure) {
                    throw Exception(setRelationshipResult.exceptionOrNull()?.message ?: "更新家庭关系失败")
                }

                _savingState.value = SavingState.Success("更新成功")

            } catch (e: Exception) {
                Log.e("EditFamilyMemberVM", "更新家庭成员失败", e)
                _savingState.value = SavingState.Error(e.message ?: "更新失败")
            }
        }
    }

    sealed class SavingState {
        object Idle : SavingState()
        object Loading : SavingState()
        data class Success(val message: String) : SavingState()
        data class Error(val message: String) : SavingState()
    }
}