package com.example.aihealthbutler

import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Check
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material.icons.filled.KeyboardArrowRight
import androidx.compose.material.icons.filled.MoreVert
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.lifecycle.viewmodel.compose.viewModel
import com.example.aihealthbutler.api.*
import com.example.aihealthbutler.api.FamilyMemberData
import com.example.aihealthbutler.ui.theme.AIHealthButlerTheme
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll

// ViewModel 定义
class FamilyMembersViewModel(
    private val repository: Repository,
    private val sessionManager: SessionManager
) : ViewModel() {
    private val _familyMembers = MutableStateFlow<List<FamilyMemberData>>(emptyList())
    val familyMembers: StateFlow<List<FamilyMemberData>> = _familyMembers
    
    private val _currentMember = MutableStateFlow<FamilyMemberData?>(null)
    val currentMember: StateFlow<FamilyMemberData?> = _currentMember
    
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading
    
    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error
    
    private val _successMessage = MutableStateFlow<String?>(null)
    val successMessage: StateFlow<String?> = _successMessage
    
    private val _medicationPlan = MutableStateFlow<MedicationPlanDetail?>(null)
    val medicationPlan: StateFlow<MedicationPlanDetail?> = _medicationPlan

    init {
        loadFamilyMembers()
        loadMedicationPlan()
    }
    // 加载家庭成员列表
    fun loadFamilyMembers() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                _error.value = null
                
                val token = sessionManager.token.firstOrNull() ?: throw Exception("未登录")
                val account = sessionManager.account.value ?: throw Exception("未登录")

                // 获取所有家庭成员
                val result = repository.getAllFamilyMembers(token, account)
                
                if (result.isSuccess) {
                    val response = result.getOrNull()
                    if (response?.code == 200 && response.data.isNotEmpty()) {
                        _familyMembers.value = response.data
                        sessionManager.defaultId.collectLatest { defaultId ->
                            if (defaultId != null) {
                                _currentMember.value = response.data.find { it.defaultId == defaultId }
                            } else {
                                _currentMember.value = response.data.firstOrNull()
                            }
                        }
                    } else {
                        _error.value = response?.message ?: "获取家庭成员列表失败"
                    }
                } else {
                    _error.value = result.exceptionOrNull()?.message ?: "获取家庭成员列表失败"
                }
            } catch (e: Exception) {
                _error.value = "获取家庭成员列表异常: ${e.message}"
                Log.e("FamilyMembersVM", "获取家庭成员列表异常", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    // 加载用药计划
    private fun loadMedicationPlan() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                _error.value = null

                // 获取token和defaultId
                val token = sessionManager.token.firstOrNull() ?: throw Exception("未登录")
                val defaultId = sessionManager.defaultId.firstOrNull() ?: throw Exception("未设置默认成员")

                // 获取用药计划列表
                val result = repository.getMedicationPlans(token, defaultId)

                if (result.isSuccess) {
                    val response = result.getOrNull()
                    if (response?.data != null && response.data.isNotEmpty()) {
                        _medicationPlan.value = response.data[0] // 显示第一个用药计划
                    }
                } else {
                    _error.value = result.exceptionOrNull()?.message ?: "获取用药计划失败"
                }
            } catch (e: Exception) {
                _error.value = "获取用药计划异常: ${e.message}"
                Log.e("FamilyMembersVM", "获取用药计划异常", e)
            } finally {
                _isLoading.value = false
            }
        }
    }
}

class MainViewModel(
    private val repository: Repository,
    private val sessionManager: SessionManager
) : ViewModel() {
    companion object {
        private const val TAG = "MainVM"
    }

    private val _currentMember = MutableStateFlow<PersonInfoData?>(null)
    val currentMember: StateFlow<PersonInfoData?> = _currentMember

    private val _familyMembers = MutableStateFlow<List<FamilyMemberData>>(emptyList())
    val familyMembers: StateFlow<List<FamilyMemberData>> = _familyMembers

    private val _isLoading = MutableStateFlow(true) // 初始状态为加载中
    val isLoading: StateFlow<Boolean> = _isLoading

    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error

    private val _medicationPlan = MutableStateFlow<MedicationPlanDetail?>(null)
    val medicationPlan: StateFlow<MedicationPlanDetail?> = _medicationPlan

    // 添加数据加载状态
    private val _dataLoaded = MutableStateFlow(false)
    val dataLoaded: StateFlow<Boolean> = _dataLoaded

    init {
        // 立即显示缓存数据，避免空白页面
        val cachedInfo = sessionManager.getCachedPersonInfo()
        if (cachedInfo != null) {
            _currentMember.value = cachedInfo
            _dataLoaded.value = true
            Log.d(TAG, "显示缓存的个人信息: ${cachedInfo.name}")
        }

        // 然后开始预加载最新数据
        Log.d(TAG, "ViewModel初始化，开始预加载数据")
        preloadData()
    }

    // 预加载数据，提升用户体验
    private fun preloadData() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                _error.value = null

                // 并发加载所有数据
                val memberJob = <EMAIL> { loadCurrentMemberInternal() }
                val medicationJob = <EMAIL> { loadMedicationPlanInternal() }

                // 等待所有任务完成
                memberJob.await()
                medicationJob.await()

                _dataLoaded.value = true
                Log.d(TAG, "预加载完成")

            } catch (e: Exception) {
                Log.e(TAG, "预加载失败", e)
                _error.value = "数据加载失败，请重试"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    // 公共加载方法（保持向后兼容）
    private fun loadCurrentMember() {
        viewModelScope.launch {
            _isLoading.value = true
            loadCurrentMemberInternal()
            _isLoading.value = false
        }
    }

    // 内部加载方法，用于预加载和刷新
    private suspend fun loadCurrentMemberInternal() {
        try {
            val token = sessionManager.token.firstOrNull() ?: throw Exception("未登录")
            val defaultId = sessionManager.defaultId.firstOrNull() ?: throw Exception("未设置默认成员")

            Log.d(TAG, "正在加载个人信息，defaultId: $defaultId")

            // 并发加载家庭成员和个人信息
            val familyJob = viewModelScope.async {
                val familyResult = repository.getAllFamilyMembers(token, sessionManager.account.value ?: "")
                familyResult.fold(
                    onSuccess = { response ->
                        if (response.code == 200) {
                            Log.d(TAG, "成功加载家庭成员: ${response.data.size}个")
                            // 异步加载关系信息
                            loadFamilyMembersWithRelations(token, defaultId, response.data)
                        }
                    },
                    onFailure = { e ->
                        Log.e(TAG, "加载家庭成员失败", e)
                        _familyMembers.value = emptyList()
                    }
                )
            }

            val personJob = viewModelScope.async {
                val personResult = repository.getPersonInfo(token, defaultId)
                personResult.fold(
                    onSuccess = { response ->
                        if (response.code == 200 && response.data != null) {
                            // 修复：account字段后端可能返回null，导致copy时报NPE
                            val safeAccount = response.data.account ?: sessionManager.account.value ?: ""
                            val personData = response.data.copy(
                                name = response.data.name ?: "未设置",
                                sexText = response.data.sexText ?: "未设置",
                                birthday = response.data.birthday ?: "未设置",
                                account = safeAccount
                            )
                            _currentMember.value = personData
                            // 缓存最新的个人信息
                            sessionManager.cachePersonInfo(personData)
                            Log.d(TAG, "成功加载个人信息: $personData")
                        } else {
                            _error.value = response.message
                            Log.e(TAG, "加载个人信息失败: ${response.message}")
                        }
                    },
                    onFailure = { e ->
                        _error.value = e.message ?: "获取个人信息失败"
                        Log.e(TAG, "加载个人信息异常", e)
                    }
                )
            }

            // 等待两个任务完成
            familyJob.await()
            personJob.await()

        } catch (e: Exception) {
            _error.value = "获取当前成员信息异常: ${e.message}"
            Log.e(TAG, "获取当前成员信息异常", e)
        }
    }

    // 公共加载方法（保持向后兼容）
    private fun loadMedicationPlan() {
        viewModelScope.launch {
            _isLoading.value = true
            loadMedicationPlanInternal()
            _isLoading.value = false
        }
    }

    // 内部加载方法，用于预加载
    private suspend fun loadMedicationPlanInternal() {
        try {
            val token = sessionManager.token.firstOrNull() ?: throw Exception("未登录")
            val defaultId = sessionManager.defaultId.firstOrNull() ?: throw Exception("未设置默认成员")

            val result = repository.getMedicationPlans(token, defaultId)

            result.fold(
                onSuccess = { response ->
                    if (response.code == 200 && response.data != null && response.data.isNotEmpty()) {
                        _medicationPlan.value = response.data[0]
                        Log.d(TAG, "成功加载用药计划")
                    }
                },
                onFailure = { e ->
                    Log.e(TAG, "获取用药计划失败", e)
                }
            )
        } catch (e: Exception) {
            Log.e(TAG, "获取用药计划异常", e)
        }
    }
    fun logout() {
        viewModelScope.launch {
            try {
                sessionManager.clearAll()
                sessionManager.clearPersonInfoCache()
                _currentMember.value = null
                _familyMembers.value = emptyList()
                _medicationPlan.value = null
                _dataLoaded.value = false
            } catch (e: Exception) {
                Log.e(TAG, "清除会话数据失败", e)
                _error.value = "退出登录失败: ${e.message}"
            }
        }
    }

    // 加载家庭成员及其关系信息
    private fun loadFamilyMembersWithRelations(token: String, currentDefaultId: String, members: List<FamilyMemberData>) {
        viewModelScope.launch {
            try {
                Log.d(TAG, "开始加载家庭成员关系信息")

                // 先显示基本信息
                val membersWithBasicInfo = members.map { member ->
                    if (member.defaultId == currentDefaultId) {
                        member.copy(role = "你")
                    } else {
                        member.copy(role = "加载中...")
                    }
                }
                _familyMembers.value = membersWithBasicInfo

                // 并发加载关系信息
                val updatedMembers = members.map { member ->
                    <EMAIL> {
                        if (member.defaultId == currentDefaultId) {
                            member.copy(role = "你")
                        } else {
                            try {
                                val relationshipResult = repository.getAllRelationships(token, member.defaultId)
                                relationshipResult.fold(
                                    onSuccess = { relationResponse ->
                                        if (relationResponse.code == 200 && relationResponse.data.isNotEmpty()) {
                                            val relationship = relationResponse.data[currentDefaultId]
                                            if (relationship != null) {
                                                member.copy(role = relationship)
                                            } else {
                                                member.copy(role = "未设置关系")
                                            }
                                        } else {
                                            member.copy(role = "未设置关系")
                                        }
                                    },
                                    onFailure = {
                                        member.copy(role = "未设置关系")
                                    }
                                )
                            } catch (e: Exception) {
                                Log.e(TAG, "获取成员${member.name}关系异常", e)
                                member.copy(role = "未设置关系")
                            }
                        }
                    }
                }.awaitAll()

                // 更新最终结果
                _familyMembers.value = updatedMembers
                Log.d(TAG, "家庭成员关系信息加载完成")

            } catch (e: Exception) {
                Log.e(TAG, "加载家庭成员关系异常", e)
                _familyMembers.value = members // 至少显示基本信息
            }
        }
    }

    // 刷新所有数据，确保实时性
    fun refreshData() {
        Log.d(TAG, "开始刷新所有数据")
        viewModelScope.launch {
            try {
                // 快速刷新，不显示全屏加载
                val memberJob = <EMAIL> { loadCurrentMemberInternal() }
                val medicationJob = <EMAIL> { loadMedicationPlanInternal() }

                // 等待所有任务完成
                memberJob.await()
                medicationJob.await()

                Log.d(TAG, "刷新完成")
            } catch (e: Exception) {
                Log.e(TAG, "刷新失败", e)
                _error.value = "刷新数据失败"
            }
        }
    }
}

class MainActivity : ComponentActivity() {
    private lateinit var viewModel: MainViewModel

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            AIHealthButlerTheme {
                val sessionManager = remember { SessionManager(applicationContext) }
                val repository = remember { Repository() }
                viewModel = remember { MainViewModel(repository, sessionManager) }

                MainScreen(
                    viewModel = viewModel,
                    onLogout = {
                        startActivity(Intent(this@MainActivity, SignInActivity::class.java))
                        finish()
                    }
                )
            }
        }
    }

    override fun onResume() {
        super.onResume()
        // 每次回到主页面时刷新数据，确保实时性
        if (::viewModel.isInitialized) {
            Log.d("MainActivity", "onResume: 刷新家庭成员数据")
            viewModel.refreshData()
        }
    }
}

@Stable
class MainScreenState(
    val drawerState: DrawerState,
    val scope: CoroutineScope
)

@Composable
fun rememberMainScreenState(
    drawerState: DrawerState = rememberDrawerState(DrawerValue.Closed),
    scope: CoroutineScope = rememberCoroutineScope()
) = remember(drawerState, scope) {
    MainScreenState(drawerState, scope)
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MainScreen(
    viewModel: MainViewModel,
    onLogout: () -> Unit
) {
    val context = LocalContext.current
    
    DisposableEffect(Unit) {
        val originalHandler = Thread.getDefaultUncaughtExceptionHandler()
        Thread.setDefaultUncaughtExceptionHandler { thread, exception ->
            Log.e("MainActivity", "未捕获的异常", exception)
            originalHandler?.uncaughtException(thread, exception)
        }
        
        onDispose {
            Thread.setDefaultUncaughtExceptionHandler(originalHandler)
        }
    }
    
    val scrollState = rememberScrollState()
    val state = rememberMainScreenState()
    
    val currentMember by viewModel.currentMember.collectAsState()
    val familyMembers by viewModel.familyMembers.collectAsState()
    val medicationPlan by viewModel.medicationPlan.collectAsState()
    val isLoading by viewModel.isLoading.collectAsState()
    val dataLoaded by viewModel.dataLoaded.collectAsState()
    val error by viewModel.error.collectAsState()
    
    LaunchedEffect(error) {
        error?.let {
            Toast.makeText(context, it, Toast.LENGTH_SHORT).show()
        }
    }
    
    ModalNavigationDrawer(
        drawerState = state.drawerState,
        gesturesEnabled = true,
        drawerContent = {
            ModalDrawerSheet(
                modifier = Modifier.width(230.dp),
                drawerContainerColor = Color.White
            ) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    contentAlignment = Alignment.CenterEnd
                ) {
                    IconButton(
                        onClick = {
                            state.scope.launch { state.drawerState.close() }
                        }
                    ) {
                        Icon(
                            painter = painterResource(id = R.drawable.ic_close),
                            contentDescription = "关闭",
                            tint = Color.Black,
                            modifier = Modifier.size(18.dp)
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(10.dp))
                
                MenuListItem(
                    iconRes = R.drawable.ic_person,
                    text = "我的订单"
                )
                MenuListItem(
                    iconRes = R.drawable.ic_settings,
                    text = "设置"
                )
                MenuListItem(
                    iconRes = R.drawable.ic_security,
                    text = "权限管理",
                    onClick = {
                        context.startActivity(
                            Intent(
                                context,
                                PermissionsManagerActivity::class.java
                            )
                        )
                        state.scope.launch { state.drawerState.close() }
                    }
                )
                MenuListItem(
                    iconRes = R.drawable.ic_qr_code,
                    text = "我的邀请码",
                    onClick = {
                        context.startActivity(
                            Intent(
                                context,
                                InviteQRCodeActivity::class.java
                            )
                        )
                        state.scope.launch { state.drawerState.close() }
                    }
                )
                MenuListItem(
                    iconRes = R.drawable.ic_scanning,
                    text = "扫一扫",
                    onClick = {
                        context.startActivity(
                            Intent(
                                context,
                                QRScannerActivity::class.java
                            )
                        )
                        state.scope.launch { state.drawerState.close() }
                    }
                )
                MenuListItem(iconRes = R.drawable.ic_customer_service, text = "联系客服")
                MenuListItem(iconRes = R.drawable.ic_feedback, text = "意见反馈")
                MenuListItem(iconRes = R.drawable.ic_help, text = "使用帮助")
                MenuListItem(iconRes = R.drawable.ic_list, text = "个人信息收集清单")
//              MenuListItem(iconRes = R.drawable.ic_share, text = "第三方共享清单")
                MenuListItem(iconRes = R.drawable.ic_privacy, text = "隐私保护摘要")
                MenuListItem(iconRes = R.drawable.ic_user_agreement, text = "用户行为公约")

                Spacer(modifier = Modifier.height(60.dp))

                // 退出登录按钮
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 12.dp, horizontal = 24.dp)
                        .clickable { onLogout() },
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        painter = painterResource(id = R.drawable.ic_logout),
                        contentDescription = "退出登录",
                        tint = Color(0xFF4285F4),
                        modifier = Modifier.size(30.dp)
                    )
                    
                    Spacer(modifier = Modifier.width(20.dp))
                    
                    Text(
                        text = "退出登录",
                        fontSize = 18.sp,
                        color = Color(0xFF4285F4)
                    )
                }
            }
        }
    ) {
        Box(modifier = Modifier.fillMaxSize()) {
            Image(
                painter = painterResource(id = R.drawable.minebackground),
                contentDescription = null,
                modifier = Modifier.fillMaxSize(),
                contentScale = ContentScale.FillBounds
            )
            
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .verticalScroll(scrollState)
            ) {
                TopAppBar(
                    title = {
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.Center
                        ) {
                            Text(
                                text = "对话",
                                fontSize = 16.sp,
                                color = Color.Gray,
                                modifier = Modifier
                                    .padding(end = 24.dp)
                                    .clickable {
                                        val intent = Intent(
                                            context,
                                            ConversationInterface::class.java
                                        )
                                        context.startActivity(intent)
                                    }
                            )
                            Text(
                                text = "我的",
                                fontSize = 16.sp,
                                fontWeight = FontWeight.Bold
                            )
                        }
                    },
                    actions = {
                        IconButton(
                            onClick = {
                                state.scope.launch { state.drawerState.open() }
                            }
                        ) {
                            Icon(
                                imageVector = Icons.Default.MoreVert,
                                contentDescription = "更多"
                            )
                        }
                    },
                    colors = TopAppBarDefaults.topAppBarColors(
                        containerColor = Color.Transparent
                    )
                )

                // 内容区域
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp)
                ) {
                    // 个人信息卡片
                    Card(
                        modifier = Modifier.fillMaxWidth(),
                        shape = RoundedCornerShape(12.dp),
                        colors = CardDefaults.cardColors(containerColor = Color.White)
                    ) {
                        Column(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(16.dp)
                        ) {
                            if (isLoading && !dataLoaded) {
                                // 首次加载时显示加载中
                                Box(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .height(120.dp),
                                    contentAlignment = Alignment.Center
                                ) {
                                    Column(horizontalAlignment = Alignment.CenterHorizontally) {
                                        CircularProgressIndicator(color = Color(0xFF2EB0AC))
                                        Spacer(modifier = Modifier.height(8.dp))
                                        Text(
                                            text = "正在加载个人信息...",
                                            fontSize = 14.sp,
                                            color = Color.Gray
                                        )
                                    }
                                }
                            } else if (currentMember != null) {
                                // 显示个人信息
                                // 昵称行
                                Row(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(start = 10.dp),
                                    horizontalArrangement = Arrangement.SpaceBetween,
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    Row(
                                        verticalAlignment = Alignment.CenterVertically
                                    ) {
                                        Text(
                                            text = currentMember?.name ?: "昵称",
                                            fontSize = 16.sp,
                                            fontWeight = FontWeight.Bold
                                        )

                                        Spacer(modifier = Modifier.width(8.dp))

                                        IconButton(
                                            onClick = {
                                                context.startActivity(
                                                    Intent(context, PersonalInformationActivity::class.java)
                                                )
                                            },
                                            modifier = Modifier.size(22.dp)
                                        ) {
                                            Icon(
                                                imageVector = Icons.Default.Edit,
                                                contentDescription = "编辑昵称",
                                                tint = Color.Gray,
                                                modifier = Modifier.size(22.dp)
                                            )
                                        }
                                    }

                                    // 切换成员按钮
                                    Row(
                                        verticalAlignment = Alignment.CenterVertically,
                                        modifier = Modifier.clickable {
                                            context.startActivity(
                                                Intent(context, FamilyMemberSwitchActivity::class.java)
                                            )
                                        }
                                    ) {
                                        Text(
                                            text = "切换成员",
                                            fontSize = 14.sp,
                                            color = Color.Gray
                                        )
                                        Icon(
                                            painter = painterResource(R.drawable.ic_switch_arrow),
                                            contentDescription = "切换成员",
                                            tint = Color.Gray,
                                            modifier = Modifier.size(20.dp)
                                        )
                                    }
                                }

                                // 年龄显示
                                Text(
                                    text = "${currentMember?.age ?: 0}岁",
                                    fontSize = 16.sp,
                                    color = Color.DarkGray,
                                    modifier = Modifier.padding(top = 8.dp, start = 10.dp)
                                )

                                // 性别显示
                                Text(
                                    text = when(currentMember?.sex) {
                                        0 -> "女"
                                        1 -> "男"
                                        else -> currentMember?.sexText ?: "未知"
                                    },
                                    fontSize = 16.sp,
                                    color = Color.DarkGray,
                                    modifier = Modifier.padding(top = 8.dp, start = 10.dp)
                                )

                                // BMI和家庭成员行
                                Row(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(top = 14.dp),
                                    horizontalArrangement = Arrangement.SpaceBetween,
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    Row(
                                        verticalAlignment = Alignment.CenterVertically,
                                        modifier = Modifier.padding(start = 10.dp)
                                    ) {
                                        Text(
                                            text = "BMI",
                                            fontSize = 16.sp,
                                            color = Color(0xFF8BC34A),
                                            fontWeight = FontWeight.Bold
                                        )
                                        Text(
                                            text = " : ${String.format("%.1f", currentMember?.bmi ?: 0.0)}",
                                            fontSize = 14.sp,
                                            fontWeight = FontWeight.Bold
                                        )
                                    }

                                    Row {
                                        val displayMembers = familyMembers.filter { it.role != "self" }.take(2)
                                        displayMembers.forEach { member ->
                                            FamilyMemberChip(text = member.role ?: "未知")
                                            Spacer(modifier = Modifier.width(4.dp))
                                        }
                                        FamilyMemberChip(text = "家庭成员", showAddIcon = true)
                                    }
                                }
                            } else {
                                // 显示错误或提示添加个人信息
                                Box(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .height(120.dp),
                                    contentAlignment = Alignment.Center
                                ) {
                                    Column(horizontalAlignment = Alignment.CenterHorizontally) {
                                        Text(
                                            text = "未找到个人信息",
                                            fontSize = 16.sp,
                                            color = Color.Gray
                                        )
                                        Spacer(modifier = Modifier.height(8.dp))
                                        Button(
                                            onClick = {
                                                context.startActivity(
                                                    Intent(context, PersonalInformationActivity::class.java)
                                                )
                                            },
                                            colors = ButtonDefaults.buttonColors(containerColor = Color(0xFF2EB0AC))
                                        ) {
                                            Text("添加个人信息")
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                // 三大功能卡片区域
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                    .padding(vertical = 0.dp, horizontal = 16.dp),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    val buttonModifier = Modifier
                        .weight(1f)
                        .height(100.dp)

                    // 用药计划 卡片
                    Card(
                        modifier = buttonModifier,
                        shape = RoundedCornerShape(16.dp),
                        colors = CardDefaults.cardColors(containerColor = Color.White),
                        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
                    ) {
                        Column(
                            modifier = Modifier
                                .fillMaxSize()
                                .clickable {
                                    val intent = Intent(context, MedicationPlanActivity::class.java)
                                    context.startActivity(intent)
                                },
                            horizontalAlignment = Alignment.CenterHorizontally,
                            verticalArrangement = Arrangement.Center
                        ) {
                            Icon(
                                painter = painterResource(R.drawable.ic_medication),
                                contentDescription = "用药计划",
                                tint = Color(0xFF64B5F6),
                                modifier = Modifier.size(40.dp)
                            )
                            Text(
                                text = "用药计划",
                                fontSize = 14.sp,
                                fontWeight = FontWeight.Bold,
                                color = Color(0xFF3B3B3B),
                                modifier = Modifier.padding(top = 8.dp)
                            )
                        }
                    }
                    // 饮食建议 卡片
                    Card(
                        modifier = buttonModifier,
                        shape = RoundedCornerShape(16.dp),
                        colors = CardDefaults.cardColors(containerColor = Color.White),
                        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
                    ) {
                        Column(
                            modifier = Modifier
                                .fillMaxSize()
                                .clickable {
                                    val intent = Intent(context, DietaryAdviceActivity::class.java)
                                    context.startActivity(intent)
                                },
                            horizontalAlignment = Alignment.CenterHorizontally,
                            verticalArrangement = Arrangement.Center
                        ) {
                            Icon(
                                painter = painterResource(R.drawable.ic_diet),
                                contentDescription = "饮食建议",
                                tint = Color(0xFFBDBDBD),
                                modifier = Modifier.size(40.dp)
                            )
                            Text(
                                text = "饮食建议",
                                fontSize = 14.sp,
                                fontWeight = FontWeight.Bold,
                                color = Color(0xFF3B3B3B),
                                modifier = Modifier.padding(top = 8.dp)
                            )
                        }
                    }
                    // 运动建议 卡片
                    Card(
                        modifier = buttonModifier,
                        shape = RoundedCornerShape(16.dp),
                        colors = CardDefaults.cardColors(containerColor = Color.White),
                        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
                    ) {
                        Column(
                            modifier = Modifier
                                .fillMaxSize()
                                .clickable {
                                    val intent = Intent(context, ExerciseAdviceActivity::class.java)
                                    context.startActivity(intent)
                                },
                            horizontalAlignment = Alignment.CenterHorizontally,
                            verticalArrangement = Arrangement.Center
                        ) {
                            Icon(
                                painter = painterResource(R.drawable.ic_exercise),
                                contentDescription = "运动建议",
                                tint = Color(0xFFFFC107),
                                modifier = Modifier.size(40.dp)
                            )
                            Text(
                                text = "运动建议",
                                fontSize = 14.sp,
                                fontWeight = FontWeight.Bold,
                                color = Color(0xFF3B3B3B),
                                modifier = Modifier.padding(top = 8.dp)
                            )
                        }
                    }
                }
                // 用药提醒、健康史、资料夹卡片区域
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp) // 增加左右间距
                ) {
                    // 用药提醒卡片
                    InfoCard(
                        title = "用药提醒",
                        modifier = Modifier.padding(top = 16.dp),
                        navigateTo = {
                            context.startActivity(
                                Intent(
                                    context,
                                    MedicationPlanActivity::class.java
                                )
                            )
                        }
                    ) {
                        Column {
                            // 修改用药计划显示部分
                            medicationPlan?.let { plan ->
                                Text(
                                    text = plan.pillName,
                                    fontSize = 16.sp,
                                    color = Color.Black,
                                    modifier = Modifier.padding(top = 8.dp)
                                )
                                Text(
                                    text = "${plan.frequency}，每次${plan.dosage}",
                                    fontSize = 14.sp,
                                    color = Color.Gray,
                                    modifier = Modifier.padding(top = 4.dp)
                                )
                                Text(
                                    text = plan.firstTime,
                                    fontSize = 14.sp,
                                    color = Color.Gray,
                                    modifier = Modifier.padding(top = 4.dp)
                                )
                            } ?: run {
                                Text(
                                    text = "暂无用药计划",
                                    fontSize = 16.sp,
                                    color = Color.Black,
                                    modifier = Modifier.padding(top = 8.dp)
                                )
                                Text(
                                    text = "点击添加用药计划",
                                    fontSize = 14.sp,
                                    color = Color.Gray,
                                    modifier = Modifier.padding(top = 4.dp)
                                )
                            }
                        }
                    }
                    // 健康史卡片
                    InfoCard(
                        title = "健康史",
                        iconRes = R.drawable.ic_leaf,
                        modifier = Modifier.padding(top = 15.dp),
                        navigateTo = {
                            context.startActivity(
                                Intent(
                                    context,
                                    HealthHistoryActivity::class.java
                                )
                            )
                        }
                    ) {
                        Text(
                            text = "既往史，个人史，家族史",
                            fontSize = 14.sp,
                            color = Color.Gray,
                            modifier = Modifier.padding(top = 15.dp)
                        )
                    }
                    // 资料夹卡片
                    InfoCard(
                        title = "资料夹",
                        iconRes = R.drawable.ic_folder,
                        modifier = Modifier.padding(top = 16.dp, bottom = 16.dp),
                        navigateTo = {
                            context.startActivity(
                                Intent(
                                    context,
                                    DocumentFolderActivity::class.java
                                )
                            )
                        }
                    ) {
                        Column {
                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(top = 8.dp),
                                horizontalArrangement = Arrangement.SpaceBetween,
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text(
                                    text = "已存储2份资料",
                                    fontSize = 16.sp,
                                    color = Color.Black
                                )
                                Button(
                                    onClick = {
                                        /* 去上传按钮点击事件 */
                                        val intent = Intent(
                                            context,
                                            DocumentFolderActivity::class.java
                                        )
                                        context.startActivity(intent)
                                    },
                                    modifier = Modifier.height(36.dp),
                                    colors = ButtonDefaults.buttonColors(
                                        containerColor = Color(0x8070C2BF),
                                        contentColor = Color(0xFF000000)
                                    ),
                                    shape = RoundedCornerShape(13.dp)
                                ) {
                                    Text(
                                        text = "去上传",
                                        fontSize = 15.sp,
                                    )
                                }
                            }
                            Text(
                                text = "用于存储病历报告型药物体检报告",
                                fontSize = 14.sp,
                                color = Color.Gray,
                                modifier = Modifier.padding(top = 4.dp)
                            )
                        }
                    }
                }
            }
        }
    }
}

    // 处理退出登录
//    DisposableEffect(key1 = Unit) {
//        onDispose {
//            viewModel.logout()
//        }
//    }
//}


@Composable
fun FamilyMemberChip(text: String, showAddIcon: Boolean = false) {
    val context = LocalContext.current
    Box(
        modifier = Modifier
            .clip(RoundedCornerShape(16.dp))
            .background(Color(0xFFEBF2F1))
            .clickable {
                if (showAddIcon) {
                    // 点击家庭成员时跳转到添加家庭成员页面
                    context.startActivity(Intent(context, AddNewFamilyMember::class.java))
                }
            }
            .padding(horizontal = 12.dp, vertical = 6.dp),
        contentAlignment = Alignment.Center
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = text,
                color = Color(0xFF333333),
                fontSize = 14.sp
            )
            if (showAddIcon) {
                Spacer(modifier = Modifier.width(4.dp))
                Icon(
                    painter = painterResource(id = R.drawable.ic_add),
                    contentDescription = "添加",
                    tint = Color(0xFF333333),
                    modifier = Modifier.size(14.dp)
                )
            }
        }
    }
}

@Composable
fun InfoCard(
    title: String,
    iconRes: Int = 0,
    modifier: Modifier = Modifier,
    navigateTo: () -> Unit = {},
    content: @Composable () -> Unit
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = title,
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold
                    )
                    if (iconRes != 0) {
                        Icon(
                            painter = painterResource(iconRes),
                            contentDescription = null,
                            tint = Color(0xFF8BC34A),
                            modifier = Modifier
                                .size(20.dp)
                                .padding(start = 6.dp)
                        )
                    }
                }
                // 右侧导航箭头
                Icon(
                    painter = painterResource(R.drawable.right_arrow),
                    contentDescription = "导航",
                    tint = Color.Gray,
                    modifier = Modifier
                        .size(24.dp)
                        .clickable { navigateTo() }
                )
            }
            content()
        }
    }
}

// 菜单项组件
@Composable
private fun MenuListItem(iconRes: Int, text: String, onClick: (() -> Unit)? = null) {
    val context = LocalContext.current
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 10.dp, horizontal = 16.dp)
            .clickable {
                onClick?.invoke() ?: run {
                    // 如果没有提供点击处理程序，使用默认行为
                    // 默认行为可以在这里定义
                }
            },
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            painter = painterResource(id = iconRes),
            contentDescription = text,
            tint = Color.Black,
            modifier = Modifier.size(28.dp)
        )

        Spacer(modifier = Modifier.width(20.dp))

        Text(
            text = text,
            fontSize = 16.sp,
            color = Color.DarkGray
        )
    }
}

@Composable
fun MemberItem(
    member: FamilyMemberData,
    isSelected: Boolean,
    onClick: () -> Unit,
    onLongClick: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable(onClick = onClick)
            .padding(vertical = 12.dp, horizontal = 16.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            text = member.name ?: "未命名",
            fontSize = 16.sp,
            fontWeight = FontWeight.Medium,
            color = Color(0xFF333333)
        )
        
        if (isSelected) {
            Icon(
                imageVector = Icons.Default.Check,
                contentDescription = "已选择",
                tint = Color(0xFF2EB0AC),
                modifier = Modifier.size(24.dp)
            )
        }
    }
}

// 根据生日计算年龄的工具函数
private fun calculateAgeFromBirthday(birthday: String): Int {
    if (birthday.isEmpty()) return 0

    try {
        val dateFormat = java.text.SimpleDateFormat("yyyy-MM-dd", java.util.Locale.getDefault())
        val birthDate = dateFormat.parse(birthday) ?: return 0

        val today = java.util.Calendar.getInstance()
        val birthCalendar = java.util.Calendar.getInstance().apply { time = birthDate }

        var age =
            today.get(java.util.Calendar.YEAR) - birthCalendar.get(java.util.Calendar.YEAR)

        // 检查是否已经过了生日
        if (today.get(java.util.Calendar.DAY_OF_YEAR) < birthCalendar.get(java.util.Calendar.DAY_OF_YEAR)) {
            age--
        }

        return if (age < 0) 0 else age
    } catch (e: Exception) {
        return 0
    }
}

@Preview(showBackground = true)
@Composable
fun DefaultPreview() {
    val context = LocalContext.current
    val sessionManager = remember { SessionManager(context) }
    val repository = remember { Repository() }
    val viewModel = remember { MainViewModel(repository, sessionManager) }

    AIHealthButlerTheme {
        Surface(
            modifier = Modifier.fillMaxSize(),
            color = MaterialTheme.colorScheme.background
        ) {
            MainScreen(
                viewModel = viewModel,
                onLogout = {}
            )
        }
    }
}


