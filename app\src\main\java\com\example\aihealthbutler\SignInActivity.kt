package com.example.aihealthbutler

import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Check
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import com.example.aihealthbutler.ui.theme.AIHealthButlerTheme
import androidx.compose.material3.OutlinedTextFieldDefaults
import androidx.compose.foundation.BorderStroke
import androidx.compose.ui.unit.Dp
import androidx.compose.foundation.clickable
//import android.widget.Toast
import androidx.lifecycle.viewmodel.compose.viewModel
import com.example.aihealthbutler.api.*
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.launch
import com.example.aihealthbutler.utils.NetworkUtils
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.withContext
import com.example.aihealthbutler.api.ApiClient
import kotlinx.coroutines.Dispatchers
import android.util.Log
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import java.net.InetSocketAddress
import java.net.Socket
import android.content.Context
import android.widget.Toast
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import com.example.aihealthbutler.utils.NetworkDebugUtil
import com.example.aihealthbutler.utils.ErrorReportUtil
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow


class SignInViewModel(
    private val repository: Repository,
    private val sessionManager: SessionManager
) : ViewModel() {
    private val TAG = "SignInVM"
    
    private val _loginState = MutableStateFlow<LoginState>(LoginState.Idle)
    val loginState: StateFlow<LoginState> = _loginState
    
    fun login(account: String, password: String) {
        if (account.isEmpty() || password.isEmpty()) {
            _loginState.value = LoginState.Error("请输入账号和密码")
            return
        }
        
        viewModelScope.launch {
            try {
                _loginState.value = LoginState.Loading
                
                val request = LoginRequest(
                    account = account,
                    password = password
                )
                val result = repository.login(request)
                
                if (result.isSuccess) {
                    val response = result.getOrNull()
                    if (response?.code == 200 && response.data != null) {
                        // 保存登录信息 - 添加保存account
                        sessionManager.saveAccount(account)  // 添加这行)
                        sessionManager.saveDefaultId(response.data.defaultId)
                        
                        _loginState.value = LoginState.Success(response.message)
                    } else {
                        _loginState.value = LoginState.Error(response?.message ?: "登录失败")
                    }
                } else {
                    _loginState.value = LoginState.Error(result.exceptionOrNull()?.message ?: "登录失败")
                }
            } catch (e: Exception) {
                _loginState.value = LoginState.Error("登录失败: ${e.message}")
            }
        }
    }
    
    fun register(account: String, password: String) {
        if (account.isEmpty() || password.isEmpty()) {
            _loginState.value = LoginState.Error("请输入账号和密码")
            return
        }
        
        viewModelScope.launch {
            try {
                _loginState.value = LoginState.Loading
                
                val request = RegisterRequest(
                    account = account,
                    password = password
                )
                val result = repository.register(request)
                
                if (result.isSuccess) {
                    val response = result.getOrNull()
                    if (response?.code == 200 && response.data != null) {
                        // 保存注册信息
                        sessionManager.saveToken(response.data.token)
                        sessionManager.saveDefaultId(response.data.defaultId)
                        
                        _loginState.value = LoginState.Success(response.message)
                    } else {
                        _loginState.value = LoginState.Error(response?.message ?: "注册失败")
                    }
                } else {
                    _loginState.value = LoginState.Error(result.exceptionOrNull()?.message ?: "注册失败")
                }
            } catch (e: Exception) {
                _loginState.value = LoginState.Error("注册失败: ${e.message}")
            }
        }
    }
    
    sealed class LoginState {
        object Idle : LoginState()
        object Loading : LoginState()
        data class Success(val message: String) : LoginState()
        data class Error(val message: String) : LoginState()
    }
}

class SignInActivity : ComponentActivity() {
    private val TAG = "SignInActivity"
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        
        // 添加详细的网络状态检查
        val connectivityManager = getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        val networkCapabilities = connectivityManager.getNetworkCapabilities(connectivityManager.activeNetwork)
        
        val networkConnected = networkCapabilities != null && 
                               networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET) &&
                               networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED)
        
        Log.d(TAG, "网络连接状态: ${if(networkConnected) "已连接" else "未连接"}")
        
        if (!networkConnected) {
            Log.e(TAG, "❌ 网络不可用：未检测到活动网络")
            MainScope().launch {
                val networkDiagnostics = NetworkDebugUtil.runNetworkDiagnostics(this@SignInActivity)
                Log.e(TAG, "网络诊断结果:\n$networkDiagnostics")
                
                Toast.makeText(
                    this@SignInActivity,
                    "网络连接不可用，请检查网络设置",
                    Toast.LENGTH_LONG
                ).show()
            }
        } else {
            // 网络连接测试（优化版本）
            MainScope().launch {
                withContext(Dispatchers.IO) {
                    // 服务器连接测试
                    try {
                        val socket = Socket()
                        val socketAddress = InetSocketAddress("qcx.yuneyang.top", 443)
                        try {
                            Log.d(TAG, "测试服务器连接...")
                            socket.connect(socketAddress, 5000)
                            Log.d(TAG, "服务器连接成功")
                        } catch (e: Exception) {
                            Log.d(TAG, "服务器连接：失败 - ${e.message}")
                        } finally {
                            try { socket.close() } catch (e: Exception) {}
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "Socket创建失败", e)
                    }
                    
                    // API连接测试
                    try {
                        Log.d(TAG, "进行API服务器连接测试...")
                        val apiConnected = ApiClient.testApiConnection()
                        Log.d(TAG, "API服务器连接测试: ${if(apiConnected) "成功" else "失败"}")
                        
                        if (!apiConnected) {
                            // 重试不同的服务器URL
                            Log.d(TAG, "尝试重置API客户端...")
                            ApiClient.reset()
                            
                            withContext(Dispatchers.Main) {
                                if (!ApiClient.testApiConnection()) {
                                    // 无法连接到任何API服务器
                                    Toast.makeText(
                                        this@SignInActivity,
                                        "无法连接到服务器，请检查网络设置或稍后再试",
                                        Toast.LENGTH_LONG
                                    ).show()
                                }
                            }
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "API连接测试失败", e)
                    }
                    
                    // 进行DNS解析测试
                    try {
                        Log.d(TAG, "🔍 测试DNS解析...")
                        val start = System.currentTimeMillis()
                        val dnsResult = runCatching {
                            val inetAddress = java.net.InetAddress.getByName("qcx.yuneyang.top")
                            val time = System.currentTimeMillis() - start
                            Log.d(TAG, "✅ DNS解析成功: ${inetAddress.hostAddress}, 耗时: ${time}ms")
                            true
                        }.getOrElse {
                            val time = System.currentTimeMillis() - start
                            Log.e(TAG, "❌ DNS解析失败: ${it.message}, 耗时: ${time}ms", it)
                            
                            // DNS 解析失败，尝试使用备用域名
                            val serverUrl = com.example.aihealthbutler.utils.CustomDns.getServerUrl()
                            if (serverUrl.isNotEmpty()) {
                                Log.d(TAG, "使用备用域名: $serverUrl")
                                
                                // 清除DNS缓存，尝试重新解析
                                com.example.aihealthbutler.utils.CustomDns.clearDnsCache()
                                false
                            } else {
                                false
                            }
                        }
                        
                        if (!dnsResult) {
                            withContext(Dispatchers.Main) {
                                Toast.makeText(
                                    this@SignInActivity,
                                    "DNS解析失败，可能无法连接到服务器",
                                    Toast.LENGTH_LONG
                                ).show()
                            }
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "DNS测试异常", e)
                    }
                }
            }
        }
        
        setContent {
            AIHealthButlerTheme {
                val sessionManager = remember { SessionManager(applicationContext) }
                val repository = remember { Repository() }
                val viewModel = remember { SignInViewModel(repository, sessionManager) }
                
                SignInScreen(
                    viewModel = viewModel,
                    onSignInSuccess = {
                        // 登录成功后跳转到对话界面
                        startActivity(Intent(this, ConversationInterface::class.java))
                        finish()
                    }
                )
            }
        }
    }

    private fun checkAndHandleNetworkStatus(): Boolean {
        val connectivityManager = getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        val networkCapabilities = connectivityManager.getNetworkCapabilities(connectivityManager.activeNetwork)
        
        if (networkCapabilities == null) {
            Toast.makeText(this, "无网络连接，请检查网络设置", Toast.LENGTH_SHORT).show()
            Log.e(TAG, "❌ 网络不可用：未检测到活动网络")
            return false
        }
        
        // 检查详细的网络能力
        val hasInternet = networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
        val hasValidated = networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED)
        val hasCellular = networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR)
        val hasWifi = networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI)
        
        Log.d(TAG, "🌐 网络状态：互联网=$hasInternet, 已验证=$hasValidated, 蜂窝=$hasCellular, WiFi=$hasWifi")
        
        if (!hasInternet || !hasValidated) {
            Toast.makeText(this, "网络已连接但无互联网访问，请检查网络设置", Toast.LENGTH_SHORT).show()
            return false
        }
        
        return true
    }

    fun checkNetworkStatus(context: Context): Boolean {
        return NetworkDebugUtil.isNetworkAvailable(context)
    }
}

@Composable
fun SignInScreen(
    viewModel: SignInViewModel,
    onSignInSuccess: () -> Unit
) {
    val context = LocalContext.current
    val userViewModel: UserViewModel = viewModel(factory = UserViewModelFactory(context))
    
    var isChecked by remember { mutableStateOf(false) }
    var selectedTabIndex by remember { mutableStateOf(0) }
    
    // 添加两个密码可见性状态，分别用于登录和注册
    var loginPasswordVisible by remember { mutableStateOf(false) }
    var registerPasswordVisible by remember { mutableStateOf(false) }
    
    // 添加登录成功弹窗状态
    var showLoginSuccess by remember { mutableStateOf(false) }
    
    // 添加注册成功弹窗状态
    var showRegisterSuccess by remember { mutableStateOf(false) }
    
    // 添加提示对话框状态
    var showProtocolAlert by remember { mutableStateOf(false) }
    var alertMessage by remember { mutableStateOf("") }
    
    // 分别为登录和注册创建独立的输入状态
    var loginPhoneNumber by remember { mutableStateOf("") }
    var loginPassword by remember { mutableStateOf("") }
    var registerPhoneNumber by remember { mutableStateOf("") }
    var registerPassword by remember { mutableStateOf("") }
    
    // 添加错误提示状态
    var errorMessage by remember { mutableStateOf("") }
    var showErrorDialog by remember { mutableStateOf(false) }
    
    // 添加加载状态
    var isLoading by remember { mutableStateOf(false) }
    
    // 添加诊断结果弹窗状态
    var showDiagnosticDialog by remember { mutableStateOf(false) }
    var diagnosticReport by remember { mutableStateOf("") }
    var isDiagnosing by remember { mutableStateOf(false) }
    
    val selectedColor = Color(0xFF009688)
    val unselectedColor = Color.Gray
    val inputFieldBackgroundColor = Color(0xFFF5FAF9)
    val buttonColor = Color(0xFF2EB0AC)

    // 登录成功弹窗
    if (showLoginSuccess) {
        Dialog(onDismissRequest = { showLoginSuccess = false }) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(Color.White, RoundedCornerShape(8.dp))
                    .padding(vertical = 20.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Box(
                    modifier = Modifier
                        .size(70.dp)
                        .background(Color(0xFF2EB0AC), CircleShape),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = Icons.Default.Check,
                        contentDescription = "Success",
                        tint = Color.White,
                        modifier = Modifier.size(36.dp)
                    )
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                Text(
                    text = "登录成功",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Medium
                )
                
                Spacer(modifier = Modifier.height(20.dp))
                
                Text(
                    text = "是否进行个人信息填写，以获得更个性化的体验？",
                    fontSize = 12.sp,
                    color = Color.Gray
                )
                
                Spacer(modifier = Modifier.height(20.dp))
                
                Button(
                    onClick = { 
                        // 关闭弹窗并跳转到个人信息页面
                        showLoginSuccess = false
                        val intent = Intent(context,PersonalInformationActivity::class.java)
                        context.startActivity(intent)
                    },
                    modifier = Modifier
                        .fillMaxWidth(0.8f)
                        .height(40.dp),
                    shape = RoundedCornerShape(20.dp),
                    colors = ButtonDefaults.buttonColors(containerColor = buttonColor)
                ) {
                    Text("前往进行信息填写", color = Color.White)
                }
                
                Spacer(modifier = Modifier.height(8.dp))
                
                TextButton(
                    onClick = {
                        // 跳转到对话界面
                        val intent = Intent(context, ConversationInterface::class.java)
                        context.startActivity(intent)
                    }
                ) {
                    Text(
                        text = "不填写直接进入 >",
                        color = Color(0xFF2EB0AC),
                        fontSize = 14.sp
                    )
                }
            }
        }
    }

    // 添加注册成功弹窗
    if (showRegisterSuccess) {
        Dialog(onDismissRequest = { showRegisterSuccess = false }) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(Color.White, RoundedCornerShape(8.dp))
                    .padding(vertical = 25.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Box(
                    modifier = Modifier
                        .size(65.dp)
                        .background(Color(0xFF2EB0AC), CircleShape),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = Icons.Default.Check,
                        contentDescription = "Success",
                        tint = Color.White,
                        modifier = Modifier.size(36.dp)
                    )
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                Text(
                    text = "注册成功",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Medium
                )
                
                Spacer(modifier = Modifier.height(20.dp))
                
                Button(
                    onClick = { 
                        // 关闭弹窗并切换到登录tab
                        showRegisterSuccess = false
                        selectedTabIndex = 0 // 切换到登录tab
                    },
                    modifier = Modifier
                        .fillMaxWidth(0.8f)
                        .height(40.dp),
                    shape = RoundedCornerShape(20.dp),
                    colors = ButtonDefaults.buttonColors(containerColor = buttonColor)
                ) {
                    Text("去登录", color = Color.White)
                }
            }
        }
    }

    // 协议提示弹窗
    if (showProtocolAlert) {
        Dialog(onDismissRequest = { showProtocolAlert = false }) {
            Column(
                modifier = Modifier
                    .fillMaxWidth(0.9f)
                    .background(Color.White, RoundedCornerShape(8.dp))
                    .padding(vertical = 20.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = alertMessage,
                    fontSize = 16.sp,
                    color = Color.Black,
                    modifier = Modifier.padding(horizontal = 24.dp, vertical = 16.dp)
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                Button(
                    onClick = { showProtocolAlert = false },
                    modifier = Modifier
                        .fillMaxWidth(0.5f)
                        .height(40.dp),
                    shape = RoundedCornerShape(20.dp),
                    colors = ButtonDefaults.buttonColors(containerColor = buttonColor)
                ) {
                    Text("确定",
                        color = Color.White,
                        fontSize = 16.sp
                    )
                }
            }
        }
    }

    // 添加错误对话框
    if (showErrorDialog) {
        Dialog(onDismissRequest = { showErrorDialog = false }) {
            Column(
                modifier = Modifier
                    .fillMaxWidth(0.9f)
                    .background(Color.White, RoundedCornerShape(12.dp))
                    .padding(20.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // 错误标题
                Text(
                    text = "登录失败",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFFE53E3E),
                    modifier = Modifier.padding(bottom = 12.dp)
                )

                // 错误详情 - 支持多行显示和左对齐
                Text(
                    text = errorMessage,
                    fontSize = 14.sp,
                    color = Color(0xFF2D3748),
                    textAlign = TextAlign.Start,
                    lineHeight = 20.sp,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 8.dp, vertical = 8.dp)
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceEvenly
                ) {
                    Button(
                        onClick = { showErrorDialog = false },
                        modifier = Modifier
                            .weight(1f)
                            .height(40.dp)
                            .padding(horizontal = 8.dp),
                        shape = RoundedCornerShape(20.dp),
                        colors = ButtonDefaults.buttonColors(containerColor = buttonColor)
                    ) {
                        Text("确定",
                            color = Color.White,
                            fontSize = 16.sp
                        )
                    }
                    
                    Button(
                        onClick = { 
                            showErrorDialog = false
                            // 显示网络诊断对话框
                            isDiagnosing = true
                            showDiagnosticDialog = true
                            
                            MainScope().launch {
                                try {
                                    val report = NetworkDebugUtil.runNetworkDiagnostics(context)
                                    
                                    // 添加Socket连接诊断
                                    val socketDiagnostics = ErrorReportUtil.diagnoseAndFixSocketIssue(
                                        context,
                                        "qcx.yuneyang.top",
                                        443
                                    )
                                    
                                    // 合并诊断结果
                                    diagnosticReport = "一般网络诊断:\n$report\n\nSocket连接诊断:\n$socketDiagnostics"
                                    isDiagnosing = false
                                } catch (e: Exception) {
                                    diagnosticReport = "诊断过程出错: ${e.message}"
                                    isDiagnosing = false
                                    
                                    // 记录错误
                                    ErrorReportUtil.logError("诊断错误", "网络诊断过程中出现异常", e)
                                }
                            }
                        },
                        modifier = Modifier
                            .weight(1f)
                            .height(40.dp)
                            .padding(horizontal = 8.dp),
                        shape = RoundedCornerShape(20.dp),
                        colors = ButtonDefaults.buttonColors(containerColor = Color.Gray)
                    ) {
                        Text("网络诊断",
                            color = Color.White,
                            fontSize = 16.sp
                        )
                    }
                }
            }
        }
    }

    // 添加网络诊断对话框
    if (showDiagnosticDialog) {
        Dialog(onDismissRequest = { if (!isDiagnosing) showDiagnosticDialog = false }) {
            Column(
                modifier = Modifier
                    .fillMaxWidth(0.9f)
                    .heightIn(max = 500.dp) // 限制最大高度
                    .background(Color.White, RoundedCornerShape(8.dp))
                    .padding(20.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "网络诊断",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier.padding(bottom = 16.dp)
                )
                
                if (isDiagnosing) {
                    CircularProgressIndicator(
                        color = buttonColor,
                        modifier = Modifier
                            .size(50.dp)
                            .padding(16.dp)
                    )
                    Text(
                        text = "正在进行网络诊断...",
                        fontSize = 16.sp,
                        modifier = Modifier.padding(vertical = 16.dp)
                    )
                } else {
                    // 滚动内容
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .weight(1f, fill = true)
                            .background(Color(0xFFF5F5F5), RoundedCornerShape(8.dp))
                            .padding(12.dp)
                    ) {
                        Text(
                            text = diagnosticReport,
                            fontSize = 12.sp,
                            modifier = Modifier
                                .fillMaxSize()
                                .verticalScroll(rememberScrollState())
                        )
                    }
                    
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    Button(
                        onClick = { showDiagnosticDialog = false },
                        modifier = Modifier
                            .fillMaxWidth(0.5f)
                            .height(40.dp),
                        shape = RoundedCornerShape(20.dp),
                        colors = ButtonDefaults.buttonColors(containerColor = buttonColor)
                    ) {
                        Text("关闭",
                            color = Color.White,
                            fontSize = 16.sp
                        )
                    }
                }
            }
        }
    }

    Box(modifier = Modifier.fillMaxSize()) {
        // Background Image
        Image(
            painter = painterResource(id = R.drawable.sign_background),
            contentDescription = null,
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.FillBounds
        )

        // Content
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = 24.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Spacer(modifier = Modifier.height(130.dp))
            // Title
            Column(
                modifier = Modifier.fillMaxWidth(),
                horizontalAlignment = Alignment.Start
            ) {
                Text(
                    text = "Hello!",
                    fontSize = 32.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.Black
                )
                Text(
                    text = "欢迎来到AI健康管家",
                    fontSize = 24.sp,
                    color = Color.Black,
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier.padding(top = 8.dp)
                )
            }

            Spacer(modifier = Modifier.height(40.dp))

            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(Color.White, RoundedCornerShape(8.dp))
                    .padding(horizontal = 16.dp, vertical = 8.dp)
            ) {
                Column(modifier = Modifier.fillMaxWidth()) {
                    // Tabs 行
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        // 账号登陆 Tab
                        Column(
                            modifier = Modifier
                                .weight(1f)
                                .clickable { selectedTabIndex = 0 },
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            Text(
                                text = "账号登陆",
                                fontSize = 16.sp,
                                fontWeight = FontWeight.Medium,
                                color = if (selectedTabIndex == 0) selectedColor else unselectedColor,
                                modifier = Modifier.padding(vertical = 8.dp)
                            )
                            // 下短横线
                            if (selectedTabIndex == 0) {
                                Box(
                                    modifier = Modifier
                                        .width(40.dp)
                                        .height(3.dp)
                                        .background(selectedColor)
                                )
                            }
                        }
                        
                        // 账号注册 Tab
                        Column(
                            modifier = Modifier
                                .weight(1f)
                                .clickable { selectedTabIndex = 1 },
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            Text(
                                text = "账号注册",
                                fontSize = 16.sp,
                                fontWeight = FontWeight.Medium,
                                color = if (selectedTabIndex == 1) selectedColor else unselectedColor,
                                modifier = Modifier.padding(vertical = 8.dp)
                            )
                            // 选中状态下的短横线
                            if (selectedTabIndex == 1) {
                                Box(
                                    modifier = Modifier
                                        .width(40.dp)
                                        .height(3.dp)
                                        .background(selectedColor)
                                )
                            }
                        }
                    }
                }
            }

            Spacer(modifier = Modifier.height(40.dp))

            // 根据选中的Tab显示不同的输入区域
            if (selectedTabIndex == 0) {
                // 登录页面的账号输入框
                OutlinedTextField(
                    value = loginPhoneNumber,
                    onValueChange = { loginPhoneNumber = it },
                    modifier = Modifier.fillMaxWidth(),
                    placeholder = { Text("登录请输入账号名", color = Color.Gray) },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Text),
                    shape = RoundedCornerShape(8.dp),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedContainerColor = inputFieldBackgroundColor,
                        unfocusedContainerColor = inputFieldBackgroundColor,
                        disabledContainerColor = inputFieldBackgroundColor,
                        focusedBorderColor = Color(0xFF2EB0AC),
                        unfocusedBorderColor = Color(0xFFF5FAF9),
                        cursorColor = selectedColor
                    ),
                    singleLine = true
                )
                
                Spacer(modifier = Modifier.height(20.dp))
                
                // 密码输入框
                OutlinedTextField(
                    value = loginPassword,
                    onValueChange = { loginPassword = it },
                    modifier = Modifier.fillMaxWidth(),
                    placeholder = { Text("请输入密码", color = Color.Gray) },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Password),
                    visualTransformation = if (loginPasswordVisible) VisualTransformation.None else PasswordVisualTransformation(),
                    shape = RoundedCornerShape(8.dp),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedContainerColor = inputFieldBackgroundColor,
                        unfocusedContainerColor = inputFieldBackgroundColor,
                        disabledContainerColor = inputFieldBackgroundColor,
                        focusedBorderColor = Color(0xFF2EB0AC),
                        unfocusedBorderColor = Color(0xFFF5FAF9),
                        cursorColor = selectedColor
                    ),
                    trailingIcon = {
                        IconButton(onClick = { loginPasswordVisible = !loginPasswordVisible }) {
                            Icon(
                                painter = painterResource(
                                    id = if (loginPasswordVisible) R.drawable.seeing else R.drawable.hiding
                                ),
                                contentDescription = if (loginPasswordVisible) "隐藏密码" else "显示密码",
                                tint = selectedColor,
                                modifier = Modifier.size(25.dp)
                            )
                        }
                    },
                    singleLine = true
                )
            } else {
                // 注册页面的账号输入框
                OutlinedTextField(
                    value = registerPhoneNumber,
                    onValueChange = { registerPhoneNumber = it },
                    modifier = Modifier.fillMaxWidth(),
                    placeholder = { Text("注册请输入账号名", color = Color.Gray) },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Text),
                    shape = RoundedCornerShape(8.dp),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedContainerColor = inputFieldBackgroundColor,
                        unfocusedContainerColor = inputFieldBackgroundColor,
                        disabledContainerColor = inputFieldBackgroundColor,
                        focusedBorderColor = Color(0xFF2EB0AC),
                        unfocusedBorderColor = Color(0xFFF5FAF9),
                        cursorColor = selectedColor
                    ),
                    singleLine = true
                )
                
                Spacer(modifier = Modifier.height(20.dp))
                
                // 验证码输入框
                OutlinedTextField(
                    value = registerPassword,
                    onValueChange = { registerPassword = it },
                    modifier = Modifier.fillMaxWidth(),
                    placeholder = { Text("请输入包含字母和数字的密码", color = Color.Gray) },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Password),
                    shape = RoundedCornerShape(8.dp),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedContainerColor = inputFieldBackgroundColor,
                        unfocusedContainerColor = inputFieldBackgroundColor,
                        disabledContainerColor = inputFieldBackgroundColor,
                        focusedBorderColor = Color(0xFF2EB0AC),
                        unfocusedBorderColor = Color(0xFFF5FAF9),
                        cursorColor = selectedColor
                    ),
                    visualTransformation = if (registerPasswordVisible) VisualTransformation.None else PasswordVisualTransformation(),
                    trailingIcon = {
                        IconButton(onClick = { registerPasswordVisible = !registerPasswordVisible }) {
                            Icon(
                                painter = painterResource(
                                    id = if (registerPasswordVisible) R.drawable.seeing else R.drawable.hiding
                                ),
                                contentDescription = if (registerPasswordVisible) "隐藏密码" else "显示密码",
                                tint = selectedColor,
                                modifier = Modifier.size(25.dp)
                            )
                        }
                    },
                    singleLine = true
                )
            }

            Spacer(modifier = Modifier.height(45.dp))

            // 按钮文本根据选中的tab变化
            Button(
                onClick = {
                    if (!isChecked) {
                        // 如果未勾选协议，显示提示
                        alertMessage = "请先阅读和同意用户和隐私协议"
                        showProtocolAlert = true
                    } else {
                        // 使用Composable内的网络检查函数
                        if (!NetworkDebugUtil.isNetworkAvailable(context)) {
                            errorMessage = "网络连接问题，请检查您的网络设置"
                            showErrorDialog = true
                            return@Button
                        }
                        
                        // 先设置加载状态
                        isLoading = true
                        Log.d("SignInScreen", "🚀 按钮点击，开始" + if (selectedTabIndex == 0) "登录" else "注册" + "请求")
                        
                        // 处理登录或注册
                        MainScope().launch {
                            try {
                                // 网络诊断
                                withContext(Dispatchers.IO) {
                                    try {
                                        val networkCheck = NetworkDebugUtil.isNetworkAvailable(context)
                                        if (!networkCheck) {
                                            withContext(Dispatchers.Main) {
                                                isLoading = false
                                                errorMessage = "网络连接不可用，请检查网络设置后重试"
                                                showErrorDialog = true
                                            }
                                            return@withContext
                                        }
                                        
                                        // 测试API连接
                                        if (!ApiClient.testApiConnection()) {
                                            // 尝试重置API客户端
                                            Log.d("SignInScreen", "API连接失败，尝试重置客户端...")
                                            ApiClient.reset()
                                            
                                            if (!ApiClient.testApiConnection()) {
                                                withContext(Dispatchers.Main) {
                                                    isLoading = false
                                                    errorMessage = "无法连接到服务器，请检查网络并稍后重试"
                                                    showErrorDialog = true
                                                }
                                                return@withContext
                                            }
                                        }
                                        
                                        // DNS测试
                                        val dnsResult = runCatching {
                                            val start = System.currentTimeMillis()
                                            val inetAddress = java.net.InetAddress.getByName("qcx.yuneyang.top")
                                            val time = System.currentTimeMillis() - start
                                            Log.d("SignInScreen", "✅ DNS解析成功: ${inetAddress.hostAddress}, 耗时: ${time}ms")
                                            true
                                        }.getOrElse { 
                                            Log.e("SignInScreen", "❌ DNS解析失败", it)
                                            val serverUrl = com.example.aihealthbutler.utils.CustomDns.getServerUrl()
                                            Log.d("SignInScreen", "使用备用域名: $serverUrl")
                                            serverUrl.isNotEmpty() // 如果有备用域名，返回true继续
                                        }
                                        
                                        if (!dnsResult) {
                                            withContext(Dispatchers.Main) {
                                                isLoading = false
                                                errorMessage = "域名解析失败，请检查网络设置"
                                                showErrorDialog = true
                                            }
                                            return@withContext
                                        }
                                    } catch (e: Exception) {
                                        Log.e("SignInScreen", "❌ 网络诊断失败", e)
                                    }
                                }
                                
                                // 根据选中的tab执行登录或注册
                                if (selectedTabIndex == 0) {
                                    // 登录逻辑
                                    if (loginPhoneNumber.isNotEmpty() && loginPassword.isNotEmpty()) {
                                        Log.d("SignInScreen", "📝 开始登录: 账号=${loginPhoneNumber}")
                                        userViewModel.login(
                                            loginPhoneNumber,
                                            loginPassword,
                                            onSuccess = { 
                                                Log.d("SignInScreen", "✅ 登录成功")
                                                isLoading = false
                                                showLoginSuccess = true 
                                            },
                                            onError = { errorMsg ->
                                                Log.e("SignInScreen", "❌ 登录失败: $errorMsg")
                                                isLoading = false

                                                // 根据错误类型提供不同的用户指导
                                                errorMessage = when {
                                                    errorMsg.contains("用户名或密码错误") -> {
                                                        "🔐 $errorMsg\n\n💡 建议：\n• 请仔细检查账号和密码\n• 确认大小写是否正确\n• 如忘记密码，请联系管理员重置"
                                                    }
                                                    errorMsg.contains("账户已被禁用") -> {
                                                        "🚫 $errorMsg\n\n💡 建议：\n• 请联系系统管理员\n• 确认账户状态\n• 可能需要重新激活账户"
                                                    }
                                                    errorMsg.contains("请求过于频繁") -> {
                                                        "⏰ $errorMsg\n\n💡 建议：\n• 请等待2-3分钟后再试\n• 避免频繁点击登录按钮\n• 确保网络连接稳定"
                                                    }
                                                    errorMsg.contains("网络") || errorMsg.contains("连接") || errorMsg.contains("超时") -> {
                                                        "🌐 $errorMsg\n\n💡 建议：\n• 检查网络连接是否正常\n• 尝试切换WiFi或移动数据\n• 确认服务器地址是否正确"
                                                    }
                                                    errorMsg.contains("服务器") -> {
                                                        "🔧 $errorMsg\n\n💡 建议：\n• 服务器可能正在维护\n• 请稍后重试\n• 如持续出现，请联系技术支持"
                                                    }
                                                    else -> {
                                                        "❌ $errorMsg\n\n💡 如问题持续，请联系技术支持"
                                                    }
                                                }

                                                showErrorDialog = true

                                                // 检测是否需要网络诊断
                                                val isNetworkError = errorMsg.contains("连接") ||
                                                                     errorMsg.contains("超时") ||
                                                                     errorMsg.contains("DNS") ||
                                                                     errorMsg.contains("网络") ||
                                                                     errorMsg.contains("host")

                                                if (isNetworkError) {
                                                    // 登录失败时进行网络诊断
                                                    MainScope().launch {
                                                        val diagnostics = NetworkDebugUtil.runNetworkDiagnostics(context)
                                                        Log.d("SignInScreen", "网络诊断结果:\n$diagnostics")

                                                        // 添加Socket连接诊断
                                                        val socketDiagnostics = ErrorReportUtil.diagnoseAndFixSocketIssue(
                                                            context,
                                                            "qcx.yuneyang.top",
                                                            443
                                                        )
                                                        Log.d("SignInScreen", "Socket连接诊断:\n$socketDiagnostics")

                                                        // 合并诊断结果
                                                        diagnosticReport = "一般网络诊断:\n$diagnostics\n\nSocket连接诊断:\n$socketDiagnostics"
                                                    }
                                                }
                                            }
                                        )
                                    } else {
                                        Log.e("SignInScreen", "❌ 登录失败: 账号或密码为空")
                                        isLoading = false
                                        errorMessage = "账号和密码不能为空"
                                        showErrorDialog = true
                                    }
                                } else {
                                    // 注册逻辑
                                    if (registerPhoneNumber.isNotEmpty() && registerPassword.isNotEmpty()) {
                                        Log.d("SignInScreen", "📝 开始注册: 账号=${registerPhoneNumber}")
                                        userViewModel.register(
                                            registerPhoneNumber,
                                            registerPassword,
                                            onSuccess = { 
                                                Log.d("SignInScreen", "✅ 注册成功")
                                                isLoading = false
                                                showRegisterSuccess = true 
                                            },
                                            onError = {
                                                Log.e("SignInScreen", "❌ 注册失败: $it")
                                                isLoading = false
                                                errorMessage = it
                                                showErrorDialog = true
                                                
                                                // 检测是否需要网络诊断
                                                val isNetworkError = it.contains("连接") || 
                                                                     it.contains("超时") || 
                                                                     it.contains("DNS") || 
                                                                     it.contains("网络") ||
                                                                     it.contains("host")
                                                                     
                                                if (isNetworkError) {
                                                    // 注册失败时进行网络诊断
                                                    MainScope().launch {
                                                        val diagnostics = NetworkDebugUtil.runNetworkDiagnostics(context)
                                                        Log.d("SignInScreen", "网络诊断结果:\n$diagnostics")
                                                        
                                                        // 添加Socket连接诊断
                                                        val socketDiagnostics = ErrorReportUtil.diagnoseAndFixSocketIssue(
                                                            context,
                                                            "qcx.yuneyang.top",
                                                            443
                                                        )
                                                        Log.d("SignInScreen", "Socket连接诊断:\n$socketDiagnostics")
                                                        
                                                        // 合并诊断结果
                                                        diagnosticReport = "一般网络诊断:\n$diagnostics\n\nSocket连接诊断:\n$socketDiagnostics"
                                                    }
                                                }
                                            }
                                        )
                                    } else {
                                        Log.e("SignInScreen", "❌ 注册失败: 账号或密码为空")
                                        isLoading = false
                                        errorMessage = "账号和密码不能为空"
                                        showErrorDialog = true
                                    }
                                }
                            } catch (e: Exception) {
                                Log.e("SignInScreen", "⚠️ API连接异常", e)
                                isLoading = false
                                
                                // 提供详细的错误信息
                                val detailedError = when (e) {
                                    is java.net.UnknownHostException -> "DNS解析失败，无法解析主机地址: ${e.message}"
                                    is java.net.ConnectException -> "连接服务器失败，服务器可能不可用: ${e.message}"
                                    is java.net.SocketTimeoutException -> "连接服务器超时: ${e.message}"
                                    is javax.net.ssl.SSLHandshakeException -> "SSL握手失败，证书问题: ${e.message}"
                                    else -> "未知错误: ${e.message}"
                                }
                                
                                errorMessage = "连接异常：$detailedError"
                                showErrorDialog = true
                            }
                        }
                    }
                },
                modifier = Modifier
                    .fillMaxWidth()
                    .height(50.dp),
                shape = RoundedCornerShape(10.dp),
                colors = ButtonDefaults.buttonColors(containerColor = buttonColor),
                enabled = !isLoading
            ) {
                if (isLoading) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(24.dp),
                        color = Color.White,
                        strokeWidth = 2.dp
                    )
                } else {
                    Text(
                        text = if (selectedTabIndex == 0) "登录" else "注册",
                        fontSize = 18.sp,
                        color = Color.White
                    )
                }
            }

            Spacer(modifier = Modifier.weight(1f)) // 将协议推到底部

            // 协议勾选部分
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 10.dp),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.Center
            ) {
                CircularCheckbox(
                    selected = isChecked,
                    onClick = { isChecked = !isChecked },
                    selectedColor = selectedColor,
                    unselectedColor = Color.Gray
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "我已阅读并同意《用户协议》和《隐私协议》",
                    fontSize = 13.sp,
                    color = Color.DarkGray
                )
            }

            // 添加底部网络诊断小链接
            Spacer(modifier = Modifier.weight(1f))
            
            TextButton(
                onClick = {
                    isDiagnosing = true
                    showDiagnosticDialog = true
                    
                    MainScope().launch {
                        try {
                            val report = NetworkDebugUtil.runNetworkDiagnostics(context)
                            
                            // 添加Socket连接诊断
                            val socketDiagnostics = ErrorReportUtil.diagnoseAndFixSocketIssue(
                                context,
                                "qcx.yuneyang.top",
                                443
                            )
                            
                            // 合并诊断结果
                            diagnosticReport = "一般网络诊断:\n$report\n\nSocket连接诊断:\n$socketDiagnostics"
                            isDiagnosing = false
                        } catch (e: Exception) {
                            diagnosticReport = "诊断过程出错: ${e.message}"
                            isDiagnosing = false
                            
                            // 记录错误
                            ErrorReportUtil.logError("诊断错误", "网络诊断过程中出现异常", e)
                        }
                    }
                },
                modifier = Modifier.padding(bottom = 8.dp)
            ) {
                Text(
                    text = "网络连接问题？点击诊断",
                    fontSize = 12.sp,
                    color = Color.Gray
                )
            }
        }
    }
}

@Composable
fun CircularCheckbox(
    selected: Boolean,
    onClick: () -> Unit,
    selectedColor: Color,
    unselectedColor: Color,
    modifier: Modifier = Modifier,
    size: Dp = 20.dp
) {
    Box(
        modifier = modifier
            .size(size)
            .clip(CircleShape)
            .background(if (selected) selectedColor else Color.Transparent)
            .border(
                BorderStroke(1.dp, if (selected) selectedColor else unselectedColor),
                CircleShape
            )
            .clickable(onClick = onClick),
        contentAlignment = Alignment.Center
    ) {
        if (selected) {
            Icon(
                imageVector = Icons.Default.Check,
                contentDescription = "Checked",
                tint = Color.White,
                modifier = Modifier.size(size * 0.7f)
            )
        }
    }
}

// 添加到类定义之前
class UserViewModel(private val repository: Repository, private val sessionManager: SessionManager) : ViewModel() {
    private val TAG = "UserViewModel"
    
    private val _loginState = MutableStateFlow<LoginState>(LoginState.Idle)
    val loginState: StateFlow<LoginState> = _loginState
    
    fun login(account: String, password: String, onSuccess: () -> Unit, onError: (String) -> Unit) {
        Log.d(TAG, "开始登录: $account")
        viewModelScope.launch {
            if (account.isEmpty() || password.isEmpty()) {
                onError("账号和密码不能为空")
                return@launch
            }
            
            try {
                Log.d(TAG, "调用登录API")
                val request = LoginRequest(
                    account = account,
                    password = password
                )
                val result = repository.login(request)
                Log.d(TAG, "登录API返回: $result")
                
                result.fold(
                    onSuccess = { response ->
                        Log.d(TAG, "登录成功: ${response.code} - ${response.message}")
                        if (response.code == 200 && response.data != null) {
                            // 保存登录信息 - 添加保存account
                            sessionManager.saveAccount(account)  // 添加这行
                            sessionManager.saveToken(response.data.token)
                            sessionManager.saveDefaultId(response.data.defaultId)
                            
                            Log.d(TAG, "登录完成，调用onSuccess")
                            onSuccess()
                        } else {
                            Log.e(TAG, "登录失败: ${response.code} - ${response.message}")
                            onError(response.message ?: "登录失败")
                        }
                    },
                    onFailure = { exception ->
                        Log.e(TAG, "登录请求失败: ${exception.message}", exception)

                        // 详细分析HTTP错误
                        val errorMessage = when {
                            exception is retrofit2.HttpException -> {
                                when (exception.code()) {
                                    401 -> {
                                        Log.e(TAG, "HTTP 401 - 认证失败")
                                        "登录失败：用户名或密码错误，请检查后重试"
                                    }
                                    403 -> {
                                        Log.e(TAG, "HTTP 403 - 账户被禁用")
                                        "账户已被禁用，请联系管理员"
                                    }
                                    404 -> {
                                        Log.e(TAG, "HTTP 404 - 服务不存在")
                                        "登录服务暂时不可用，请稍后重试"
                                    }
                                    429 -> {
                                        Log.e(TAG, "HTTP 429 - 请求过于频繁")
                                        "登录尝试过于频繁，请稍后再试"
                                    }
                                    500 -> {
                                        Log.e(TAG, "HTTP 500 - 服务器内部错误")
                                        "服务器内部错误，请稍后重试"
                                    }
                                    502, 503, 504 -> {
                                        Log.e(TAG, "HTTP ${exception.code()} - 服务器不可用")
                                        "服务器暂时不可用，请稍后重试"
                                    }
                                    else -> {
                                        Log.e(TAG, "HTTP ${exception.code()} - 未知错误")
                                        "登录失败：服务器错误 (${exception.code()})"
                                    }
                                }
                            }
                            exception.message?.contains("timeout", ignoreCase = true) == true -> {
                                Log.e(TAG, "网络超时")
                                "网络连接超时，请检查网络后重试"
                            }
                            exception.message?.contains("connection", ignoreCase = true) == true -> {
                                Log.e(TAG, "连接失败")
                                "无法连接到服务器，请检查网络连接"
                            }
                            exception.message?.contains("host", ignoreCase = true) == true -> {
                                Log.e(TAG, "域名解析失败")
                                "无法解析服务器地址，请检查网络设置"
                            }
                            else -> {
                                Log.e(TAG, "未知登录错误: ${exception.message}")
                                "登录失败：${exception.message ?: "未知错误"}"
                            }
                        }

                        onError(errorMessage)
                    }
                )
            } catch (e: Exception) {
                Log.e(TAG, "登录异常: ${e.message}", e)
                onError("登录异常：${e.message}")
            }
        }
    }
    
    fun register(account: String, password: String, onSuccess: () -> Unit, onError: (String) -> Unit) {
        Log.d(TAG, "开始注册: $account")
        viewModelScope.launch {
            if (account.isEmpty() || password.isEmpty()) {
                onError("账号和密码不能为空")
                return@launch
            }
            
            try {
                Log.d(TAG, "调用注册API")
                val request = RegisterRequest(
                    account = account,
                    password = password
                )
                val result = repository.register(request)
                Log.d(TAG, "注册API返回: $result")
                
                result.fold(
                    onSuccess = { response ->
                        Log.d(TAG, "注册结果: ${response.code} - ${response.message}")
                        if (response.code == 200 && response.data != null) {
                            // 保存注册信息
                            sessionManager.saveToken(response.data.token)
                            sessionManager.saveDefaultId(response.data.defaultId)
                            
                            Log.d(TAG, "注册成功，调用onSuccess")
                            onSuccess()
                        } else {
                            Log.e(TAG, "注册失败: ${response.message}")
                            onError(response.message ?: "注册失败")
                        }
                    },
                    onFailure = {
                        Log.e(TAG, "注册请求失败: ${it.message}", it)
                        // 提供更友好的错误信息
                        val errorMessage = when {
                            it.message?.contains("timeout") == true -> "注册超时，请检查网络连接"
                            it.message?.contains("Unable to resolve host") == true -> "无法解析服务器地址，请检查网络连接"
                            it.message?.contains("Connection refused") == true -> "服务器拒绝连接，请稍后再试"
                            else -> "注册请求失败：${it.message}"
                        }
                        onError(errorMessage)
                    }
                )
            } catch (e: Exception) {
                Log.e(TAG, "注册异常: ${e.message}", e)
                onError("注册异常：${e.message}")
            }
        }
    }
    
    sealed class LoginState {
        object Idle : LoginState()
        object Loading : LoginState()
        data class Success(val message: String) : LoginState()
        data class Error(val message: String) : LoginState()
    }
}


// 添加ViewModel工厂
class UserViewModelFactory(private val context: android.content.Context) : ViewModelProvider.Factory {
    @Suppress("UNCHECKED_CAST")
    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        if (modelClass.isAssignableFrom(UserViewModel::class.java)) {
            val repository = Repository()
            val sessionManager = SessionManager(context)
            return UserViewModel(repository, sessionManager) as T
        }
        throw IllegalArgumentException("Unknown ViewModel class")
 
   }
}






