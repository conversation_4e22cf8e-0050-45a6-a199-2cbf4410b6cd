/ Header Record For PersistentHashMapValueStorage$ #androidx.activity.ComponentActivity androidx.lifecycle.ViewModel java.io.Serializable androidx.lifecycle.ViewModelC Bcom.example.aihealthbutler.AddNewFamilyMemberViewModel.SavingStateC Bcom.example.aihealthbutler.AddNewFamilyMemberViewModel.SavingStateC Bcom.example.aihealthbutler.AddNewFamilyMemberViewModel.SavingStateC Bcom.example.aihealthbutler.AddNewFamilyMemberViewModel.SavingStateC Bcom.example.aihealthbutler.AddNewFamilyMemberViewModel.SavingState$ #androidx.activity.ComponentActivity androidx.lifecycle.ViewModel? >com.example.aihealthbutler.MedicationPlanViewModel.SavingState? >com.example.aihealthbutler.MedicationPlanViewModel.SavingState? >com.example.aihealthbutler.MedicationPlanViewModel.SavingState? >com.example.aihealthbutler.MedicationPlanViewModel.SavingState$ #androidx.activity.ComponentActivity kotlin.Enum kotlin.Enum$ #androidx.activity.ComponentActivity kotlin.Enum$ #androidx.activity.ComponentActivity androidx.lifecycle.ViewModel$ #androidx.activity.ComponentActivity$ #androidx.activity.ComponentActivity androidx.lifecycle.ViewModelA @com.example.aihealthbutler.EditFamilyMemberViewModel.SavingStateA @com.example.aihealthbutler.EditFamilyMemberViewModel.SavingStateA @com.example.aihealthbutler.EditFamilyMemberViewModel.SavingStateA @com.example.aihealthbutler.EditFamilyMemberViewModel.SavingState$ #androidx.activity.ComponentActivity androidx.lifecycle.ViewModel$ #androidx.activity.ComponentActivity$ #androidx.activity.ComponentActivity androidx.lifecycle.ViewModel$ #androidx.activity.ComponentActivity$ #androidx.activity.ComponentActivity androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel$ #androidx.activity.ComponentActivity androidx.lifecycle.ViewModel$ #androidx.activity.ComponentActivity$ #androidx.activity.ComponentActivity$ #androidx.activity.ComponentActivity androidx.lifecycle.ViewModel$ #androidx.activity.ComponentActivity androidx.lifecycle.ViewModel' &com.example.aihealthbutler.QRScanState' &com.example.aihealthbutler.QRScanState' &com.example.aihealthbutler.QRScanState' &com.example.aihealthbutler.QRScanState$ #androidx.activity.ComponentActivity androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel6 5com.example.aihealthbutler.SignInViewModel.LoginState6 5com.example.aihealthbutler.SignInViewModel.LoginState6 5com.example.aihealthbutler.SignInViewModel.LoginState6 5com.example.aihealthbutler.SignInViewModel.LoginState$ #androidx.activity.ComponentActivity androidx.lifecycle.ViewModel4 3com.example.aihealthbutler.UserViewModel.LoginState4 3com.example.aihealthbutler.UserViewModel.LoginState4 3com.example.aihealthbutler.UserViewModel.LoginState4 3com.example.aihealthbutler.UserViewModel.LoginState- ,androidx.lifecycle.ViewModelProvider.Factory androidx.lifecycle.ViewModel$ #androidx.activity.ComponentActivity java.io.Serializable java.io.Serializable java.io.Serializable1 0com.example.aihealthbutler.api.FolderApiResponse1 0com.example.aihealthbutler.api.FolderApiResponse!  com.google.gson.JsonDeserializer, +androidx.camera.core.ImageAnalysis.Analyzer$ #androidx.activity.ComponentActivity kotlin.Enum java.io.Serializable+ *com.example.aihealthbutler.qr.QRCodeResult+ *com.example.aihealthbutler.qr.QRCodeResult1 0com.example.aihealthbutler.qr.QRValidationResult1 0com.example.aihealthbutler.qr.QRValidationResult1 0com.example.aihealthbutler.qr.QRValidationResult: 9com.example.aihealthbutler.speech.SpeechRecognitionEngine kotlin.Enum kotlin.Enum: 9com.example.aihealthbutler.speech.SpeechRecognitionEngine: 9com.example.aihealthbutler.speech.SpeechRecognitionEngine$ #androidx.activity.ComponentActivity okhttp3.Dns kotlin.Enum androidx.lifecycle.ViewModel$ #androidx.activity.ComponentActivity androidx.lifecycle.ViewModel$ #androidx.activity.ComponentActivity androidx.lifecycle.ViewModel6 5com.example.aihealthbutler.SignInViewModel.LoginState6 5com.example.aihealthbutler.SignInViewModel.LoginState6 5com.example.aihealthbutler.SignInViewModel.LoginState6 5com.example.aihealthbutler.SignInViewModel.LoginState$ #androidx.activity.ComponentActivity androidx.lifecycle.ViewModel4 3com.example.aihealthbutler.UserViewModel.LoginState4 3com.example.aihealthbutler.UserViewModel.LoginState4 3com.example.aihealthbutler.UserViewModel.LoginState4 3com.example.aihealthbutler.UserViewModel.LoginState- ,androidx.lifecycle.ViewModelProvider.Factory$ #androidx.activity.ComponentActivity kotlin.Enum$ #androidx.activity.ComponentActivity$ #androidx.activity.ComponentActivity kotlin.Enum$ #androidx.activity.ComponentActivity$ #androidx.activity.ComponentActivity kotlin.Enum$ #androidx.activity.ComponentActivity kotlin.Enum$ #androidx.activity.ComponentActivity kotlin.Enum