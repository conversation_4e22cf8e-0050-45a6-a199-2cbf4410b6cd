package com.example.aihealthbutler.api

import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map

// 添加这个常量定义
private const val PREF_NAME = "AIHealthButlerPrefs"

// 为Context类添加dataStore扩展属性
private val Context.dataStore: DataStore<Preferences> by preferencesDataStore(name = "settings")

class SessionManager(private val context: Context) {
    private val TAG = "SessionManager"

    // 提供公共方法访问context
    fun getContext(): Context = context
    
    // 使用同一个SharedPreferences实例
    private val sharedPreferences: SharedPreferences =
        context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE)
    
    // 使用Flow存储账号信息
    private val _account = MutableStateFlow<String?>(null)
    val account: StateFlow<String?> = _account
    
    // 使用Flow存储token信息
    private val _token = MutableStateFlow<String?>(null)
    val token: Flow<String?> = _token
    
    // 使用Flow存储默认ID信息
    private val _defaultId = MutableStateFlow<String?>(null)
    val defaultId: Flow<String?> = _defaultId
    
    // 添加个人信息缓存
    private val _cachedPersonInfo = MutableStateFlow<PersonInfoData?>(null)
    val cachedPersonInfo: StateFlow<PersonInfoData?> = _cachedPersonInfo

    companion object {
        private const val KEY_ACCOUNT = "account"
        private const val KEY_TOKEN = "token"
        private const val KEY_DEFAULT_ID = "default_id"
        private const val KEY_CACHED_PERSON_NAME = "cached_person_name"
        private const val KEY_CACHED_PERSON_SEX = "cached_person_sex"
        private const val KEY_CACHED_PERSON_BIRTHDAY = "cached_person_birthday"

        // DataStore Keys
        val TOKEN_KEY = stringPreferencesKey("token")
        val DEFAULT_ID_KEY = stringPreferencesKey("default_id")
    }
    
    init {
        // 初始化时从SharedPreferences加载数据
        loadData()
    }
    
    private fun loadData() {
        // 从SharedPreferences加载数据到Flow
        val savedAccount = sharedPreferences.getString(KEY_ACCOUNT, null)
        val savedToken = sharedPreferences.getString(KEY_TOKEN, null)
        val savedDefaultId = sharedPreferences.getString(KEY_DEFAULT_ID, null)
        
        Log.d(TAG, "加载保存的数据: account=$savedAccount, token=${savedToken?.take(10)}, defaultId=$savedDefaultId")
        
        _account.value = savedAccount
        _token.value = savedToken
        _defaultId.value = savedDefaultId
    }
    
    fun saveAccount(account: String) {
        sharedPreferences.edit().putString(KEY_ACCOUNT, account).apply()
        _account.value = account
        Log.d(TAG, "保存账号: $account")
    }
    
    fun saveToken(token: String) {
        sharedPreferences.edit().putString(KEY_TOKEN, token).apply()
        _token.value = token
        Log.d(TAG, "保存token: ${token.take(10)}...")
    }
    
    fun saveDefaultId(defaultId: String) {
        sharedPreferences.edit().putString(KEY_DEFAULT_ID, defaultId).apply()
        _defaultId.value = defaultId
        Log.d(TAG, "保存defaultId: $defaultId")
    }
    
    fun clearAll() {
        sharedPreferences.edit().clear().apply()
        _account.value = null
        _token.value = null
        _defaultId.value = null
        Log.d(TAG, "清除所有数据")
    }
    
    fun isLoggedIn(): Boolean {
        return !_token.value.isNullOrEmpty() && !_account.value.isNullOrEmpty()
    }
    
    // 格式化token
    fun formatToken(token: String): String {
        return if (!token.startsWith("Bearer ")) "Bearer $token" else token
    }

    // 缓存个人信息
    fun cachePersonInfo(personInfo: PersonInfoData) {
        _cachedPersonInfo.value = personInfo

        // 同时保存到SharedPreferences持久化
        sharedPreferences.edit().apply {
            putString(KEY_CACHED_PERSON_NAME, personInfo.name)
            putString(KEY_CACHED_PERSON_SEX, personInfo.sexText)
            putString(KEY_CACHED_PERSON_BIRTHDAY, personInfo.birthday)
            apply()
        }
        Log.d(TAG, "缓存个人信息: ${personInfo.name}")
    }

    // 获取缓存的个人信息
    fun getCachedPersonInfo(): PersonInfoData? {
        if (_cachedPersonInfo.value != null) {
            return _cachedPersonInfo.value
        }

        // 从SharedPreferences恢复
        val name = sharedPreferences.getString(KEY_CACHED_PERSON_NAME, null)
        val sex = sharedPreferences.getString(KEY_CACHED_PERSON_SEX, null)
        val birthday = sharedPreferences.getString(KEY_CACHED_PERSON_BIRTHDAY, null)

        if (name != null && sex != null && birthday != null) {
            val cachedInfo = PersonInfoData(
                defaultId = _defaultId.value ?: "",
                name = name,
                sex = if (sex == "男") 1 else 0, // 从文本转换为数字
                sexText = sex,
                birthday = birthday,
                age = 0, // 缓存时不计算年龄
                height = 0, // 缓存时不保存身高体重
                weight = 0f,
                bmi = 0f,
                account = _account.value ?: ""
            )
            _cachedPersonInfo.value = cachedInfo
            Log.d(TAG, "从缓存恢复个人信息: $name")
            return cachedInfo
        }

        return null
    }

    // 清除个人信息缓存
    fun clearPersonInfoCache() {
        _cachedPersonInfo.value = null
        sharedPreferences.edit().apply {
            remove(KEY_CACHED_PERSON_NAME)
            remove(KEY_CACHED_PERSON_SEX)
            remove(KEY_CACHED_PERSON_BIRTHDAY)
            apply()
        }
        Log.d(TAG, "清除个人信息缓存")
    }
}

