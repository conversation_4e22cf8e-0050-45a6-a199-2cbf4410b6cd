# 接口文档整理

## 注册接口
### 用户注册接口
- **接口地址**: `POST /api/register`
- **接口说明**: 用于新用户注册账号。
- **Content-Type**: `application/json`

#### 请求参数
| 参数名 | 类型 | 是否必填 | 示例值 | 说明 |
| ---- | ---- | ---- | ---- | ---- |
| account | string | 是 | user1 | 用户账号 |
| password | string | 是 | 123456 | 用户密码 |

#### 请求示例（JSON）
```json
{
  "account": "user1",
  "password": "123456"
}
```

#### 响应字段
| 字段名 | 类型 | 示例值 | 说明 |
| ---- | ---- | ---- | ---- |
| code | int | 200 | 响应状态码 |
| message | string | 注册成功 | 响应消息 |
| data.token | string | JWT_TOKEN | 登录凭证 |
| data.defaultId | string | user2_20250506195636_0849634e | 该用户的默认家庭成员ID |
| timestamp | long | ************* | 时间戳 |

#### 响应示例（200 OK）
```json
{
  "code": 200,
  "message": "注册成功",
  "data": {
    "token": "eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9...snip...",
    "defaultId": "user2_20250506195636_0849634e"
  },
  "timestamp": *************
}
```

## 登录接口
### 用户登录接口
- **接口地址**: `POST /api/login`
- **接口说明**: 用户登录获取 token。
- **Content-Type**: `application/json`

#### 请求参数
| 参数名 | 类型 | 是否必填 | 示例值 | 说明 |
| ---- | ---- | ---- | ---- | ---- |
| account | string | 是 | user1 | 用户账号 |
| password | string | 是 | 123456 | 用户密码 |

#### 请求示例（JSON）
```json
{
  "account": "user1",
  "password": "123456"
}
```

#### 响应字段
| 字段名 | 类型 | 示例值 | 说明 |
| ---- | ---- | ---- | ---- |
| code | int | 200 | 响应状态码 |
| message | string | 登录成功 | 响应消息 |
| data.token | string | JWT_TOKEN | 登录凭证 |
| data.defaultId | string | user1_20250506195636_0849634e | 默认家庭成员ID |
| timestamp | long | ************* | 时间戳 |

#### 响应示例（200 OK）
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9...snip...",
    "defaultId": "user1_20250506195636_0849634e"
  },
  "timestamp": *************
}
```

## 添加账号其他成员
### 添加账号下家庭成员
向现有账号下添加一个新的家庭成员。新成员将继承主账号的密码，但拥有独立的 `defaultId`。

#### 请求信息
- **接口路径**: `/api/add-member`
- **请求方法**: `POST`
- **Content-Type**: `application/json`

#### 请求参数
| 参数名 | 类型 | 必填 | 描述 |
| ---- | ---- | ---- | ---- |
| account | String | 是 | 主账号名称 |

#### 请求示例
```json
{
  "account": "user123"
}
```

#### 响应参数
| 参数名 | 类型 | 描述 |
| ---- | ---- | ---- |
| code | Integer | 状态码，200表示成功 |
| message | String | 响应消息 |
| data | Object | 响应数据 |
| data.default_id | String | 新成员的唯一标识 |
| timestamp | Long | 时间戳 |

#### 响应示例
```json
{
  "code": 200,
  "message": "添加成功",
  "data": {
    "default_id": "member456"
  },
  "timestamp": *************
}
```

## 切换家庭成员
在同一账号下的不同家庭成员之间进行切换，并获取新的身份令牌。

### 请求信息
- **接口路径**: `/api/switch-member`
- **请求方法**: `POST`
- **Content-Type**: `application/json`

### 请求参数
| 参数名 | 类型 | 必填 | 描述 |
| ---- | ---- | ---- | ---- |
| account | String | 是 | 账号名称 |
| defaultId | String | 是 | 要切换到的成员ID |

### 请求示例
```json
{
  "account": "user123",
  "defaultId": "member456"
}
```

### 响应参数
| 参数名 | 类型 | 描述 |
| ---- | ---- | ---- |
| code | Integer | 状态码，200表示成功 |
| message | String | 响应消息 |
| data | Object | 响应数据 |
| data.token | String | 新的身份令牌 |
| data.defaultId | String | 切换后的成员ID |
| timestamp | Long | 时间戳 |

### 响应示例
```json
{
  "code": 200,
  "message": "切换成功",
  "data": {
    "token": "eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9...",
    "defaultId": "member456"
  },
  "timestamp": *************
}
```

### 错误响应
| 错误场景 | 响应示例 |
| ---- | ---- |
| 账号不存在 | `{ "code": 404, "message": "账号不存在" }` |
| 成员ID不匹配 | `{ "code": 400, "message": "成员ID不匹配" }` |

### 注意事项
- 添加新成员时，新成员将继承主账号的密码。
- 切换成员后需要使用新的 token 进行后续请求。
- token 需要在请求头中携带，格式如下：
  ```
  Authorization: Bearer {token}
  ```

## 创建个人信息
### 创建个人信息接口
- **接口地址**: `POST /api/person/user`
- **接口说明**: 为指定 defaultId 创建新的家庭成员信息。
- **认证方式**: `Bearer Token`
- **Content-Type**: `application/json`

#### 请求参数
| 参数名 | 类型 | 是否必填 | 示例值 | 说明 |
| ---- | ---- | ---- | ---- | ---- |
| name | string | 是 | 张三 | 姓名 |
| sex | int | 是 | 1 | 性别: 0女, 1男 |
| birthday | date | 是 | 1990-01-01 | 出生日期 |
| height | int | 是 | 175 | 身高(cm) |
| weight | float | 是 | 70 | 体重(kg) |

#### 请求示例
```json
{
  "name": "张三",
  "sex": 1,
  "birthday": "1990-01-01",
  "height": 175,
  "weight": 70
}
```

#### 响应示例
```json
{
  "code": 200,
  "message": "创建个人信息成功",
  "data": {
    "defaultId": "user1_20250506195636_0849634e",
    "name": "张三",
    "sex": 1,
    "sexText": "男",
    "birthday": "1990-01-01",
    "age": 35,
    "height": 175,
    "weight": 70,
    "bmi": 22.9,
    "account": "user1",
    "consultants": 0
  },
  "timestamp": *************
}
```

## 删除个人信息
### 删除个人信息接口
- **接口地址**: `DELETE /api/person/{defaultId}`
- **接口说明**: 根据 defaultId 删除该成员信息
- **认证方式**: `Bearer Token`

#### 响应示例
```json
{
  "code": 200,
  "message": "删除个人信息成功",
  "data": true,
  "timestamp": *************
}
```

## 更新个人信息
### 更新个人信息接口
- **接口地址**: `PUT /api/person/{defaultId}`
- **接口说明**: 修改指定成员的信息
- **认证方式**: `Bearer Token`
- **Content-Type**: `application/json`

#### 请求参数
| 参数名 | 类型 | 是否必填 | 示例值 | 说明 |
| ---- | ---- | ---- | ---- | ---- |
| name | string | 否 | 李四 | 姓名 |
| sex | int | 否 | 1 | 性别 |
| birthday | date | 否 | 1990-01-01 | 出生日期 |
| height | int | 否 | 180 | 身高(cm) |
| weight | float | 否 | 75 | 体重(kg) |
| role | string | 否 | 父亲 | 家庭角色 |

#### 请求示例
```json
{
  "name": "李四",
  "sex": 1,
  "birthday": "1990-01-01",
  "height": 180,
  "weight": 75,
  "role": "父亲"
}
```

#### 响应示例
```json
{
  "code": 200,
  "message": "更新个人信息成功",
  "data": {
    "defaultId": "user1_20250506195636_0849634e",
    "name": "李四",
    "sex": 1,
    "sexText": "男",
    "birthday": "1990-01-01",
    "age": 35,
    "height": 180,
    "weight": 75,
    "bmi": 23.1,
    "account": "user1",
    "consultants": 0
  },
  "timestamp": *************
}
```

## 获取个人信息
### 获取个人信息接口
- **接口地址**: `GET /api/person/{defaultId}`
- **接口说明**: 根据 defaultId 查询家庭成员详细信息。
- **认证方式**: `Bearer Token`

#### 请求参数
| 参数名 | 类型 | 是否必填 | 示例值 | 说明 |
| ---- | ---- | ---- | ---- | ---- |
| defaultId | string | 是 | user1_20250506195636_0849634e | 成员唯一ID |

#### 响应示例
```json
{
  "code": 200,
  "message": "获取个人信息成功",
  "data": {
    "defaultId": "user1_20250506195636_0849634e",
    "name": "张三",
    "sex": 1,
    "sexText": "男",
    "birthday": "1989-12-31",
    "age": 35,
    "height": 175,
    "weight": 70,
    "bmi": 22.9,
    "account": "user1",
    "consultants": 0
  },
  "timestamp": *************
}
```

## 创建家庭关系
### 创建家庭关系接口
- **接口地址**: `POST /api/family/user`
- **接口说明**: 为当前账号添加一个默认ID成员的家庭关系。
- **认证方式**: `Bearer Token`
- **Content-Type**: `application/json`

#### 请求参数
| 参数名 | 类型 | 是否必填 | 示例值 | 说明 |
| ---- | ---- | ---- | ---- | ---- |
| role | string | 是 | 父亲 | 家庭成员角色 |

#### 请求示例
```json
{
  "role": "父亲"
}
```

#### 响应示例
```json
{
  "code": 200,
  "message": "添加家庭关系成功",
  "data": {
    "account": "user1",
    "defaultId": "user1_20250506195636_0849634e",
    "role": "父亲"
  },
  "timestamp": *************
}
```

## 删除家庭关系
### 删除家庭关系接口
- **接口地址**: `DELETE /api/family/{defaultId}`
- **接口说明**: 删除指定 defaultId 成员的家庭角色绑定。
- **认证方式**: `Bearer Token`

#### 请求参数
| 参数名 | 类型 | 是否必填 | 示例值 | 说明 |
| ---- | ---- | ---- | ---- | ---- |
| defaultId | string | 是 | user1_20250506195636_0849634e | 成员唯一ID |

#### 响应示例
```json
{
  "code": 200,
  "message": "删除家庭关系成功",
  "data": true,
  "timestamp": *************
}
```

### 更改家庭关系
#### 更改家庭关系接口
- **接口地址**: `PUT /api/family/{defaultId}`
- **接口说明**: 修改指定成员的家庭角色信息。
- **认证方式**: `Bearer Token`
- **Content-Type**: `application/json`

##### 请求参数
| 参数名 | 类型 | 是否必填 | 示例值 | 说明 |
| ---- | ---- | ---- | ---- | ---- |
| defaultId | string | 是 | user1_20250506195636_0849634e | 成员唯一ID |

##### 请求示例（JSON）
```json
{
  "role": "母亲"
}
```

##### 响应示例
```json
{
  "code": 200,
  "message": "更新家庭关系成功",
  "data": {
    "account": "user1",
    "defaultId": "user1_20250506195636_0849634e",
    "role": "母亲"
  },
  "timestamp": *************
}
```

### 获取家庭关系
#### 获取家庭关系接口
- **接口地址**: `GET /api/family/{defaultId}`
- **接口说明**: 获取特定成员的家庭角色信息。
- **认证方式**: `Bearer Token`

##### 请求参数
| 参数名 | 类型 | 是否必填 | 示例值 | 说明 |
| ---- | ---- | ---- | ---- | ---- |
| defaultId | string | 是 | user1_20250506195636_0849634e | 成员唯一ID |

##### 响应示例
```json
{
  "code": 200,
  "message": "获取家庭信息成功",
  "data": {
    "account": "user1",
    "defaultId": "user1_20250506195636_0849634e",
    "role": "母亲"
  },
  "timestamp": *************
}
```

### 获取账号下所有家庭成员信息
#### 获取账号下所有家庭成员信息
- **接口地址**: `GET /api/family/account/{account}`
- **接口说明**: 查看该账号下所有已绑定的家庭成员信息（含 person）。
- **认证方式**: `Bearer Token`

##### 请求参数
| 参数名 | 类型 | 是否必填 | 示例值 | 说明 |
| ---- | ---- | ---- | ---- | ---- |
| account | string | 是 | user1 | 用户账号 |

##### 响应参数
| 参数名 | 类型 | 示例值 | 说明 |
| ---- | ---- | ---- | ---- |
| code | int | 200 | 状态码，200 表示成功 |
| message | string | 获取账号所有家庭信息成功 | 接口返回提示信息 |
| data | array | - | 家庭成员列表 |
| defaultId | string | user1_20250506195636_0849634e | 成员唯一 ID |
| name | string | 李四 | 姓名 |
| sex | int | 1 | 性别（0 女，1 男） |
| sexText | string | 男 | 性别文本 |
| birthday | string | 1990-01-01 | 出生日期 |
| age | int | 35 | 年龄 |
| height | int | 180 | 身高(cm) |
| weight | float | 75 | 体重(kg) |
| bmi | float | 23.1 | 身体质量指数（自动计算） |
| account | string | user1 | 所属账号 |
| consultants | int | 0 | 咨询次数 |
| role | string | 母亲 | 家庭角色 |
| timestamp | long | ************* | 返回时间戳 |

##### 响应示例
```json
{
  "code": 200,
  "message": "获取账号所有家庭信息成功",
  "data": [
    {
      "defaultId": "user1_20250506195636_0849634e",
      "name": "李四",
      "sex": 1,
      "sexText": "男",
      "birthday": "1990-01-01",
      "age": 35,
      "height": 180,
      "weight": 75,
      "bmi": 23.1,
      "account": "user1",
      "consultants": 0,
      "role": "母亲"
    }
  ],
  "timestamp": *************
}
```

### 创建健康信息
#### 创建健康信息
- **URL**: `POST http://localhost:8080/api/health/create`
- **请求类型**: `application/json`
- **鉴权方式**: `Bearer Token`

##### 请求参数（JSON）
| 参数名 | 类型 | 描述 |
| ---- | ---- | ---- |
| defaultId | string | 用户唯一标识 |
| medicalCondition | string | 当前病症 |
| allergyHistory | string | 过敏史 |
| vaccinationHistory | string | 疫苗接种情况 |
| smokingStatus | string | 吸烟情况 |
| drinkingStatus | string | 饮酒情况 |
| familyMedicalHistory | string | 家族病史 |
| menstrualStatus | string | 月经情况 |
| pregnancyStatus | string | 是否怀孕 |

##### 请求示例
```json
{
  "defaultId": "d60a91b0ada14aa7",
  "medicalCondition": "高血压",
  "allergyHistory": "花粉过敏",
  "vaccinationHistory": "已完成新冠疫苗接种",
  "smokingStatus": "从不",
  "drinkingStatus": "偶尔",
  "familyMedicalHistory": "父亲有糖尿病史",
  "menstrualStatus": "规律",
  "pregnancyStatus": "否"
}
```

##### 响应参数
| 参数名 | 类型 | 描述 |
| ---- | ---- | ---- |
| code | number | 响应码，200 表示成功 |
| message | string | 响应信息 |
| data | any | 返回数据 |
| timestamp | number | 时间戳 |

##### 响应示例
```json
{
  "code": 200,
  "message": "健康信息创建成功",
  "data": null,
  "timestamp": 1748278621072
}
```

### 删除健康信息
#### 删除健康信息
- **接口地址**: `DELETE /api/health/{healthId}`
- **接口说明**: 根据 healthId 删除健康信息记录。
- **鉴权方式**: `Bearer Token`

##### 路径参数
| 参数名 | 类型 | 描述 |
| ---- | ---- | ---- |
| healthId | number | 健康信息 ID |

##### 响应示例
```json
{
  "code": 200,
  "message": "健康信息删除成功",
  "data": null,
  "timestamp": 1748272170446
}
```

### 更新健康信息
#### 更新健康信息
- **接口地址**: `PUT /api/health`
- **接口说明**: 更新指定健康信息记录。
- **请求类型**: `application/json`
- **鉴权方式**: `Bearer Token`

##### 请求参数（JSON）
同创建接口，新增字段：
| 参数名 | 类型 | 描述 |
| ---- | ---- | ---- |
| healthId | number | 健康记录 ID |

##### 请求示例
```json
{
  "healthId": 1,
  "defaultId": "d60a91b0ada14aa7",
  "medicalCondition": "高血压，糖尿病",
  "allergyHistory": "花粉过敏，海鲜过敏",
  "vaccinationHistory": "已完成新冠疫苗接种",
  "smokingStatus": "从不",
  "drinkingStatus": "从不",
  "familyMedicalHistory": "父亲无糖尿病史",
  "menstrualStatus": "规律",
  "pregnancyStatus": "否"
}
```

##### 响应示例
```json
{
  "code": 200,
  "message": "健康信息更新成功",
  "data": null,
  "timestamp": 1748272842658
}
```

### 获取用户健康信息
#### 获取用户健康信息
- **接口地址**: `GET /api/health/{defaultId}`
- **接口说明**: 根据 defaultId 获取健康信息。
- **鉴权方式**: `Bearer Token`

##### 路径参数
| 参数名 | 类型 | 描述 |
| ---- | ---- | ---- |
| defaultId | string | 用户唯一标识 |

##### 响应示例
```json
{
  "code": 200,
  "message": "获取健康信息成功",
  "data": {
    "healthId": 1,
    "medicalCondition": "高血压，糖尿病",
    "allergyHistory": "花粉过敏，海鲜过敏",
    "vaccinationHistory": "已完成新冠疫苗接种",
    "smokingStatus": "从不",
    "drinkingStatus": "从不",
    "familyMedicalHistory": "父亲无糖尿病史",
    "menstrualStatus": "规律",
    "pregnancyStatus": "否",
    "defaultId": "d60a91b0ada14aa7",
    "surgeryHistories": [
      {
        "surgeryId": 1,
        "healthId": 1,
        "surgeryName": "阑尾切除术",
        "surgeryDate": "2024-03-19T16:00:00.000+08:00",
        "surgeryDescription": "急性阑尾炎手术"
      }
    ]
  },
  "timestamp": 1748272095525
}
```

### 添加手术史记录
#### 添加手术史记录
- **URL**: `POST http://localhost:8080/api/health/surgery`
- **请求类型**: `application/json`
- **鉴权方式**: `Bearer Token`

##### 请求参数（JSON）
| 参数名 | 类型 | 描述 |
| ---- | ---- | ---- |
| healthId | number | 健康信息 ID |
| surgeryName | string | 手术名称 |
| surgeryDate | string | 手术日期（yyyy-MM-dd） |
| surgeryDescription | string | 手术说明 |

##### 请求示例
```json
{
  "healthId": 1,
  "surgeryName": "阑尾切除术",
  "surgeryDate": "2025-03-20",
  "surgeryDescription": "急性阑尾炎手术"
}
```

##### 响应示例
```json
{
  "code": 200,
  "message": "手术史记录添加成功",
  "data": null,
  "timestamp": 1748271125977
}
```

### 删除手术史记录
#### 删除手术史记录
- **URL**: `DELETE http://localhost:8080/api/health/surgery/{surgeryId}`
- **鉴权方式**: `Bearer Token`

##### 路径参数
| 参数名 | 类型 | 描述 |
| ---- | ---- | ---- |
| surgeryId | number | 手术记录 ID |

##### 响应示例
```json
{
  "code": 200,
  "message": "手术史记录删除成功",
  "data": null,
  "timestamp": 1748271399322
}
```

### 更新手术史记录
#### 更新手术史记录
- **URL**: `PUT http://localhost:8080/api/health/surgery`
- **请求类型**: `application/json`
- **鉴权方式**: `Bearer Token`

##### 请求参数（JSON）
| 参数名 | 类型 | 描述 |
| ---- | ---- | ---- |
| surgeryId | number | 手术记录 ID |
| healthId | number | 健康信息 ID |
| surgeryName | string | 手术名称 |
| surgeryDate | string | 手术日期 |
| surgeryDescription | string | 手术说明 |

##### 请求示例
```json
{
  "surgeryId": 2,
  "healthId": 1,
  "surgeryName": "阑尾切除术",
  "surgeryDate": "2024-06-20",
  "surgeryDescription": "更新后的手术说明"
}
```

##### 响应示例
```json
{
  "code": 200,
  "message": "手术史记录更新成功",
  "data": null,
  "timestamp": 1748271221467
}
```

### 获取手术史记录列表
#### 获取手术史记录列表
- **URL**: `GET http://localhost:8080/api/health/surgery/{healthId}`
- **鉴权方式**: `Bearer Token`

##### 路径参数
| 参数名 | 类型 | 描述 |
| ---- | ---- | ---- |
| healthId | number | 健康信息 ID |

##### 响应示例
```json
{
  "code": 200,
  "message": "获取手术史记录成功",
  "data": [
    {
      "surgeryId": 2,
      "healthId": 1,
      "surgeryName": "阑尾切除术",
      "surgeryDate": "2024-06-19T16:00:00.000+08:00",
      "surgeryDescription": "更新后的手术说明"
    },
    {
      "surgeryId": 1,
      "healthId": 1,
      "surgeryName": "阑尾切除术",
      "surgeryDate": "2024-03-19T16:00:00.000+08:00",
      "surgeryDescription": "急性阑尾炎手术"
    }
  ],
  "timestamp": 1748271263862
}
```

### 创建用药计划
#### 创建用药计划
- **接口地址**: `POST /api/mplans`
- **接口说明**: 为指定家庭成员创建用药计划。
- **请求类型**: `application/json`

##### 请求参数
| 参数名 | 类型 | 是否必填 | 示例值 | 说明 |
| ---- | ---- | ---- | ---- | ---- |
| pillName | string | 是 | 阿司匹林 | 药品名称 |
| startDate | string | 是 | 2023-05-15 | 开始日期 |
| duration | int | 是 | 30 | 用药天数 |
| method | string | 是 | 口服 | 给药方式 |
| frequency | string | 是 | 每日 | 用药频率 |
| frequencyDetail | int | 是 | 3 | 每天次数 |
| firstTime | string | 是 | 2023-05-15T08:00:00 | 第一次服药时间 |
| secondTime | string | 否 | 2023-05-15T12:00:00 | 第二次服药时间 |
| thirdTime | string | 否 | 2023-05-15T18:00:00 | 第三次服药时间 |
| dosage | string | 是 | 100mg | 每次剂量 |
| guide | string | 否 | 饭后服用 | 用药指导 |
| note | string | 否 | 如有不适请停止使用 | 备注 |
| defaultId | string | 是 | user1_20250506195636_0849634e | 家庭成员 ID |

##### 请求示例
```json
{
  "pillName": "阿司匹林",
  "startDate": "2023-05-15",
  "duration": 30,
  "method": "口服",
  "frequency": "每日",
  "frequencyDetail": 3,
  "firstTime": "2023-05-15T08:00:00",
  "secondTime": "2023-05-15T12:00:00",
  "thirdTime": "2023-05-15T18:00:00",
  "dosage": "100mg",
  "guide": "饭后服用",
  "note": "如有不适请停止使用",
  "defaultId": "user1_20250506195636_0849634e"
}
```

##### 响应参数
| 字段名 | 类型 | 示例值 | 说明 |
| ---- | ---- | ---- | ---- |
| mplanId | int | 5 | 用药计划 ID |
| 其它字段 | 同请求 | 同请求字段 | 用药详情 |

##### 响应示例
```json
{
  "mplanId": 5,
  "pillName": "阿司匹林",
  "startDate": "2023-05-15",
  "duration": 30,
  "method": "口服",
  "frequency": "每日",
  "frequencyDetail": 3,
  "firstTime": "2023-05-15T08:00:00",
  "secondTime": "2023-05-15T12:00:00",
  "thirdTime": "2023-05-15T18:00:00",
  "dosage": "100mg",
  "guide": "饭后服用",
  "note": "如有不适请停止使用",
  "defaultId": "user1_20250506195636_0849634e"
}
```

### 删除用药计划
#### 删除用药计划
- **接口地址**: `DELETE /api/mplans/{mplanId}`
- **接口说明**: 删除指定的用药计划。

##### 请求参数
| 参数名 | 类型 | 是否必填 | 示例值 | 说明 |
| ---- | ---- | ---- | ---- | ---- |
| mplanId | int | 是 | 5 | 用药计划 ID |

##### 响应示例
```json
{
  "code": 200,
  "message": "删除成功",
  "data": null
}
```

### 批量删除用药计划
#### 批量删除用药计划
- **接口地址**: `DELETE /api/mplans/batch`
- **请求类型**: `application/json`

##### 请求参数
传入需删除的用药计划 ID 数组：
```json
[1, 2, 3]
```

##### 响应示例
```json
{
  "code": 200,
  "message": "批量删除成功",
  "data": null
}
```

### 更新用药计划
#### 更新用药计划
- **接口地址**: `PUT /api/mplans/{mplanId}`
- **接口说明**: 更新指定的用药计划。
- **请求类型**: `application/json`

##### 请求参数
| 参数名 | 类型 | 是否必填 | 示例值 | 说明 |
| ---- | ---- | ---- | ---- | ---- |
| pillName | string | 是 | 阿司匹林（更新） | 药品名称 |
| startDate | string | 是 | 2023-05-15 | 开始日期 |
| duration | int | 是 | 15 | 用药天数 |
| method | string | 是 | 口服 | 给药方式 |
| frequency | string | 是 | 每日 | 用药频率 |
| frequencyDetail | int | 是 | 2 | 每天次数 |
| firstTime | string | 是 | 2023-05-15T09:00:00 | 第一次服药时间 |
| secondTime | string | 否 | 2023-05-15T12:00:00 | 第二次服药时间 |
| thirdTime | string | 否 | 2023-05-15T18:00:00 | 第三次服药时间 |
| dosage | string | 是 | 50mg | 每次剂量 |
| guide | string | 否 | 饭后服用 | 用药指导 |
| note | string | 否 | 更新后的备注 | 备注 |
| defaultId | string | 是 | user1_20250506195636_0849634e | 家庭成员 ID |

##### 请求示例
```json
{
  "pillName": "阿司匹林（更新）",
  "startDate": "2023-05-15",
  "duration": 15,
  "method": "口服",
  "frequency": "每日",
  "frequencyDetail": 2,
  "firstTime": "2023-05-15T09:00:00",
  "secondTime": "2023-05-15T12:00:00",
  "thirdTime": "2023-05-15T18:00:00",
  "dosage": "50mg",
  "guide": "饭后服用",
  "note": "更新后的备注",
  "defaultId": "user1_20250506195636_0849634e"
}
```

##### 响应参数
| 字段名 | 类型 | 示例值 | 说明 |
| ---- | ---- | ---- | ---- |
| mplanId | int | 5 | 用药计划 ID |
| 其它字段 | 同请求 | 同请求字段 | 用药详情 |

##### 响应示例
```json
{
  "mplanId": 5,
  "pillName": "阿司匹林（更新）",
  "startDate": "2023-05-15",
  "duration": 15,
  "method": "口服",
  "frequency": "每日",
  "frequencyDetail": 2,
  "firstTime": "2023-05-15T09:00:00",
  "secondTime": "2023-05-15T12:00:00",
  "thirdTime": "2023-05-15T18:00:00",
  "dosage": "50mg",
  "guide": "饭后服用",
  "note": "更新后的备注",
  "defaultId": "user1_20250506195636_0849634e"
}
```

### 获取单个用药计划
#### 获取单个用药计划
- **接口地址**: `GET /api/mplans/{mplanId}`
- **接口说明**: 获取指定用药计划详情。

##### 响应参数
| 字段名 | 类型 | 示例值 | 说明 |
| ---- | ---- | ---- | ---- |
| mplanId | int | 5 | 用药计划 ID |
| 其它字段 | 同请求 | 同请求字段 | 用药详情 |

##### 响应示例
```json
{
  "mplanId": 5,
  "pillName": "阿司匹林",
  "startDate": "2023-05-15",
  "duration": 30,
  "method": "口服",
  "frequency": "每日",
  "frequencyDetail": 3,
  "firstTime": "2023-05-15T08:00:00",
  "secondTime": "2023-05-15T12:00:00",
  "thirdTime": "2023-05-15T18:00:00",
  "dosage": "100mg",
  "guide": "饭后服用",
  "note": "如有不适请停止使用",
  "defaultId": "user1_20250506195636_0849634e"
}
```

### 获取用户所有用药计划
#### 获取用户所有用药计划
- **接口地址**: `GET /api/mplans/user/{defaultId}`
- **接口说明**: 获取指定用户的全部用药计划。

##### 响应参数
返回用药计划对象数组。

##### 响应示例
```json
[
  {
    "mplanId": 5,
    "pillName": "阿司匹林",
    "startDate": "2023-05-15",
    "duration": 30,
    "method": "口服",
    "frequency": "每日",
    "frequencyDetail": 3,
    "firstTime": "2023-05-15T08:00:00",
    "secondTime": "2023-05-15T12:00:00",
    "thirdTime": "2023-05-15T18:00:00",
    "dosage": "100mg",
    "guide": "饭后服用",
    "note": "如有不适请停止使用",
    "defaultId": "user1_20250506195636_0849634e"
  }
]
```

### 创建资料夹
#### 创建资料夹
- **接口地址**: `POST /api/folder`
- **接口说明**: 用于创建新的资料夹，上传图片路径。
- **请求类型**: `application/json`

##### 请求参数
| 参数名 | 类型 | 是否必填 | 示例值 | 说明 |
| ---- | ---- | ---- | ---- | ---- |
| picture | string | 是 | D:\Users\HP\Desktop\aie\image\img.png | 图片路径（本地） |

##### 请求示例
```json
{
  "picture": "D:\\Users\\HP\\Desktop\\aie\\image\\img.png"
}
```

##### 响应参数
| 字段名 | 类型 | 示例值 | 说明 |
| ---- | ---- | ---- | ---- |
| code | int | 200 | 状态码 |
| message | string | 创建资料夹成功 | 响应消息 |
| data.folderId | int | 1 | 资料夹 ID |
| data.picture | string | 图片路径 | 图片路径 |
| data.defaultId | string | user1_20250506195636_0849634e | 用户 ID |
| timestamp | long | 1747189083718 | 时间戳 |

##### 响应示例
```json
{
  "code": 200,
  "message": "创建资料夹成功",
  "data": {
    "folderId": 1,
    "picture": "D:\\Users\\HP\\Desktop\\aie\\image\\img.png",
    "defaultId": "user1_20250506195636_0849634e"
  },
  "timestamp": 1747189083718
}
```

### 删除资料夹
#### 删除资料夹
- **接口地址**: `DELETE /api/folder/{folderId}`

##### 响应参数
| 字段名 | 类型 | 示例值 | 说明 |
| ---- | ---- | ---- | ---- |
| code | int | 200 | 状态码 |
| message | string | 删除资料夹成功 | 响应信息 |
| data | bool | true | 是否成功 |
| timestamp | long | 1747189449042 | 时间戳 |

##### 响应示例
```json
{
  "code": 200,
  "message": "删除资料夹成功",
  "data": true,
  "timestamp": 1747189449042
}
```

### 根据用户id删除资料夹
#### 根据用户 ID 删除资料夹
- **接口地址**: `DELETE /api/folder/user/{defaultId}`

##### 响应示例
```json
{
  "code": 200,
  "message": "删除资料夹成功",
  "data": true,
  "timestamp": 1747189491589
}
```

### 更新资料夹
#### 更新资料夹
- **接口地址**: `PUT /api/folder/{folderId}`

##### 请求参数
| 参数名 | 类型 | 是否必填 | 示例值 | 说明 |
| ---- | ---- | ---- | ---- | ---- |
| picture | string | 是 | D:\Users\HP\Desktop\aie\image\img.png | 图片路径（本地） |

##### 请求示例
```json
{
  "picture": "D:\\Users\\HP\\Desktop\\aie\\image1\\img.png"
}
```

##### 响应参数
| 字段名 | 类型 | 示例值 | 说明 |
| ---- | ---- | ---- | ---- |
| code | int | 200 | 状态码 |
| message | string | 更新资料夹成功 | 响应消息 |
| data.folderId | int | 1 | 资料夹 ID |
| data.picture | string | 图片路径 | 图片路径 |
| data.defaultId | string | user1_20250506195636_0849634e | 用户 ID |
| timestamp | long | 1747189268681 | 时间戳 |

##### 响应示例
```json
{
  "code": 200,
  "message": "更新资料夹成功",
  "data": {
    "folderId": 1,
    "picture": "D:\\Users\\HP\\Desktop\\aie\\image1\\img.png",
    "defaultId": "user1_20250506195636_0849634e"
  },
  "timestamp": 1747189268681
}
```

### 获取单个资料夹
#### 获取单个资料夹
- **接口地址**: `GET /api/folder/{folderId}`

##### 响应参数
| 字段名 | 类型 | 示例值 | 说明 |
| ---- | ---- | ---- | ---- |
| code | int | 200 | 状态码 |
| message | string | 获取资料夹成功 | 响应消息 |
| data.folderId | int | 1 | 资料夹 ID |
| data.picture | string | 图片路径 | 图片路径 |
| data.defaultId | string | user1_20250506195636_0849634e | 用户 ID |
| timestamp | long | 1747189328743 | 时间戳 |

##### 响应示例
```json
{
  "code": 200,
  "message": "获取资料夹成功",
  "data": {
    "folderId": 1,
    "picture": "D:\\Users\\HP\\Desktop\\aie\\image1\\img.png",
    "defaultId": "user1_20250506195636_0849634e"
  },
  "timestamp": 1747189328743
}
```

### 根据用户id获取资料夹
#### 根据用户 ID 获取资料夹
- **接口地址**: `GET /api/folder/user/{defaultId}`

##### 响应示例
```json
{
  "code": 200,
  "message": "获取资料夹成功",
  "data": {
    "folderId": 1,
    "picture": "D:\\Users\\HP\\Desktop\\aie\\image1\\img.png",
    "defaultId": "user1_20250506195636_0849634e"
  },
  "timestamp": 1747189402782
}
```