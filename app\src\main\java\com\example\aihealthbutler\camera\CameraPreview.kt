package com.example.aihealthbutler.camera

import android.content.Context
import android.media.Image
import android.util.Log
import android.util.Size
import android.view.Surface
import androidx.camera.core.*
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.camera.view.PreviewView
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.compose.ui.viewinterop.AndroidView
import androidx.core.content.ContextCompat
import androidx.lifecycle.LifecycleOwner
import com.google.mlkit.vision.barcode.BarcodeScanning
import com.google.mlkit.vision.barcode.BarcodeScannerOptions
import com.google.mlkit.vision.barcode.common.Barcode
import com.google.mlkit.vision.common.InputImage
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors
import java.util.concurrent.TimeUnit

/**
 * 相机预览组件，集成二维码扫描功能
 */
@Composable
fun CameraPreview(
    modifier: Modifier = Modifier,
    onQRCodeDetected: (String) -> Unit,
    flashEnabled: Boolean = false
) {
    val context = LocalContext.current
    val lifecycleOwner = LocalLifecycleOwner.current

    var camera by remember { mutableStateOf<Camera?>(null) }
    val cameraExecutor = remember { Executors.newSingleThreadExecutor() }

    // 控制闪光灯
    LaunchedEffect(flashEnabled, camera) {
        camera?.let { cam ->
            try {
                cam.cameraControl.enableTorch(flashEnabled)
                Log.d("CameraPreview", "闪光灯状态: $flashEnabled")
            } catch (e: Exception) {
                Log.e("CameraPreview", "闪光灯控制失败", e)
            }
        }
    }
    
    AndroidView(
        factory = { ctx ->
            val previewView = PreviewView(ctx)
            
            // 启动相机
            startCamera(
                context = ctx,
                lifecycleOwner = lifecycleOwner,
                previewView = previewView,
                onQRCodeDetected = onQRCodeDetected,
                cameraExecutor = cameraExecutor,
                onCameraReady = { cam -> camera = cam }
            )
            
            previewView
        },
        modifier = modifier.fillMaxSize()
    )
    

    
    // 清理资源
    DisposableEffect(Unit) {
        onDispose {
            cameraExecutor.shutdown()
        }
    }
}

/**
 * 启动相机并设置预览和分析
 */
private fun startCamera(
    context: Context,
    lifecycleOwner: LifecycleOwner,
    previewView: PreviewView,
    onQRCodeDetected: (String) -> Unit,
    cameraExecutor: ExecutorService,
    onCameraReady: (Camera) -> Unit
) {
    val cameraProviderFuture = ProcessCameraProvider.getInstance(context)
    
    cameraProviderFuture.addListener({
        try {
            val cameraProvider = cameraProviderFuture.get()
            
            // 预览用例
            val preview = Preview.Builder()
                .setTargetResolution(Size(1280, 720))
                .build()
                .also {
                    it.setSurfaceProvider(previewView.surfaceProvider)
                }

            // 图像分析用例（用于二维码扫描）
            val imageAnalyzer = ImageAnalysis.Builder()
                .setBackpressureStrategy(ImageAnalysis.STRATEGY_KEEP_ONLY_LATEST)
                .setTargetResolution(Size(1280, 720)) // 设置合适的分辨率
                .setImageQueueDepth(1) // 减少队列深度，提高实时性
                .setTargetRotation(Surface.ROTATION_0) // 尝试强制使用0度旋转
                .build()
                .also {
                    it.setAnalyzer(cameraExecutor, QRCodeAnalyzer(onQRCodeDetected))
                }
            
            // 选择后置摄像头
            val cameraSelector = CameraSelector.DEFAULT_BACK_CAMERA
            
            try {
                // 解绑之前的用例
                cameraProvider.unbindAll()
                
                // 绑定用例到相机
                val camera = cameraProvider.bindToLifecycle(
                    lifecycleOwner,
                    cameraSelector,
                    preview,
                    imageAnalyzer
                )

                Log.d("CameraPreview", "相机启动成功")

                onCameraReady(camera)
                
            } catch (exc: Exception) {
                Log.e("CameraPreview", "相机绑定失败", exc)
            }
            
        } catch (exc: Exception) {
            Log.e("CameraPreview", "相机启动失败", exc)
        }
    }, ContextCompat.getMainExecutor(context))
}

/**
 * 二维码分析器
 */
private class QRCodeAnalyzer(
    private val onQRCodeDetected: (String) -> Unit
) : ImageAnalysis.Analyzer {

    // 配置扫描器，专注于二维码格式以提高性能
    private val options = BarcodeScannerOptions.Builder()
        .setBarcodeFormats(
            Barcode.FORMAT_QR_CODE,
            Barcode.FORMAT_AZTEC,
            Barcode.FORMAT_DATA_MATRIX
        )
        .build()

    private val scanner = BarcodeScanning.getClient(options)
    private var lastAnalyzedTimestamp = 0L
    
    override fun analyze(imageProxy: ImageProxy) {
        val currentTimestamp = System.currentTimeMillis()
        if (currentTimestamp - lastAnalyzedTimestamp >= 500) {
            lastAnalyzedTimestamp = currentTimestamp

            val mediaImage = imageProxy.image
            if (mediaImage != null) {
                val rotationDegrees = imageProxy.imageInfo.rotationDegrees
                Log.d("QRCodeAnalyzer", "分析图像 - 旋转角度: $rotationDegrees°")

                // 尝试0度角度（修正旋转问题）
                val image = InputImage.fromMediaImage(mediaImage, 0)

                scanner.process(image)
                    .addOnSuccessListener { barcodes ->
                        Log.d("QRCodeAnalyzer", "检测到 ${barcodes.size} 个条码")

                        for (barcode in barcodes) {
                            val rawValue = barcode.rawValue
                            if (!rawValue.isNullOrBlank()) {
                                Log.d("QRCodeAnalyzer", "✅ 识别成功: $rawValue")
                                onQRCodeDetected(rawValue)
                                break
                            }
                        }
                        imageProxy.close()
                    }
                    .addOnFailureListener { exception ->
                        Log.e("QRCodeAnalyzer", "扫描失败", exception)
                        imageProxy.close()
                    }
            } else {
                imageProxy.close()
            }
        } else {
            imageProxy.close()
        }
    }

}
