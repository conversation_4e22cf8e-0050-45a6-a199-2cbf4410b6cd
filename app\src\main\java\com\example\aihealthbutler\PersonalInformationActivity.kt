package com.example.aihealthbutler

import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.aihealthbutler.ui.theme.AIHealthButlerTheme
import com.example.aihealthbutler.ui.components.GenderSelectionDialog
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.draw.clip
import androidx.compose.foundation.shape.CircleShape
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.viewModelScope
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.compose.foundation.border
import com.example.aihealthbutler.api.*
import kotlinx.coroutines.launch
import android.util.Log
import java.text.SimpleDateFormat
import java.util.*
import java.math.BigDecimal
import java.math.RoundingMode
import kotlin.fold
import android.widget.Toast
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.firstOrNull


class PersonalInformationActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            AIHealthButlerTheme {
                val sessionManager = remember { SessionManager(applicationContext) }
                val repository = remember { Repository() }
                val viewModel = remember { PersonalInformationViewModel(repository, sessionManager) }
                
                PersonalInformationScreen(
                    viewModel = viewModel,
                    onBack = {
                        // 返回主页面
                        startActivity(Intent(this, MainActivity::class.java))
                        finish()
                    }
                )
            }
        }
    }
}

class PersonalInformationViewModel(
    private val repository: Repository,
    private val sessionManager: SessionManager
) : ViewModel() {
    private val TAG = "PersonalInfoVM"
    
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading
    
    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error
    
    private val _personInfo = MutableStateFlow<PersonInfoData?>(null)
    val personInfo: StateFlow<PersonInfoData?> = _personInfo
    
    fun setError(message: String?) {
        _error.value = message
    }
    
    init {
        loadPersonInfo()
    }
    
    private fun loadPersonInfo() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                setError(null)
                
                // 获取当前默认ID和token
                val defaultId = sessionManager.defaultId.firstOrNull() ?: throw Exception("未设置默认成员")
                val token = sessionManager.token.firstOrNull() ?: throw Exception("未登录")
                
                // 获取个人信息
                val result = repository.getPersonInfo(token, defaultId)
                
                result.fold(
                    onSuccess = { response ->
                        if (response.code == 200 && response.data != null) {
                            _personInfo.value = response.data
                            setError(null)
                        } else {
                            setError(response.message ?: "获取个人信息失败")
                        }
                    },
                    onFailure = { e ->
                        setError("获取个人信息失败: ${e.message}")
                        Log.e(TAG, "获取个人信息失败", e)
                    }
                )
            } catch (e: Exception) {
                setError("获取个人信息异常: ${e.message}")
                Log.e(TAG, "获取个人信息异常", e)
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    fun updatePersonInfo(
        name: String,
        sex: Int?,
        birthday: String,
        height: Int?,
        weight: Float?,
        onSuccess: () -> Unit
    ) {
        if (name.isEmpty() || birthday.isEmpty()) {
            setError("请填写所有必填项")
            return
        }
        
        if (height == null || height <= 0) {
            setError("请输入有效的身高")
            return
        }
        
        if (weight == null || weight <= 0) {
            setError("请输入有效的体重")
            return
        }
        
        if (sex == null) {
            setError("请选择性别")
            return
        }
        
        viewModelScope.launch {
            try {
                _isLoading.value = true
                setError(null)
                
                // 获取当前默认ID和token
                val defaultId = sessionManager.defaultId.firstOrNull() ?: throw Exception("未设置默认成员")
                val token = sessionManager.token.firstOrNull() ?: throw Exception("未登录")
                
                        // 更新个人信息
                        val request = PersonInfoRequest(
                            name = name,
                            sex = sex,
                            birthday = birthday,
                            height = height,
                            weight = weight
                        )
                        
                val result = repository.updatePersonInfo(token, defaultId, request)
                        
                result.fold(
                    onSuccess = { response ->
                        if (response.code == 200 && response.data != null) {
                                _personInfo.value = response.data
                                Log.d(TAG, "更新个人信息成功")
                                onSuccess()
                            } else {
                            setError(response.message ?: "更新个人信息失败")
                        }
                    },
                    onFailure = { e ->
                        setError("更新个人信息失败: ${e.message}")
                        Log.e(TAG, "更新个人信息失败", e)
                    }
                )
            } catch (e: Exception) {
                setError("更新个人信息异常: ${e.message}")
                Log.e(TAG, "更新个人信息异常", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    // 加载个人信息
    fun loadPersonalInfo(defaultId: String) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                _error.value = null
                
                val token = sessionManager.token.firstOrNull() ?: throw Exception("未登录")
                
                val result = repository.getPersonInfo(token, defaultId)
                
                result.fold(
                    onSuccess = { response ->
                        if (response.code == 200 && response.data != null) {
                            _personInfo.value = response.data
                            _error.value = null
                        } else {
                            _error.value = response.message
                        }
                    },
                    onFailure = { e ->
                        _error.value = "获取个人信息失败: ${e.message}"
                        Log.e(TAG, "获取个人信息失败", e)
                    }
                )
            } catch (e: Exception) {
                Log.e(TAG, "获取个人信息异常", e)
                _error.value = e.message ?: "获取个人信息失败"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    // 更新个人信息
    fun updatePersonalInfo(
        defaultId: String,
        name: String,
        sex: Int,
        birthday: String,
        height: Int,
        weight: Float,
        role: String? = null
    ) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                _error.value = null
                
                val token = sessionManager.token.firstOrNull() ?: throw Exception("未登录")
                
                val request = PersonInfoRequest(
                    name = name,
                    sex = sex,
                    birthday = birthday,
                    height = height,
                    weight = weight,
                    role = role
                )
                
                val result = repository.updatePersonInfo(token, defaultId, request)
                
                result.fold(
                    onSuccess = { response ->
                        if (response.code == 200 && response.data != null) {
                            _personInfo.value = response.data
                            _error.value = null
                    } else {
                            _error.value = response.message
                    }
                    },
                    onFailure = { e ->
                        _error.value = "更新个人信息失败: ${e.message}"
                        Log.e(TAG, "更新个人信息失败", e)
                }
                )
            } catch (e: Exception) {
                Log.e(TAG, "更新个人信息异常", e)
                _error.value = e.message ?: "更新个人信息失败"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    // 创建个人信息
    fun createPersonalInfo(
        name: String,
        sex: Int,
        birthday: String,
        height: Int,
        weight: Float
    ) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                _error.value = null
                
                val token = sessionManager.token.firstOrNull() ?: throw Exception("未登录")
                
                val request = PersonInfoRequest(
                    name = name,
                    sex = sex,
                    birthday = birthday,
                    height = height,
                    weight = weight
                )
                
                val result = repository.createPersonInfo(token, request)
                
                result.fold(
                    onSuccess = { response ->
                        if (response.code == 200 && response.data != null) {
                            _personInfo.value = response.data
                            _error.value = null
                        } else {
                            _error.value = response.message
                        }
                    },
                    onFailure = { e ->
                        _error.value = "创建个人信息失败: ${e.message}"
                        Log.e(TAG, "创建个人信息失败", e)
                    }
                )
            } catch (e: Exception) {
                Log.e(TAG, "创建个人信息异常", e)
                _error.value = e.message ?: "创建个人信息失败"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    // 删除个人信息
    fun deletePersonalInfo(defaultId: String) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                _error.value = null
                
                val token = sessionManager.token.firstOrNull() ?: throw Exception("未登录")
                
                val result = repository.deletePersonInfo(token, defaultId)
                
                result.fold(
                    onSuccess = { response ->
                        if (response.code == 200) {
                            _personInfo.value = null
                            _error.value = null
                        } else {
                            _error.value = response.message
                        }
                    },
                    onFailure = { e ->
                        _error.value = "删除个人信息失败: ${e.message}"
                        Log.e(TAG, "删除个人信息失败", e)
                    }
                )
            } catch (e: Exception) {
                Log.e(TAG, "删除个人信息异常", e)
                _error.value = e.message ?: "删除个人信息失败"
            } finally {
                _isLoading.value = false
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PersonalInformationScreen(
    viewModel: PersonalInformationViewModel,
    onBack: () -> Unit
) {
    val context = LocalContext.current
    
    // 确保所有变量都有非null的初始值
    var nickname by remember { mutableStateOf("") }
    var gender by remember { mutableStateOf("") }
    var birthdate by remember { mutableStateOf("") }
    var height by remember { mutableStateOf("0") }
    var weight by remember { mutableStateOf("0") }
    var bmi by remember { mutableStateOf(0f) }

    // 添加性别选择对话框状态
    var showGenderDialog by remember { mutableStateOf(false) }

    // 添加身高体重错误信息状态
    var heightError by remember { mutableStateOf("") }
    var weightError by remember { mutableStateOf("") }

    // 添加日期选择器状态
    var showDatePicker by remember { mutableStateOf(false) }
    val calendar = remember { Calendar.getInstance() }
    val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
    
    // 当前选择的年月
    var currentYear by remember { mutableStateOf(calendar.get(Calendar.YEAR)) }
    var currentMonth by remember { mutableStateOf(calendar.get(Calendar.MONTH)) }
    
    // 获取当前月的天数
    val daysInMonth = remember(currentYear, currentMonth) {
        val cal = Calendar.getInstance()
        cal.set(currentYear, currentMonth, 1)
        cal.getActualMaximum(Calendar.DAY_OF_MONTH)
    }
    
    // 获取当前月第一天是星期几
    val firstDayOfMonth = remember(currentYear, currentMonth) {
        val cal = Calendar.getInstance()
        cal.set(currentYear, currentMonth, 1)
        cal.get(Calendar.DAY_OF_WEEK) - 1 // 调整为从0开始
    }

    // 显示加载状态对话框
    val isLoading by viewModel.isLoading.collectAsState()
    if (isLoading) {
        Dialog(
            onDismissRequest = { },
            properties = DialogProperties(dismissOnClickOutside = false)
        ) {
            Box(
                modifier = Modifier
                    .size(100.dp)
                    .background(Color.White, RoundedCornerShape(8.dp))
                    .border(1.dp, Color.LightGray.copy(alpha = 0.3f), RoundedCornerShape(8.dp)),
                contentAlignment = Alignment.Center
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    CircularProgressIndicator(
                        color = Color(0xFF2EB0AC),
                        strokeWidth = 3.dp,
                        modifier = Modifier.size(40.dp)
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "保存中...",
                        fontSize = 14.sp,
                        color = Color.Gray
                    )
                }
            }
        }
    }
    
    // 显示错误信息对话框
    val errorMsg by viewModel.error.collectAsState()
    if (!errorMsg.isNullOrEmpty()) {
        Dialog(
            onDismissRequest = { viewModel.setError(null) },
            properties = DialogProperties(dismissOnClickOutside = true)
        ) {
            Box(
                modifier = Modifier
                    .fillMaxWidth(0.9f)
                    .background(Color.White, RoundedCornerShape(12.dp))
                    .border(1.dp, Color.LightGray.copy(alpha = 0.3f), RoundedCornerShape(12.dp))
                    .padding(20.dp)
            ) {
                Column(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = "提示",
                        fontWeight = FontWeight.Bold,
                        fontSize = 18.sp,
                        color = Color.Black
                    )
                    
                    Spacer(modifier = Modifier.height(16.dp))

                    Text(
                        text = errorMsg ?: "",
                        fontSize = 16.sp,
                        color = Color.Gray,
                        textAlign = TextAlign.Center
                    )
                    
                    Spacer(modifier = Modifier.height(24.dp))
                    
                    Button(
                        onClick = { viewModel.setError(null) },
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFF2EB0AC)
                        ),
                        modifier = Modifier
                            .fillMaxWidth(0.6f)
                            .height(40.dp),
                        shape = RoundedCornerShape(20.dp)
                    ) {
                        Text("确定", color = Color.White)
                    }
                }
            }
        }
    }
    
    // 添加保存成功对话框
    var showSaveSuccess by remember { mutableStateOf(false) }
    
    if (showSaveSuccess) {
        Dialog(
            onDismissRequest = { 
                showSaveSuccess = false
                // 返回主页
                onBack()
            },
            properties = DialogProperties(dismissOnClickOutside = false)
        ) {
            Box(
                modifier = Modifier
                    .fillMaxWidth(0.9f)
                    .background(Color.White, RoundedCornerShape(12.dp))
                    .border(1.dp, Color.LightGray.copy(alpha = 0.3f), RoundedCornerShape(12.dp))
                    .padding(20.dp)
            ) {
                Column(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    // 使用可用的图标
                    Box(
                        modifier = Modifier
                            .size(60.dp)
                            .background(Color(0xFF2EB0AC).copy(alpha = 0.1f), CircleShape),
                        contentAlignment = Alignment.Center
                    ) {
                        Icon(
                            painter = painterResource(id = R.drawable.ic_add_circle_outline),  // 替换为一个可用的图标
                            contentDescription = "成功",
                            tint = Color(0xFF2EB0AC),
                            modifier = Modifier.size(35.dp)
                        )
                    }
                    
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    Text(
                        text = "保存成功",
                        fontWeight = FontWeight.Bold,
                        fontSize = 18.sp,
                        color = Color.Black
                    )
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    Text(
                        text = "您的个人信息已成功保存",
                        fontSize = 16.sp,
                        color = Color.Gray,
                        textAlign = TextAlign.Center
                    )
                    
                    Spacer(modifier = Modifier.height(24.dp))
                    
                    Button(
                        onClick = { 
                            showSaveSuccess = false
                            // 返回主页
                            onBack()
                        },
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFF2EB0AC)
                        ),
                        modifier = Modifier
                            .fillMaxWidth(0.6f)
                            .height(40.dp),
                        shape = RoundedCornerShape(20.dp)
                    ) {
                        Text("返回主页", color = Color.White)
                    }
                }
            }
        }
    }

    // 从ViewModel获取用户信息并更新UI
    val personInfoData by viewModel.personInfo.collectAsState()
    LaunchedEffect(personInfoData) {
        personInfoData?.let { data ->
            nickname = data.name ?: ""
            gender = when(data.sex) {
                1 -> "男"
                0 -> "女"
                else -> ""
            }
            birthdate = data.birthday ?: ""
            height = data.height?.toString() ?: "0"
            weight = data.weight?.toString() ?: "0"
            bmi = data.bmi ?: 0f
        }
    }
    
    // 监听身高和体重变化，自动计算BMI（仅用于显示）
    LaunchedEffect(height, weight) {
        if (height.isNotEmpty() && weight.isNotEmpty()) {
            try {
                val heightValue = height.toInt()
                val weightValue = weight.toFloat()
                if (heightValue > 0 && weightValue > 0) {
                    bmi = calculateBMI(heightValue, weightValue)
                }
            } catch (e: Exception) {
                // 忽略解析错误
            }
        }
    }

    val mainColor = Color(0xFF2EB0AC)
    val backgroundColor = Color(0xFFFFFFFF)

    Scaffold(
        containerColor = backgroundColor,
        topBar = {
            Column {
                CenterAlignedTopAppBar(
                    title = {
                        Text(
                            "个人信息",
                            fontSize = 20.sp,
                            fontWeight = FontWeight.Bold,
                        )
                    },
                    navigationIcon = {
                        IconButton(
                            onClick = {
                                onBack()
                            }) {
                            Icon(
                                painter = painterResource(id = R.drawable.left_arrow),
                                contentDescription = "返回",
                                modifier = Modifier.size(20.dp)
                            )
                        }
                    },
                    colors = TopAppBarDefaults.centerAlignedTopAppBarColors(
                        containerColor = Color.White
                    )
                )
                Divider(
                    color = Color.LightGray,
                    thickness = 3.dp,
                    modifier = Modifier
                        .fillMaxWidth(0.9f)
                        .align(Alignment.CenterHorizontally)
                )
            }
        }
    ) { innerPadding ->
        Column(
            modifier = Modifier
                .padding(innerPadding)
                .padding(horizontal = 24.dp)
                .fillMaxSize()
                .background(backgroundColor)
        ) {
            // 昵称
            Text(
                text = "昵称",
                color = Color.Gray,
                fontSize = 15.sp,
                modifier = Modifier.padding(top = 16.dp, bottom = 8.dp)
            )
            OutlinedTextField(
                value = nickname ?: "",
                onValueChange = { nickname = it },
                modifier = Modifier
                    .fillMaxWidth()
                    .height(52.dp),
                shape = RoundedCornerShape(8.dp),
                colors = OutlinedTextFieldDefaults.colors(
                    unfocusedBorderColor = Color.LightGray,
                    focusedBorderColor = mainColor,
                    unfocusedContainerColor = Color.White,
                    focusedContainerColor = Color.White
                ),
                singleLine = true
            )

            // 性别
            Text(
                text = "性别",
                color = Color.Gray,
                fontSize = 15.sp,
                modifier = Modifier.padding(top = 16.dp, bottom = 8.dp)
            )
            OutlinedTextField(
                value = gender ?: "",
                onValueChange = { },
                modifier = Modifier
                    .fillMaxWidth()
                    .height(52.dp),
                shape = RoundedCornerShape(8.dp),
                colors = OutlinedTextFieldDefaults.colors(
                    unfocusedBorderColor = Color.LightGray,
                    focusedBorderColor = mainColor,
                    unfocusedContainerColor = Color.White,
                    focusedContainerColor = Color.White
                ),
                trailingIcon = {
                    Icon(
                        painter = painterResource(id = R.drawable.ic_dropdown),
                        contentDescription = "选择性别",
                        tint = Color.Gray,
                        modifier = Modifier
                            .size(18.dp)
                            .clickable { showGenderDialog = true }
                    )
                },
                readOnly = true,
                singleLine = true
            )

            // 出生日期
            Text(
                text = "出生日期",
                color = Color.Gray,
                fontSize = 15.sp,
                modifier = Modifier.padding(top = 16.dp, bottom = 8.dp)
            )
            Box(modifier = Modifier.fillMaxWidth()) {
                OutlinedTextField(
                    value = birthdate ?: "",
                    onValueChange = { },
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(52.dp),
                    shape = RoundedCornerShape(8.dp),
                    colors = OutlinedTextFieldDefaults.colors(
                        unfocusedBorderColor = Color.LightGray,
                        focusedBorderColor = mainColor,
                        unfocusedContainerColor = Color.White,
                        focusedContainerColor = Color.White
                    ),
                    trailingIcon = {
                        Icon(
                            painter = painterResource(id = R.drawable.ic_calendar_today),
                            contentDescription = "选择日期",
                            tint = Color.Gray,
                            modifier = Modifier.size(22.dp)
                        )
                    },
                    readOnly = true,
                    singleLine = true
                )
                
                // 添加透明可点击层
                Box(
                    modifier = Modifier
                        .matchParentSize()
                        .clickable(
                            indication = null,
                            interactionSource = remember { MutableInteractionSource() }
                        ) { 
                            showDatePicker = true
                        }
                )
            }

            // 身高
            Text(
                text = "身高",
                color = Color.Gray,
                fontSize = 15.sp,
                modifier = Modifier.padding(top = 16.dp, bottom = 8.dp)
            )
            OutlinedTextField(
                value = height ?: "0",
                onValueChange = {
                    // 只允许输入数字
                    if (it.isEmpty() || it.all { char -> char.isDigit() }) {
                        height = it
                        heightError = if (it.isEmpty() || it.toIntOrNull() ?: 0 <= 0) {
                            "身高必须大于0"
                        } else {
                            ""
                        }
                    }
                },
                modifier = Modifier
                    .fillMaxWidth()
                    .height(52.dp),
                shape = RoundedCornerShape(8.dp),
                colors = OutlinedTextFieldDefaults.colors(
                    unfocusedBorderColor = Color.LightGray,
                    focusedBorderColor = mainColor,
                    unfocusedContainerColor = Color.White,
                    focusedContainerColor = Color.White
                ),
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                trailingIcon = {
                    Text(
                        text = "cm",
                        color = Color.Gray,
                        fontSize = 15.sp,
                        modifier = Modifier.padding(end = 16.dp)
                    )
                },
                singleLine = true,
                isError = heightError.isNotEmpty()
            )
            if (heightError.isNotEmpty()) {
                Text(
                    text = heightError,
                    color = MaterialTheme.colorScheme.error,
                    fontSize = 12.sp,
                    modifier = Modifier.padding(start = 8.dp)
                )
            }

            // 体重
            Text(
                text = "体重",
                color = Color.Gray,
                fontSize = 15.sp,
                modifier = Modifier.padding(top = 16.dp, bottom = 8.dp)
            )
            OutlinedTextField(
                value = weight ?: "0",
                onValueChange = {
                    // 允许输入数字和小数点
                    if (it.isEmpty() || it.matches(Regex("^\\d*\\.?\\d*$"))) {
                        weight = it
                        weightError = if (it.isEmpty() || it.toFloatOrNull() ?: 0f <= 0f) {
                            "体重必须大于0"
                        } else {
                            ""
                        }
                    }
                },
                modifier = Modifier
                    .fillMaxWidth()
                    .height(52.dp),
                shape = RoundedCornerShape(8.dp),
                colors = OutlinedTextFieldDefaults.colors(
                    unfocusedBorderColor = Color.LightGray,
                    focusedBorderColor = mainColor,
                    unfocusedContainerColor = Color.White,
                    focusedContainerColor = Color.White
                ),
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                trailingIcon = {
                    Text(
                        text = "kg",
                        color = Color.Gray,
                        fontSize = 15.sp,
                        modifier = Modifier.padding(end = 16.dp)
                    )
                },
                singleLine = true,
                isError = weightError.isNotEmpty()
            )
            if (weightError.isNotEmpty()) {
                Text(
                    text = weightError,
                    color = MaterialTheme.colorScheme.error,
                    fontSize = 12.sp,
                    modifier = Modifier.padding(start = 8.dp)
                )
            }

            Spacer(modifier = Modifier.weight(1f))

            // 保存按钮
            Button(
                onClick = {
                    // 验证输入
                    if (nickname.isEmpty()) {
                        viewModel.setError("请输入昵称")
                        return@Button
                    }
                    
                    if (gender.isEmpty()) {
                        viewModel.setError("请选择性别")
                        return@Button
                    }
                    
                    if (birthdate.isEmpty()) {
                        viewModel.setError("请选择出生日期")
                        return@Button
                    }
                    
                    if (height.isEmpty() || height.toIntOrNull() ?: 0 <= 0) {
                        heightError = "身高必须大于0"
                        viewModel.setError("请输入有效的身高")
                        return@Button
                    }

                    if (weight.isEmpty() || weight.toFloatOrNull() ?: 0f <= 0f) {
                        weightError = "体重必须大于0"
                        viewModel.setError("请输入有效的体重")
                        return@Button
                    }
                    
                    try {
                        // 转换性别格式（男=1，女=0）
                        val sexValue = when(gender) {
                            "男" -> 1
                            "女" -> 0
                            else -> null
                        }
                        
                        // 使用viewModel来处理获取defaultId的操作
                        viewModel.updatePersonInfo(
                            name = nickname.trim(),
                            sex = sexValue ?: 1,
                            birthday = birthdate.trim(),
                            height = height.toIntOrNull() ?: 0,
                            weight = weight.toFloatOrNull() ?: 0f
                        ) {
                            // 成功回调
                                showSaveSuccess = true
                            }
                    } catch (e: Exception) {
                        viewModel.setError("保存失败：${e.message}")
                        Log.e("PersonalInfoScreen", "保存异常", e)
                    }
                },
                modifier = Modifier
                    .fillMaxWidth()
                    .height(50.dp)
                    .padding(bottom = 8.dp),
                shape = RoundedCornerShape(8.dp),
                colors = ButtonDefaults.buttonColors(containerColor = mainColor)
            ) {
                Text(
                    text = "保存",
                    fontSize = 16.sp,
                    color = Color.White
                )
            }

            Spacer(modifier = Modifier.height(24.dp))
        }
    }

    // 性别选择对话框
    if (showGenderDialog) {
        GenderSelectionDialog(
            onDismiss = { showGenderDialog = false },
            onGenderSelected = { selectedGender ->
                gender = selectedGender
                showGenderDialog = false
            }
        )
    }

    // 日历选择器对话框
    if (showDatePicker) {
        Dialog(
            onDismissRequest = { showDatePicker = false },
            properties = DialogProperties(dismissOnClickOutside = true)
        ) {
            Card(
                modifier = Modifier
                    .fillMaxWidth(0.95f)
                    .padding(horizontal = 0.dp, vertical = 8.dp),
                shape = RoundedCornerShape(16.dp),
                colors = CardDefaults.cardColors(containerColor = Color.White)
            ) {
                Column(
                    modifier = Modifier
                        .padding(horizontal = 12.dp, vertical = 12.dp)
                ) {
                    // 月份和年份导航 - 等间距排布
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 8.dp),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.SpaceBetween // 改为等间距
                    ) {
                        // 年份减按钮
                        Text(
                            text = "<<",
                            fontSize = 16.sp,
                            color = Color(0xFF505050),
                            modifier = Modifier
                                .clickable { currentYear-- }
                                .padding(horizontal = 8.dp, vertical = 4.dp)
                        )
                        
                        // 月份减按钮
                        Text(
                            text = "<",
                            fontSize = 16.sp,
                            color = Color(0xFF505050),
                            modifier = Modifier
                                .clickable {
                                    if (currentMonth == 0) {
                                        currentMonth = 11
                                        currentYear--
                                    } else {
                                        currentMonth--
                                    }
                                }
                                .padding(horizontal = 8.dp, vertical = 4.dp)
                        )
                        
                        // 年月显示
                        Text(
                            text = "${currentYear}年${currentMonth + 1}月",
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Medium,
                            color = Color(0xFF303030)
                        )
                        
                        // 月份加按钮
                        Text(
                            text = ">",
                            fontSize = 16.sp,
                            color = Color(0xFF505050),
                            modifier = Modifier
                                .clickable {
                                    if (currentMonth == 11) {
                                        currentMonth = 0
                                        currentYear++
                                    } else {
                                        currentMonth++
                                    }
                                }
                                .padding(horizontal = 8.dp, vertical = 4.dp)
                        )
                        
                        // 年份加按钮
                        Text(
                            text = ">>",
                            fontSize = 16.sp,
                            color = Color(0xFF505050),
                            modifier = Modifier
                                .clickable { currentYear++ }
                                .padding(horizontal = 8.dp, vertical = 4.dp)
                        )
                    }
                    
                    Spacer(modifier = Modifier.height(12.dp))
                    
                    // 星期标题
                    Row(modifier = Modifier.fillMaxWidth()) {
                        listOf("日", "一", "二", "三", "四", "五", "六").forEach { day ->
                            Box(
                                modifier = Modifier.weight(1f),
                                contentAlignment = Alignment.Center
                            ) {
                                Text(
                                    text = day,
                                    fontSize = 14.sp,
                                    color = Color(0xFF505050)
                                )
                            }
                        }
                    }
                    
                    // 添加分隔线
                    Divider(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 6.dp),
                        color = Color(0xFFE0E0E0),
                        thickness = 1.dp
                    )
                    
                    // 日期网格
                    val rows = (daysInMonth + firstDayOfMonth + 6) / 7
                    for (row in 0 until rows) {
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(vertical = 0.dp)
                        ) {
                            for (col in 0 until 7) {
                                val day = row * 7 + col - firstDayOfMonth + 1
                                Box(
                                    modifier = Modifier
                                        .weight(1f)
                                        .padding(2.dp)
                                        .height(32.dp),
                                    contentAlignment = Alignment.Center
                                ) {
                                    if (day in 1..daysInMonth) {
                                        // 解析当前选中的日期
                                        val selectedCalendar = Calendar.getInstance().apply {
                                            time = if (birthdate.isEmpty()) Date() else dateFormat.parse(birthdate) ?: Date()
                                        }
                                        val selectedDay = selectedCalendar.get(Calendar.DAY_OF_MONTH)
                                        val selectedMonth = selectedCalendar.get(Calendar.MONTH)
                                        val selectedYear = selectedCalendar.get(Calendar.YEAR)
                                        
                                        // 判断是否是选中的日期
                                        val isSelected = day == selectedDay && 
                                                        currentMonth == selectedMonth && 
                                                        currentYear == selectedYear
                                        
                                        Box(
                                            modifier = Modifier
                                                .size(30.dp)
                                                .aspectRatio(1f)
                                                .clip(CircleShape)
                                                .background(
                                                    if (isSelected) mainColor else Color.Transparent
                                                )
                                                .clickable {
                                                    calendar.set(currentYear, currentMonth, day)
                                                    birthdate = dateFormat.format(calendar.time)
                                                    showDatePicker = false
                                                },
                                            contentAlignment = Alignment.Center
                                        ) {
                                            Text(
                                                text = day.toString(),
                                                color = if (isSelected) Color.White else Color(0xFF303030),
                                                fontSize = 14.sp,
                                                textAlign = TextAlign.Center
                                            )
                                        }
                                    }
                                }
                            }
                        }
                    }
                    
                    Spacer(modifier = Modifier.height(2.dp))
                }
            }
        }
    }
}

// 计算BMI
fun calculateBMI(height: Int, weight: Float): Float {
    if (height <= 0 || weight <= 0) return 0f
    
    val heightInMeters = height / 100.0f
    val bmi = weight / (heightInMeters * heightInMeters)
    
    // 四舍五入到小数点后一位
    return BigDecimal(bmi.toDouble())
        .setScale(1, RoundingMode.HALF_UP)
        .toFloat()
}