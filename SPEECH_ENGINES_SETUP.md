# 语音识别引擎集成指南

本项目支持多种语音识别引擎，为用户提供Google语音服务的替代方案。

## 🎯 支持的引擎

### 1. Google语音识别 (默认)
- **优点**: 识别准确率高，支持多语言
- **缺点**: 需要Google服务，部分设备不可用
- **状态**: ✅ 已集成

### 2. 讯飞语音识别 (推荐替代方案)
- **优点**: 中文识别准确率高，支持离线+在线
- **缺点**: 需要申请APPID
- **状态**: 🔧 需要配置

### 3. Vosk离线引擎
- **优点**: 完全离线，保护隐私，开源免费
- **缺点**: 需要下载模型文件，准确率中等
- **状态**: 🔧 需要配置

### 4. 百度语音识别
- **优点**: 支持多种方言，API稳定
- **缺点**: 需要申请API Key
- **状态**: 📋 待实现

## 🛠️ 集成步骤

### 讯飞语音识别集成

#### 1. 注册讯飞开放平台
1. 访问 [讯飞开放平台](https://www.xfyun.cn/)
2. 注册账号并实名认证
3. 创建应用，选择"语音听写"服务
4. 获取APPID

#### 2. 下载SDK
1. 在讯飞控制台下载Android SDK
2. 将`Msc.jar`文件放入`app/libs/`目录
3. 将so库文件放入`app/src/main/jniLibs/`对应架构目录

#### 3. 配置代码
```kotlin
// 在 XunfeiSpeechEngine.kt 中替换APPID
private const val APPID = "your_actual_appid_here"
```

#### 4. 添加权限
```xml
<!-- AndroidManifest.xml -->
<uses-permission android:name="android.permission.RECORD_AUDIO" />
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
<uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
<uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
<uses-permission android:name="android.permission.READ_PHONE_STATE" />
<uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
```

### Vosk离线引擎集成

#### 1. 下载模型文件
1. 访问 [Vosk Models](https://alphacephei.com/vosk/models)
2. 下载中文小模型: `vosk-model-small-cn-0.22`
3. 解压到`app/src/main/assets/`目录

#### 2. 添加依赖
```kotlin
// app/build.gradle.kts (已添加)
implementation("com.alphacephei:vosk-android:0.3.45")
```

#### 3. 初始化模型
应用首次启动时会自动从assets复制模型到内部存储。

### 百度语音识别集成

#### 1. 申请百度AI开放平台
1. 访问 [百度AI开放平台](https://ai.baidu.com/)
2. 创建应用，获取API Key和Secret Key

#### 2. 实现BaiduSpeechEngine
```kotlin
// 需要实现 BaiduSpeechEngine 类
class BaiduSpeechEngine(
    private val context: Context,
    private val apiKey: String,
    private val secretKey: String
) : SpeechRecognitionEngine {
    // 实现接口方法
}
```

## 🔧 使用方法

### 启用多引擎模式
```kotlin
// 在初始化时启用多引擎模式
speechToTextManager.setMultiEngineMode(true)
```

### 手动切换引擎
```kotlin
// 切换到讯飞引擎
speechToTextManager.switchEngine(SpeechEngineType.XUNFEI)

// 切换到Vosk引擎
speechToTextManager.switchEngine(SpeechEngineType.VOSK)
```

### 检查引擎状态
```kotlin
// 获取可用引擎
val availableEngines = speechToTextManager.getAvailableEngines()

// 获取当前引擎
val currentEngine = speechToTextManager.getCurrentEngineType()

// 获取诊断信息
val diagnosticInfo = speechToTextManager.getEnginesDiagnosticInfo()
```

## 📱 用户界面

### 引擎选择对话框
- 在语音识别错误时，用户可以点击"切换引擎"按钮
- 显示所有可用引擎的状态和描述
- 支持一键切换引擎

### 错误处理
- 自动检测引擎可用性
- 提供详细的错误信息和解决方案
- 支持引擎降级（Google → 讯飞 → Vosk）

## 🔍 故障排除

### 常见问题

#### 1. 讯飞引擎初始化失败
- 检查APPID是否正确配置
- 确认网络连接正常
- 检查权限是否已授予

#### 2. Vosk引擎不可用
- 确认模型文件已正确放置
- 检查存储空间是否充足
- 确认模型文件完整性

#### 3. 所有引擎都不可用
- 检查录音权限
- 确认设备支持语音识别
- 重启应用重新初始化

### 调试信息
```kotlin
// 获取详细诊断信息
val diagnosticInfo = speechToTextManager.getEnginesDiagnosticInfo()
Log.d("SpeechEngines", diagnosticInfo)
```

## 📊 性能对比

| 引擎 | 准确率 | 速度 | 离线支持 | 隐私性 | 成本 |
|------|--------|------|----------|--------|------|
| Google | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ❌ | ⭐⭐ | 免费 |
| 讯飞 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐ | 有限免费 |
| Vosk | ⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | 完全免费 |
| 百度 | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ❌ | ⭐⭐⭐ | 有限免费 |

## 🚀 未来计划

- [ ] 集成华为HMS语音服务
- [ ] 支持自定义语音模型
- [ ] 添加语音识别质量评估
- [ ] 实现混合识别模式（多引擎投票）
- [ ] 支持实时语音翻译

## 📞 技术支持

如果在集成过程中遇到问题，请：
1. 查看应用日志获取详细错误信息
2. 使用引擎诊断功能检查状态
3. 参考各厂商官方文档
4. 提交Issue描述具体问题
