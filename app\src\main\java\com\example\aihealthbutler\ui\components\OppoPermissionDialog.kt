package com.example.aihealthbutler.ui.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material.icons.filled.Warning
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import com.example.aihealthbutler.utils.OppoPermissionHelper

/**
 * OPPO设备权限请求对话框
 */
@Composable
fun OppoPermissionDialog(
    isVisible: Boolean,
    onDismiss: () -> Unit,
    onOpenSettings: () -> Unit
) {
    val context = LocalContext.current
    
    if (isVisible) {
        Dialog(onDismissRequest = onDismiss) {
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                shape = RoundedCornerShape(16.dp),
                colors = CardDefaults.cardColors(
                    containerColor = Color.White
                ),
                elevation = CardDefaults.cardElevation(
                    defaultElevation = 8.dp
                )
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(24.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    // 警告图标
                    Icon(
                        imageVector = Icons.Default.Warning,
                        contentDescription = "权限警告",
                        tint = Color(0xFFFF9800),
                        modifier = Modifier.size(48.dp)
                    )
                    
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    // 标题
                    Text(
                        text = "需要录音权限",
                        fontSize = 20.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color(0xFF333333),
                        textAlign = TextAlign.Center
                    )
                    
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    // 权限说明
                    Text(
                        text = OppoPermissionHelper.getPermissionGuideMessage(),
                        fontSize = 14.sp,
                        color = Color(0xFF666666),
                        textAlign = TextAlign.Start,
                        lineHeight = 20.sp
                    )
                    
                    Spacer(modifier = Modifier.height(24.dp))
                    
                    // 按钮区域
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(12.dp)
                    ) {
                        // 取消按钮
                        OutlinedButton(
                            onClick = onDismiss,
                            modifier = Modifier.weight(1f),
                            colors = ButtonDefaults.outlinedButtonColors(
                                contentColor = Color(0xFF666666)
                            ),
                            shape = RoundedCornerShape(8.dp)
                        ) {
                            Text("稍后设置")
                        }
                        
                        // 去设置按钮
                        Button(
                            onClick = {
                                OppoPermissionHelper.openOppoPermissionSettings(context)
                                onOpenSettings()
                            },
                            modifier = Modifier.weight(1f),
                            colors = ButtonDefaults.buttonColors(
                                containerColor = Color(0xFF74B5AF)
                            ),
                            shape = RoundedCornerShape(8.dp)
                        ) {
                            Icon(
                                imageVector = Icons.Default.Settings,
                                contentDescription = "设置",
                                modifier = Modifier.size(16.dp)
                            )
                            Spacer(modifier = Modifier.width(4.dp))
                            Text("去设置", color = Color.White)
                        }
                    }
                }
            }
        }
    }
}

/**
 * 权限诊断信息对话框
 */
@Composable
fun PermissionDiagnosticDialog(
    isVisible: Boolean,
    onDismiss: () -> Unit
) {
    val context = LocalContext.current
    
    if (isVisible) {
        Dialog(onDismissRequest = onDismiss) {
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                shape = RoundedCornerShape(16.dp),
                colors = CardDefaults.cardColors(
                    containerColor = Color.White
                ),
                elevation = CardDefaults.cardElevation(
                    defaultElevation = 8.dp
                )
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(24.dp)
                ) {
                    // 标题
                    Text(
                        text = "权限诊断信息",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color(0xFF333333)
                    )
                    
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    // 诊断信息
                    Text(
                        text = OppoPermissionHelper.getOppoPermissionDiagnostic(context),
                        fontSize = 12.sp,
                        color = Color(0xFF666666),
                        lineHeight = 16.sp
                    )
                    
                    Spacer(modifier = Modifier.height(24.dp))
                    
                    // 关闭按钮
                    Button(
                        onClick = onDismiss,
                        modifier = Modifier.fillMaxWidth(),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFF74B5AF)
                        ),
                        shape = RoundedCornerShape(8.dp)
                    ) {
                        Text("关闭", color = Color.White)
                    }
                }
            }
        }
    }
}

/**
 * 权限状态指示器
 */
@Composable
fun PermissionStatusIndicator(
    hasPermission: Boolean,
    isOppoDevice: Boolean = false,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        // 状态图标
        Icon(
            imageVector = if (hasPermission) Icons.Default.Settings else Icons.Default.Warning,
            contentDescription = if (hasPermission) "权限已授予" else "权限未授予",
            tint = if (hasPermission) Color(0xFF4CAF50) else Color(0xFFFF9800),
            modifier = Modifier.size(16.dp)
        )
        
        // 状态文本
        Text(
            text = when {
                hasPermission -> "录音权限已授予"
                isOppoDevice -> "OPPO设备需要手动设置"
                else -> "录音权限未授予"
            },
            fontSize = 12.sp,
            color = if (hasPermission) Color(0xFF4CAF50) else Color(0xFFFF9800)
        )
    }
}
