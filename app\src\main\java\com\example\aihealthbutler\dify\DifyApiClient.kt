package com.example.aihealthbutler.dify

import android.util.Log
import com.example.aihealthbutler.utils.ConnectionResetHandler
import com.example.aihealthbutler.utils.HttpsConfigHelper
import com.google.gson.Gson
import com.google.gson.JsonSyntaxException
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.withContext
import okhttp3.*
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.RequestBody.Companion.asRequestBody
import okhttp3.RequestBody.Companion.toRequestBody
import java.io.BufferedReader
import java.io.File
import java.io.IOException
import java.security.cert.X509Certificate
import java.util.concurrent.TimeUnit
import javax.net.ssl.SSLContext
import javax.net.ssl.TrustManager
import javax.net.ssl.X509TrustManager

/**
 * Dify API 客户端
 */
class DifyApiClient(
    private val apiKey: String,
    private val baseUrl: String = "https://qcx.yuneyang.top/v1"
) {
    private val client = HttpsConfigHelper.configureHttpsClient(
        OkHttpClient.Builder()
            .connectTimeout(30, TimeUnit.SECONDS)
            .readTimeout(120, TimeUnit.SECONDS)  // 增加读取超时时间
            .writeTimeout(60, TimeUnit.SECONDS)
            .retryOnConnectionFailure(true)
            .connectionPool(ConnectionPool(5, 5, TimeUnit.MINUTES))  // 连接池配置
    )
        .addInterceptor { chain ->
            val request = chain.request()
            Log.d(TAG, "Making HTTPS request to: ${request.url}")
            Log.d(TAG, "Request headers: ${request.headers}")

            try {
                val response = chain.proceed(request)
                Log.d(TAG, "HTTPS response code: ${response.code}")
                Log.d(TAG, "HTTPS response headers: ${response.headers}")
                response
            } catch (e: Exception) {
                Log.e(TAG, "HTTPS request failed in interceptor", e)
                throw e
            }
        }
        .addNetworkInterceptor { chain ->
            // 网络层拦截器，添加HTTPS相关头部
            val request = chain.request().newBuilder()
                .addHeader("Connection", "keep-alive")
                .addHeader("Keep-Alive", "timeout=30, max=100")
                .addHeader("User-Agent", "AIHealthButler-Android-HTTPS")
                .build()

            // 直接执行请求，异常处理由应用层负责
            chain.proceed(request)
        }
        .build()
    
    private val gson = Gson()
    
    companion object {
        private const val TAG = "DifyApiClient"
        private const val CONTENT_TYPE_JSON = "application/json"
        private const val CONTENT_TYPE_MULTIPART = "multipart/form-data"
    }
    
    /**
     * 发送聊天消息（流式模式）
     */
    fun sendChatMessageStream(
        query: String,
        user: String,
        conversationId: String? = null,
        files: List<FileInfo>? = null,
        inputs: Map<String, Any> = emptyMap(),
        retryCount: Int = 3
    ): Flow<StreamChatResponse> = flow {
        val request = ChatMessageRequest(
            query = query,
            user = user,
            conversationId = conversationId,
            files = files,
            inputs = inputs,
            responseMode = ResponseMode.STREAMING
        )
        
        val requestBody = gson.toJson(request).toRequestBody(CONTENT_TYPE_JSON.toMediaType())
        
        val httpRequest = Request.Builder()
            .url("$baseUrl/chat-messages")
            .post(requestBody)
            .addHeader("Authorization", "Bearer $apiKey")
            .addHeader("Content-Type", CONTENT_TYPE_JSON)
            .addHeader("Accept", "text/event-stream")
            .addHeader("Cache-Control", "no-cache")
            .build()
        
        try {
            Log.d(TAG, "Sending stream request to: $baseUrl/chat-messages")
            Log.d(TAG, "Request body: ${gson.toJson(request)}")

            // 使用连接重置处理工具执行流式请求
            val response = ConnectionResetHandler.streamRetry("Stream Chat Request") {
                executeStreamRequest(httpRequest)
            }

            Log.d(TAG, "Stream response code: ${response.code}")
            Log.d(TAG, "Stream response headers: ${response.headers}")

            if (!response.isSuccessful) {
                val errorBody = response.body?.string()
                Log.e(TAG, "Stream API request failed: ${response.code} - $errorBody")
                throw IOException("Stream API request failed: ${response.code} - $errorBody")
            }

            // 直接处理流式响应，避免协程上下文问题
            response.body?.let { responseBody ->
                try {
                    val reader = responseBody.charStream().buffered()
                    var lineCount = 0

                    reader.useLines { lines ->
                        for (line in lines) {
                            lineCount++

                            try {
                                if (line.startsWith("data: ")) {
                                    val jsonData = line.substring(6).trim()
                                    if (jsonData.isNotEmpty() && jsonData != "[DONE]") {
                                        val streamResponse = gson.fromJson(jsonData, StreamChatResponse::class.java)
                                        emit(streamResponse)
                                    }
                                }
                            } catch (e: JsonSyntaxException) {
                                Log.w(TAG, "Failed to parse stream line $lineCount: $line", e)
                                // 继续处理下一行，不中断整个流
                            } catch (e: Exception) {
                                Log.e(TAG, "Error processing stream line $lineCount: $line", e)
                                // 对于其他异常，也继续处理
                            }
                        }
                    }

                    Log.d(TAG, "Stream processing completed. Total lines: $lineCount")

                } catch (e: java.io.IOException) {
                    Log.e(TAG, "Stream reading interrupted", e)
                    throw e
                }
            } ?: throw IOException("Empty response body in stream")
        } catch (e: Exception) {
            Log.e(TAG, "Stream chat failed", e)

            // 记录详细的诊断信息
            val diagnosticInfo = ConnectionResetHandler.getDiagnosticInfo(e)
            Log.d(TAG, "Stream chat diagnostic:\n$diagnosticInfo")

            // 重新抛出异常，让ConnectionResetHandler处理重试
            throw e
        }
    }.flowOn(Dispatchers.IO)

    /**
     * 执行流式请求，带有连接重置处理
     */
    private fun executeStreamRequest(request: Request): Response {
        var lastException: Exception? = null

        // 尝试多次执行请求
        repeat(3) { attempt ->
            try {
                Log.d(TAG, "Executing stream request, attempt: ${attempt + 1}")
                return client.newCall(request).execute()
            } catch (e: java.net.SocketException) {
                Log.w(TAG, "Socket exception on attempt ${attempt + 1}: ${e.message}")
                lastException = e

                // 短暂延迟后重试
                if (attempt < 2) {
                    Thread.sleep(500L * (attempt + 1))
                }
            } catch (e: java.io.IOException) {
                Log.w(TAG, "IO exception on attempt ${attempt + 1}: ${e.message}")
                lastException = e

                if (attempt < 2) {
                    Thread.sleep(500L * (attempt + 1))
                }
            }
        }

        // 所有尝试都失败，抛出最后一个异常
        throw lastException ?: IOException("Failed to execute stream request after 3 attempts")
    }


    
    /**
     * 发送聊天消息（阻塞模式）
     */
    suspend fun sendChatMessage(
        query: String,
        user: String,
        conversationId: String? = null,
        files: List<FileInfo>? = null,
        inputs: Map<String, Any> = emptyMap()
    ): ChatCompletionResponse = withContext(Dispatchers.IO) {
        val request = ChatMessageRequest(
            query = query,
            user = user,
            conversationId = conversationId,
            files = files,
            inputs = inputs,
            responseMode = ResponseMode.BLOCKING
        )
        
        val requestBody = gson.toJson(request).toRequestBody(CONTENT_TYPE_JSON.toMediaType())
        
        val httpRequest = Request.Builder()
            .url("$baseUrl/chat-messages")
            .post(requestBody)
            .addHeader("Authorization", "Bearer $apiKey")
            .addHeader("Content-Type", CONTENT_TYPE_JSON)
            .build()
        
        val response = client.newCall(httpRequest).execute()
        
        if (!response.isSuccessful) {
            val errorBody = response.body?.string()
            Log.e(TAG, "API request failed: ${response.code} - $errorBody")
            throw IOException("API request failed: ${response.code}")
        }
        
        val responseBody = response.body?.string()
            ?: throw IOException("Empty response body")
        
        gson.fromJson(responseBody, ChatCompletionResponse::class.java)
    }
    
    /**
     * 上传文件
     */
    suspend fun uploadFile(
        file: File,
        user: String
    ): FileUploadResponse = withContext(Dispatchers.IO) {
        val requestBody = MultipartBody.Builder()
            .setType(MultipartBody.FORM)
            .addFormDataPart("user", user)
            .addFormDataPart(
                "file",
                file.name,
                file.asRequestBody("application/octet-stream".toMediaType())
            )
            .build()
        
        val request = Request.Builder()
            .url("$baseUrl/files/upload")
            .post(requestBody)
            .addHeader("Authorization", "Bearer $apiKey")
            .build()
        
        val response = client.newCall(request).execute()
        
        if (!response.isSuccessful) {
            val errorBody = response.body?.string()
            Log.e(TAG, "File upload failed: ${response.code} - $errorBody")
            throw IOException("File upload failed: ${response.code}")
        }
        
        val responseBody = response.body?.string()
            ?: throw IOException("Empty response body")
        
        gson.fromJson(responseBody, FileUploadResponse::class.java)
    }
    
    /**
     * 停止响应
     */
    suspend fun stopResponse(
        taskId: String,
        user: String
    ): StopResponseResult = withContext(Dispatchers.IO) {
        val request = StopResponseRequest(user = user)
        val requestBody = gson.toJson(request).toRequestBody(CONTENT_TYPE_JSON.toMediaType())
        
        val httpRequest = Request.Builder()
            .url("$baseUrl/chat-messages/$taskId/stop")
            .post(requestBody)
            .addHeader("Authorization", "Bearer $apiKey")
            .addHeader("Content-Type", CONTENT_TYPE_JSON)
            .build()
        
        val response = client.newCall(httpRequest).execute()
        
        if (!response.isSuccessful) {
            val errorBody = response.body?.string()
            Log.e(TAG, "Stop response failed: ${response.code} - $errorBody")
            throw IOException("Stop response failed: ${response.code}")
        }
        
        val responseBody = response.body?.string()
            ?: throw IOException("Empty response body")
        
        gson.fromJson(responseBody, StopResponseResult::class.java)
    }
    
    /**
     * 消息反馈
     */
    suspend fun sendMessageFeedback(
        messageId: String,
        rating: String?, // "like", "dislike", null
        user: String,
        content: String? = null
    ): MessageFeedbackResult = withContext(Dispatchers.IO) {
        val request = MessageFeedbackRequest(
            rating = rating,
            user = user,
            content = content
        )
        val requestBody = gson.toJson(request).toRequestBody(CONTENT_TYPE_JSON.toMediaType())
        
        val httpRequest = Request.Builder()
            .url("$baseUrl/messages/$messageId/feedbacks")
            .post(requestBody)
            .addHeader("Authorization", "Bearer $apiKey")
            .addHeader("Content-Type", CONTENT_TYPE_JSON)
            .build()
        
        val response = client.newCall(httpRequest).execute()
        
        if (!response.isSuccessful) {
            val errorBody = response.body?.string()
            Log.e(TAG, "Message feedback failed: ${response.code} - $errorBody")
            throw IOException("Message feedback failed: ${response.code}")
        }
        
        val responseBody = response.body?.string()
            ?: throw IOException("Empty response body")
        
        gson.fromJson(responseBody, MessageFeedbackResult::class.java)
    }

    /**
     * 语音转文字
     */
    suspend fun audioToText(
        audioFile: File,
        user: String
    ): AudioToTextResponse = withContext(Dispatchers.IO) {
        Log.d(TAG, "Converting audio to text: ${audioFile.name}, size: ${audioFile.length()} bytes")

        // 验证文件格式
        val supportedFormats = listOf("mp3", "mp4", "mpeg", "mpga", "m4a", "wav", "webm")
        val fileExtension = audioFile.extension.lowercase()
        if (!supportedFormats.contains(fileExtension)) {
            throw IllegalArgumentException("不支持的音频格式: $fileExtension. 支持的格式: $supportedFormats")
        }

        // 验证文件大小 (15MB限制)
        val maxSizeBytes = 15 * 1024 * 1024 // 15MB
        if (audioFile.length() > maxSizeBytes) {
            throw IllegalArgumentException("音频文件过大: ${audioFile.length()} bytes. 最大支持: $maxSizeBytes bytes (15MB)")
        }

        // 构建multipart请求
        val requestBody = MultipartBody.Builder()
            .setType(MultipartBody.FORM)
            .addFormDataPart("user", user)
            .addFormDataPart(
                "file",
                audioFile.name,
                audioFile.asRequestBody("audio/*".toMediaType())
            )
            .build()

        val request = Request.Builder()
            .url("$baseUrl/audio-to-text")
            .post(requestBody)
            .addHeader("Authorization", "Bearer $apiKey")
            .build()

        Log.d(TAG, "Sending audio-to-text request to: $baseUrl/audio-to-text")

        val response = client.newCall(request).execute()

        if (!response.isSuccessful) {
            val errorBody = response.body?.string()
            Log.e(TAG, "Audio to text failed: ${response.code} - $errorBody")
            throw IOException("Audio to text failed: ${response.code} - $errorBody")
        }

        val responseBody = response.body?.string()
            ?: throw IOException("Empty response body")

        Log.d(TAG, "Audio to text response: $responseBody")

        gson.fromJson(responseBody, AudioToTextResponse::class.java)
    }
    
    /**
     * 测试网络连接
     */
    suspend fun testConnection(): Boolean = withContext(Dispatchers.IO) {
        try {
            // 先测试基础连接
            Log.d(TAG, "Testing connection to: $baseUrl")

            val request = Request.Builder()
                .url("$baseUrl/")  // 添加斜杠确保路径正确
                .get()  // 改用GET请求，因为HEAD可能被服务器拒绝
                .addHeader("Authorization", "Bearer $apiKey")
                .addHeader("User-Agent", "AIHealthButler-Android")
                .addHeader("Accept", "application/json")
                .build()

            val response = client.newCall(request).execute()
            val responseCode = response.code
            val responseBody = response.body?.string()

            Log.d(TAG, "Connection test - Code: $responseCode")
            Log.d(TAG, "Connection test - Body: ${responseBody?.take(200)}")

            response.close()

            // 2xx或4xx都表示服务器可达，只是可能需要正确的端点
            val isConnectable = responseCode in 200..499
            Log.d(TAG, "Connection test result: $isConnectable")

            isConnectable
        } catch (e: Exception) {
            Log.e(TAG, "Connection test failed", e)

            // 尝试简单的Socket连接测试
            try {
                val (host, port) = parseHostAndPort(baseUrl)
                Log.d(TAG, "Fallback: Testing socket connection to $host:$port")

                val socket = java.net.Socket()
                socket.connect(java.net.InetSocketAddress(host, port), 5000)
                socket.close()

                Log.d(TAG, "Socket connection successful")
                return@withContext true
            } catch (socketException: Exception) {
                Log.e(TAG, "Socket connection also failed", socketException)
                return@withContext false
            }
        }
    }

    /**
     * 解析URL获取主机和端口
     */
    private fun parseHostAndPort(url: String): Pair<String, Int> {
        return try {
            val cleanUrl = url.replace("http://", "").replace("https://", "")
            val parts = cleanUrl.split(":")
            val host = parts[0].split("/")[0]
            val port = if (parts.size > 1) {
                parts[1].split("/")[0].toIntOrNull() ?: 80
            } else {
                if (url.startsWith("https://")) 443 else 80
            }
            Pair(host, port)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to parse URL: $url", e)
            Pair("", 80)
        }
    }

    /**
     * 详细的API连接测试
     */
    suspend fun testApiEndpoint(): ApiTestResult = withContext(Dispatchers.IO) {
        val testResult = ApiTestResult()

        try {
            // 1. 测试基础连接
            Log.d(TAG, "Step 1: Testing basic connection")
            testResult.basicConnection = testConnection()

            // 2. 测试chat-messages端点
            Log.d(TAG, "Step 2: Testing chat-messages endpoint")
            val chatRequest = ChatMessageRequest(
                query = "Hello",
                user = "test-user",
                responseMode = ResponseMode.BLOCKING
            )

            val requestBody = gson.toJson(chatRequest).toRequestBody(CONTENT_TYPE_JSON.toMediaType())
            val httpRequest = Request.Builder()
                .url("$baseUrl/chat-messages")
                .post(requestBody)
                .addHeader("Authorization", "Bearer $apiKey")
                .addHeader("Content-Type", CONTENT_TYPE_JSON)
                .build()

            val response = client.newCall(httpRequest).execute()
            testResult.chatEndpoint = response.isSuccessful
            testResult.responseCode = response.code
            testResult.responseMessage = response.message

            if (!response.isSuccessful) {
                testResult.errorDetails = response.body?.string() ?: "No error details"
            }

            response.close()

        } catch (e: Exception) {
            testResult.exception = e
            testResult.errorDetails = e.message ?: "Unknown error"
            Log.e(TAG, "API test failed", e)
        }

        testResult
    }

    /**
     * 释放资源
     */
    fun release() {
        client.dispatcher.executorService.shutdown()
        client.connectionPool.evictAll()
    }
}

/**
 * API测试结果
 */
data class ApiTestResult(
    var basicConnection: Boolean = false,
    var chatEndpoint: Boolean = false,
    var responseCode: Int = 0,
    var responseMessage: String = "",
    var errorDetails: String = "",
    var exception: Exception? = null
) {
    fun isFullyWorking(): Boolean = basicConnection && chatEndpoint

    fun getDetailedReport(): String = buildString {
        appendLine("=== API测试详细报告 ===")
        appendLine("基础连接: ${if (basicConnection) "✓ 成功" else "✗ 失败"}")
        appendLine("聊天端点: ${if (chatEndpoint) "✓ 成功" else "✗ 失败"}")
        appendLine("响应码: $responseCode")
        appendLine("响应消息: $responseMessage")

        if (errorDetails.isNotEmpty()) {
            appendLine("错误详情: $errorDetails")
        }

        exception?.let {
            appendLine("异常类型: ${it.javaClass.simpleName}")
            appendLine("异常消息: ${it.message}")
        }

        appendLine("总体状态: ${if (isFullyWorking()) "✓ 正常工作" else "✗ 存在问题"}")
    }
}
