package com.example.aihealthbutler

import android.content.Context
import android.media.MediaPlayer
import android.media.MediaRecorder
import android.os.Build
import android.util.Log
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import java.io.File
import java.io.IOException
import kotlin.math.log10
import kotlin.math.sqrt

class AudioRecorderManager(private val context: Context) {
    private var mediaRecorder: MediaRecorder? = null
    private var mediaPlayer: MediaPlayer? = null
    private var audioFile: File? = null
    private var isRecording = false
    private var isPlaying = false
    private var recordingStartTime = 0L
    
    // 录音状态流
    private val _recordingState = MutableStateFlow(RecordingState.IDLE)
    val recordingState: StateFlow<RecordingState> = _recordingState
    
    // 录音时长流
    private val _recordingDuration = MutableStateFlow(0L)
    val recordingDuration: StateFlow<Long> = _recordingDuration
    
    // 音量级别流（用于波形动画）
    private val _volumeLevel = MutableStateFlow(0f)
    val volumeLevel: StateFlow<Float> = _volumeLevel
    
    // 播放状态流
    private val _playingState = MutableStateFlow(PlayingState.IDLE)
    val playingState: StateFlow<PlayingState> = _playingState
    
    enum class RecordingState {
        IDLE, RECORDING, PAUSED, COMPLETED
    }
    
    enum class PlayingState {
        IDLE, PLAYING, PAUSED, COMPLETED
    }
    
    /**
     * 开始录音
     */
    fun startRecording(): Boolean {
        return try {
            // 创建音频文件
            audioFile = createAudioFile()
            
            // 初始化MediaRecorder
            mediaRecorder = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                MediaRecorder(context)
            } else {
                @Suppress("DEPRECATION")
                MediaRecorder()
            }.apply {
                setAudioSource(MediaRecorder.AudioSource.MIC)
                setOutputFormat(MediaRecorder.OutputFormat.AAC_ADTS)
                setAudioEncoder(MediaRecorder.AudioEncoder.AAC)
                setOutputFile(audioFile?.absolutePath)
                
                try {
                    prepare()
                    start()
                } catch (e: IOException) {
                    Log.e("AudioRecorder", "prepare() failed", e)
                    return false
                }
            }
            
            isRecording = true
            recordingStartTime = System.currentTimeMillis()
            _recordingState.value = RecordingState.RECORDING
            
            // 开始监控音量级别
            startVolumeMonitoring()
            
            true
        } catch (e: Exception) {
            Log.e("AudioRecorder", "Failed to start recording", e)
            false
        }
    }
    
    /**
     * 停止录音
     */
    fun stopRecording(): File? {
        return try {
            if (isRecording && mediaRecorder != null) {
                try {
                    // 先停止录音
                    mediaRecorder?.stop()
                } catch (stopException: Exception) {
                    Log.w("AudioRecorder", "Error stopping MediaRecorder", stopException)
                    // 即使停止失败，也要继续清理资源
                }

                try {
                    // 释放资源
                    mediaRecorder?.release()
                } catch (releaseException: Exception) {
                    Log.w("AudioRecorder", "Error releasing MediaRecorder", releaseException)
                }

                mediaRecorder = null
                isRecording = false
                _recordingState.value = RecordingState.COMPLETED
                _volumeLevel.value = 0f

                // 检查文件是否存在且有效
                audioFile?.let { file ->
                    if (file.exists() && file.length() > 0) {
                        Log.d("AudioRecorder", "Recording stopped successfully, file size: ${file.length()} bytes")
                        file
                    } else {
                        Log.w("AudioRecorder", "Audio file is empty or doesn't exist")
                        null
                    }
                }
            } else {
                Log.w("AudioRecorder", "stopRecording called but not recording or mediaRecorder is null")
                null
            }
        } catch (e: Exception) {
            Log.e("AudioRecorder", "Unexpected error in stopRecording", e)
            // 确保清理状态
            cleanupRecording()
            null
        }
    }
    
    /**
     * 清理录音资源（内部方法）
     */
    private fun cleanupRecording() {
        try {
            mediaRecorder?.release()
        } catch (e: Exception) {
            Log.w("AudioRecorder", "Error releasing MediaRecorder during cleanup", e)
        }
        mediaRecorder = null
        isRecording = false
        _recordingState.value = RecordingState.IDLE
        _volumeLevel.value = 0f
    }

    /**
     * 取消录音
     */
    fun cancelRecording() {
        try {
            if (isRecording && mediaRecorder != null) {
                try {
                    mediaRecorder?.stop()
                } catch (stopException: Exception) {
                    Log.w("AudioRecorder", "Error stopping MediaRecorder during cancel", stopException)
                }

                try {
                    mediaRecorder?.release()
                } catch (releaseException: Exception) {
                    Log.w("AudioRecorder", "Error releasing MediaRecorder during cancel", releaseException)
                }
                mediaRecorder = null
                isRecording = false

                // 删除录音文件
                try {
                    audioFile?.delete()
                } catch (deleteException: Exception) {
                    Log.w("AudioRecorder", "Error deleting audio file during cancel", deleteException)
                }
                audioFile = null

                _recordingState.value = RecordingState.IDLE
                _volumeLevel.value = 0f

                Log.d("AudioRecorder", "Recording cancelled successfully")
            } else {
                Log.w("AudioRecorder", "cancelRecording called but not recording or mediaRecorder is null")
            }
        } catch (e: Exception) {
            Log.e("AudioRecorder", "Unexpected error in cancelRecording", e)
            // 确保清理状态
            cleanupRecording()
        }
    }
    
    /**
     * 播放音频
     */
    fun playAudio(audioFile: File): Boolean {
        return try {
            stopPlaying() // 停止当前播放

            mediaPlayer = MediaPlayer().apply {
                setDataSource(audioFile.absolutePath)
                setOnCompletionListener {
                    _playingState.value = PlayingState.COMPLETED
                    <EMAIL> = false
                }
                setOnErrorListener { _, _, _ ->
                    _playingState.value = PlayingState.IDLE
                    <EMAIL> = false
                    true
                }

                prepare()
                start()
            }

            isPlaying = true
            _playingState.value = PlayingState.PLAYING
            true
        } catch (e: Exception) {
            Log.e("AudioRecorder", "Failed to play audio", e)
            false
        }
    }
    
    /**
     * 停止播放
     */
    fun stopPlaying() {
        try {
            mediaPlayer?.apply {
                if (isPlaying()) {
                    stop()
                }
                release()
            }
            mediaPlayer = null
            isPlaying = false
            _playingState.value = PlayingState.IDLE
        } catch (e: Exception) {
            Log.e("AudioRecorder", "Failed to stop playing", e)
        }
    }
    
    /**
     * 获取录音时长（秒）
     */
    fun getRecordingDuration(): Long {
        return if (isRecording) {
            (System.currentTimeMillis() - recordingStartTime) / 1000
        } else {
            0L
        }
    }
    
    /**
     * 获取音频文件时长（毫秒）
     */
    fun getAudioDuration(audioFile: File): Long {
        return try {
            val mp = MediaPlayer().apply {
                setDataSource(audioFile.absolutePath)
                prepare()
            }
            val duration = mp.duration.toLong()
            mp.release()
            duration // 返回毫秒数
        } catch (e: Exception) {
            Log.e("AudioRecorder", "Failed to get audio duration", e)
            0L
        }
    }
    
    /**
     * 创建音频文件
     */
    private fun createAudioFile(): File {
        val audioDir = File(context.filesDir, "audio")
        if (!audioDir.exists()) {
            audioDir.mkdirs()
        }
        return File(audioDir, "voice_${System.currentTimeMillis()}.aac")
    }
    
    /**
     * 开始监控音量级别
     */
    private fun startVolumeMonitoring() {
        // 这里可以实现音量监控逻辑
        // 由于MediaRecorder的getMaxAmplitude()需要在录音过程中调用
        // 我们可以用协程定期获取音量级别
    }
    
    /**
     * 获取当前音量级别（0.0 - 1.0）
     */
    fun getCurrentVolumeLevel(): Float {
        return try {
            if (isRecording && mediaRecorder != null) {
                val amplitude = mediaRecorder!!.maxAmplitude
                if (amplitude > 0) {
                    // 将振幅转换为0-1的范围
                    val db = 20 * log10(amplitude.toDouble() / 32767.0)
                    ((db + 60) / 60).coerceIn(0.0, 1.0).toFloat()
                } else {
                    0f
                }
            } else {
                0f
            }
        } catch (e: Exception) {
            0f
        }
    }
    
    /**
     * 释放资源
     */
    fun release() {
        stopRecording()
        stopPlaying()
    }
}
