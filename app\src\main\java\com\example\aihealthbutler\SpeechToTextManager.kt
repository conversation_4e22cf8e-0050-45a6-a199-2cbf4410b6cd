package com.example.aihealthbutler

import android.Manifest
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Bundle
import android.speech.RecognitionListener
import android.speech.RecognizerIntent
import android.speech.SpeechRecognizer
import android.util.Log
import androidx.core.content.ContextCompat
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import com.example.aihealthbutler.speech.*
import com.example.aihealthbutler.speech.SpeechToTextState
import com.example.aihealthbutler.speech.SpeechToTextResult
import com.example.aihealthbutler.utils.OppoPermissionHelper
import java.io.File
import java.util.*

// 使用speech包中的类型定义
// SpeechToTextState 和 SpeechToTextResult 已在 speech 包中定义

/**
 * 语音转文字管理器
 * 支持实时语音识别和音频文件转文字
 * 现在支持多种语音识别引擎
 */
class SpeechToTextManager(val context: Context) {

    private var speechRecognizer: SpeechRecognizer? = null
    private var isListening = false
    private var initializationAttempts = 0
    private val maxInitializationAttempts = 3

    // 多引擎支持
    private val speechEngineManager = SpeechEngineManager(context)
    private var useMultiEngine = true // 是否启用多引擎模式

    // 状态流
    private val _state = MutableStateFlow(SpeechToTextState.IDLE)
    val state: StateFlow<SpeechToTextState> = _state

    // 结果流
    private val _result = MutableStateFlow(SpeechToTextResult())
    val result: StateFlow<SpeechToTextResult> = _result

    // 错误信息流
    private val _errorMessage = MutableStateFlow("")
    val errorMessage: StateFlow<String> = _errorMessage

    // 当前引擎信息
    val currentEngineInfo: StateFlow<String> = speechEngineManager.currentEngineInfo

    /**
     * 设置错误信息（供外部调用）
     */
    fun setError(message: String) {
        _errorMessage.value = message
        _state.value = SpeechToTextState.ERROR
    }

    /**
     * 启用/禁用多引擎模式
     */
    fun setMultiEngineMode(enabled: Boolean) {
        useMultiEngine = enabled
        if (enabled) {
            CoroutineScope(Dispatchers.Main).launch {
                speechEngineManager.reinitializeEngines()
            }
        }
    }

    /**
     * 切换语音识别引擎
     */
    fun switchEngine(engineType: SpeechEngineType): Boolean {
        if (!useMultiEngine) return false
        return speechEngineManager.switchToEngine(engineType)
    }

    /**
     * 获取可用引擎列表
     */
    fun getAvailableEngines(): Map<SpeechEngineType, Boolean> {
        return speechEngineManager.engineStatus.value
    }

    /**
     * 获取当前使用的引擎类型
     */
    fun getCurrentEngineType(): SpeechEngineType {
        return speechEngineManager.getCurrentEngineType()
    }

    /**
     * 获取引擎诊断信息
     */
    fun getEnginesDiagnosticInfo(): String {
        return speechEngineManager.getDiagnosticInfo()
    }

    /**
     * 重新初始化语音识别器
     */
    fun reinitialize() {
        Log.d("SpeechToText", "Attempting to reinitialize SpeechRecognizer")
        release()
        initializationAttempts = 0
        initializeSpeechRecognizer()
    }

    /**
     * 强制重新初始化（用于严重错误后的恢复）
     */
    fun forceReinitialize() {
        Log.w("SpeechToText", "Force reinitializing SpeechRecognizer")

        // 强制清理所有状态
        try {
            speechRecognizer?.destroy()
        } catch (e: Exception) {
            Log.w("SpeechToText", "Error destroying SpeechRecognizer during force reinit", e)
        }

        speechRecognizer = null
        isListening = false
        initializationAttempts = 0

        // 重置状态
        _state.value = SpeechToTextState.IDLE
        _result.value = SpeechToTextResult()
        _errorMessage.value = ""

        // 等待更长时间确保资源完全释放
        android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
            Log.d("SpeechToText", "Starting force reinitialization after delay")
            initializeSpeechRecognizer()
        }, 3000) // 3秒延迟
    }

    /**
     * 获取Google服务安装指导
     */
    fun getGoogleServiceInstallGuide(): String {
        val manufacturer = android.os.Build.MANUFACTURER
        val model = android.os.Build.MODEL

        return when {
            manufacturer.equals("OPPO", ignoreCase = true) -> {
                buildString {
                    append("OPPO ${model} 语音服务设置指南:\n\n")
                    append("方法一 - 启用Google语音输入:\n")
                    append("1. 打开「设置」\n")
                    append("2. 进入「其他设置」\n")
                    append("3. 选择「语言与输入法」\n")
                    append("4. 点击「虚拟键盘」\n")
                    append("5. 启用「Google语音输入」\n\n")

                    append("方法二 - 安装Google应用:\n")
                    append("1. 打开「软件商店」\n")
                    append("2. 搜索「Google」\n")
                    append("3. 安装「Google」应用\n")
                    append("4. 重启手机后重试\n\n")

                    append("方法三 - 手动安装:\n")
                    append("1. 下载Google Play服务APK\n")
                    append("2. 允许安装未知来源应用\n")
                    append("3. 安装后重启设备\n\n")

                    append("注意: 部分OPPO设备可能需要开启开发者选项")
                }
            }
            manufacturer.equals("Xiaomi", ignoreCase = true) -> {
                "小米设备:\n1. 打开小米应用商店\n2. 搜索并安装Google服务\n3. 在设置中启用Google语音输入"
            }
            manufacturer.equals("Huawei", ignoreCase = true) -> {
                "华为设备:\n1. 由于GMS限制，建议使用华为语音助手\n2. 或通过第三方渠道安装Google服务"
            }
            else -> {
                "通用方法:\n1. 安装Google Play商店\n2. 更新Google Play服务\n3. 安装Google应用\n4. 在系统设置中启用Google语音输入"
            }
        }
    }

    /**
     * 检查语音识别器是否可用
     */
    fun isAvailable(): Boolean {
        return speechRecognizer != null && SpeechRecognizer.isRecognitionAvailable(context)
    }

    /**
     * 获取设备兼容性信息
     */
    fun getDeviceCompatibilityInfo(): String {
        val info = StringBuilder()

        // 检查语音识别可用性
        val isRecognitionAvailable = SpeechRecognizer.isRecognitionAvailable(context)
        info.append("语音识别支持: ${if (isRecognitionAvailable) "是" else "否"}\n")

        // 检查权限状态
        val hasPermission = ContextCompat.checkSelfPermission(
            context,
            Manifest.permission.RECORD_AUDIO
        ) == PackageManager.PERMISSION_GRANTED
        info.append("录音权限: ${if (hasPermission) "已授予" else "未授予"}\n")

        // 检查识别器状态
        info.append("识别器状态: ${if (speechRecognizer != null) "已初始化" else "未初始化"}\n")

        // 检查Android版本
        info.append("Android版本: ${android.os.Build.VERSION.RELEASE}\n")

        // 检查设备制造商
        info.append("设备制造商: ${android.os.Build.MANUFACTURER}\n")
        info.append("设备型号: ${android.os.Build.MODEL}")

        return info.toString()
    }

    /**
     * 诊断语音识别问题
     */
    fun diagnoseIssues(): List<String> {
        val issues = mutableListOf<String>()

        // 检查基本支持
        if (!SpeechRecognizer.isRecognitionAvailable(context)) {
            issues.add("设备不支持语音识别功能")
            issues.add("建议：检查是否安装了Google语音服务")
        }

        // 检查权限
        if (ContextCompat.checkSelfPermission(context, Manifest.permission.RECORD_AUDIO)
            != PackageManager.PERMISSION_GRANTED) {
            issues.add("缺少录音权限")
            issues.add("建议：在应用设置中授予录音权限")
        }

        // 检查识别器
        if (speechRecognizer == null) {
            issues.add("语音识别器未能正确初始化")
            issues.add("建议：重启应用或检查Google语音服务")
        }

        // 检查网络连接（语音识别通常需要网络）
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE)
            as? android.net.ConnectivityManager
        val activeNetwork = connectivityManager?.activeNetworkInfo
        if (activeNetwork?.isConnected != true) {
            issues.add("网络连接不可用")
            issues.add("建议：检查网络连接，语音识别需要网络支持")
        }

        return issues
    }

    /**
     * 执行详细的系统诊断
     */
    private fun performDetailedDiagnostic(): String {
        val diagnostic = StringBuilder()

        // 基本设备信息
        diagnostic.append("设备: ${android.os.Build.MANUFACTURER} ${android.os.Build.MODEL}\n")
        diagnostic.append("Android版本: ${android.os.Build.VERSION.RELEASE} (API ${android.os.Build.VERSION.SDK_INT})\n")

        // 语音识别支持检查
        val isRecognitionAvailable = SpeechRecognizer.isRecognitionAvailable(context)
        diagnostic.append("语音识别支持: ${if (isRecognitionAvailable) "是" else "否"}\n")

        // 权限状态
        val hasRecordPermission = ContextCompat.checkSelfPermission(
            context, Manifest.permission.RECORD_AUDIO
        ) == PackageManager.PERMISSION_GRANTED
        diagnostic.append("录音权限: ${if (hasRecordPermission) "已授予" else "未授予"}\n")

        // 网络状态
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE)
            as? android.net.ConnectivityManager
        val activeNetwork = connectivityManager?.activeNetworkInfo
        val networkStatus = when {
            activeNetwork == null -> "无网络"
            activeNetwork.isConnected -> "${activeNetwork.typeName} 已连接"
            else -> "${activeNetwork.typeName} 未连接"
        }
        diagnostic.append("网络状态: $networkStatus\n")

        // 应用包名
        diagnostic.append("应用包名: ${context.packageName}\n")

        // 当前识别器状态
        diagnostic.append("识别器状态: ${if (speechRecognizer != null) "已创建" else "未创建"}\n")
        diagnostic.append("初始化尝试次数: $initializationAttempts\n")

        // 系统语言
        diagnostic.append("系统语言: ${Locale.getDefault()}\n")

        return diagnostic.toString()
    }

    /**
     * 检查Google语音服务状态
     */
    fun checkGoogleSpeechService(): String {
        return try {
            val packageManager = context.packageManager
            val googleServicesPackages = listOf(
                "com.google.android.googlequicksearchbox", // Google App
                "com.google.android.gms", // Google Play Services
                "com.google.android.tts", // Google Text-to-Speech
                "com.android.speech.tts", // 系统TTS
                "com.google.android.voicesearch" // Google语音搜索
            )

            val serviceStatus = StringBuilder()
            serviceStatus.append("语音服务状态:\n")

            var hasAnyGoogleService = false
            for (packageName in googleServicesPackages) {
                try {
                    val packageInfo = packageManager.getPackageInfo(packageName, 0)
                    val isEnabled = packageManager.getApplicationInfo(packageName, 0).enabled
                    serviceStatus.append("• ${getServiceName(packageName)}: 已安装${if (isEnabled) "且启用" else "但禁用"}\n")
                    if (isEnabled) hasAnyGoogleService = true
                } catch (e: Exception) {
                    serviceStatus.append("• ${getServiceName(packageName)}: 未安装\n")
                }
            }

            // 检查OPPO特有的语音服务
            if (android.os.Build.MANUFACTURER.equals("OPPO", ignoreCase = true)) {
                val oppoServices = listOf(
                    "com.oppo.speech", // OPPO语音服务
                    "com.coloros.speechassist", // ColorOS语音助手
                    "com.oppo.voiceassistant" // OPPO语音助手
                )

                serviceStatus.append("\nOPPO语音服务:\n")
                for (packageName in oppoServices) {
                    try {
                        val packageInfo = packageManager.getPackageInfo(packageName, 0)
                        val isEnabled = packageManager.getApplicationInfo(packageName, 0).enabled
                        serviceStatus.append("• ${getServiceName(packageName)}: 已安装${if (isEnabled) "且启用" else "但禁用"}\n")
                    } catch (e: Exception) {
                        serviceStatus.append("• ${getServiceName(packageName)}: 未安装\n")
                    }
                }
            }

            if (!hasAnyGoogleService) {
                serviceStatus.append("\n⚠️ 未检测到可用的Google语音服务\n")
                serviceStatus.append("建议:\n")
                serviceStatus.append("1. 安装Google应用\n")
                serviceStatus.append("2. 更新Google Play服务\n")
                serviceStatus.append("3. 在设置中启用Google语音服务")
            }

            serviceStatus.toString()
        } catch (e: Exception) {
            "语音服务检查失败: ${e.message}"
        }
    }

    /**
     * 获取服务友好名称
     */
    private fun getServiceName(packageName: String): String {
        return when (packageName) {
            "com.google.android.googlequicksearchbox" -> "Google应用"
            "com.google.android.gms" -> "Google Play服务"
            "com.google.android.tts" -> "Google文字转语音"
            "com.android.speech.tts" -> "系统文字转语音"
            "com.google.android.voicesearch" -> "Google语音搜索"
            "com.oppo.speech" -> "OPPO语音服务"
            "com.coloros.speechassist" -> "ColorOS语音助手"
            "com.oppo.voiceassistant" -> "OPPO语音助手"
            else -> packageName
        }
    }

    /**
     * 创建语音识别器（带备用方案）
     */
    private fun createSpeechRecognizerWithFallback(): SpeechRecognizer? {
        return try {
            // 首先尝试默认方式创建
            var recognizer = SpeechRecognizer.createSpeechRecognizer(context)

            if (recognizer != null) {
                Log.d("SpeechToText", "Default SpeechRecognizer created successfully")
                return recognizer
            }

            // 如果默认方式失败，尝试指定服务组件
            Log.d("SpeechToText", "Default creation failed, trying with specific component")

            // 尝试Google语音服务
            val googleComponent = android.content.ComponentName(
                "com.google.android.googlequicksearchbox",
                "com.google.android.voicesearch.serviceapi.GoogleRecognitionService"
            )

            try {
                recognizer = SpeechRecognizer.createSpeechRecognizer(context, googleComponent)
                if (recognizer != null) {
                    Log.d("SpeechToText", "Google SpeechRecognizer created successfully")
                    return recognizer
                }
            } catch (e: Exception) {
                Log.w("SpeechToText", "Failed to create Google SpeechRecognizer", e)
            }

            // 对于OPPO设备，尝试系统语音服务
            if (android.os.Build.MANUFACTURER.equals("OPPO", ignoreCase = true)) {
                Log.d("SpeechToText", "Trying OPPO specific speech service")

                val oppoComponents = listOf(
                    android.content.ComponentName(
                        "com.oppo.speech",
                        "com.oppo.speech.recognition.RecognitionService"
                    ),
                    android.content.ComponentName(
                        "com.coloros.speechassist",
                        "com.coloros.speechassist.service.SpeechRecognitionService"
                    )
                )

                for (component in oppoComponents) {
                    try {
                        recognizer = SpeechRecognizer.createSpeechRecognizer(context, component)
                        if (recognizer != null) {
                            Log.d("SpeechToText", "OPPO SpeechRecognizer created with component: ${component.className}")
                            return recognizer
                        }
                    } catch (e: Exception) {
                        Log.w("SpeechToText", "Failed to create OPPO SpeechRecognizer with component: ${component.className}", e)
                    }
                }
            }

            Log.e("SpeechToText", "All SpeechRecognizer creation attempts failed")
            null

        } catch (e: Exception) {
            Log.e("SpeechToText", "Exception in createSpeechRecognizerWithFallback", e)
            null
        }
    }

    init {
        Log.d("SpeechToText", "Initializing SpeechToTextManager")
        initializeSpeechRecognizer()
    }

    /**
     * 初始化语音识别器
     */
    private fun initializeSpeechRecognizer() {
        initializationAttempts++
        Log.d("SpeechToText", "Starting SpeechRecognizer initialization - attempt $initializationAttempts")

        try {
            // 详细的环境检查
            val diagnosticInfo = performDetailedDiagnostic()
            Log.d("SpeechToText", "Diagnostic info: $diagnosticInfo")

            // 检查设备是否支持语音识别
            if (!SpeechRecognizer.isRecognitionAvailable(context)) {
                Log.e("SpeechToText", "Speech recognition not available on this device")

                val errorMsg = buildString {
                    append("设备不支持语音识别功能\n")
                    append("设备信息: ${android.os.Build.MANUFACTURER} ${android.os.Build.MODEL}\n")
                    append("Android版本: ${android.os.Build.VERSION.RELEASE}\n\n")

                    // 简化的错误信息，详细指导通过按钮查看
                    if (android.os.Build.MANUFACTURER.equals("OPPO", ignoreCase = true)) {
                        append("OPPO设备需要特殊设置\n")
                        append("请点击「安装指导」查看详细步骤")
                    } else {
                        append("请确保已安装Google语音服务\n")
                        append("点击「安装指导」查看解决方案")
                    }
                }

                _errorMessage.value = errorMsg
                _state.value = SpeechToTextState.ERROR
                return
            }

            // 检查权限
            if (ContextCompat.checkSelfPermission(context, Manifest.permission.RECORD_AUDIO)
                != PackageManager.PERMISSION_GRANTED) {
                Log.e("SpeechToText", "Missing RECORD_AUDIO permission")
                _errorMessage.value = "缺少录音权限\n请在应用设置中授予录音权限"
                _state.value = SpeechToTextState.ERROR
                return
            }

            // 检查网络连接
            val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE)
                as? android.net.ConnectivityManager
            val activeNetwork = connectivityManager?.activeNetworkInfo
            if (activeNetwork?.isConnected != true) {
                Log.w("SpeechToText", "No network connection available")
                _errorMessage.value = "网络连接不可用\n语音识别需要网络支持，请检查网络连接"
                _state.value = SpeechToTextState.ERROR
                return
            }

            // 释放之前的识别器
            try {
                speechRecognizer?.destroy()
                speechRecognizer = null
                Log.d("SpeechToText", "Previous SpeechRecognizer destroyed")
            } catch (destroyException: Exception) {
                Log.w("SpeechToText", "Error destroying previous SpeechRecognizer", destroyException)
            }

            // 等待一小段时间确保资源完全释放
            Thread.sleep(100)

            // 创建语音识别器
            Log.d("SpeechToText", "Creating new SpeechRecognizer...")
            speechRecognizer = createSpeechRecognizerWithFallback()

            if (speechRecognizer == null) {
                Log.e("SpeechToText", "Failed to create SpeechRecognizer - attempt $initializationAttempts")

                val errorMsg = buildString {
                    append("语音识别服务创建失败\n")
                    append("尝试次数: $initializationAttempts/$maxInitializationAttempts\n")
                    if (initializationAttempts >= maxInitializationAttempts) {
                        append("可能原因:\n")
                        append("• Google语音服务未安装或已禁用\n")
                        append("• 设备不支持语音识别\n")
                        append("• 系统资源不足\n")
                        append("建议:\n")
                        append("• 重启应用\n")
                        append("• 检查Google Play服务\n")
                        append("• 重启设备")
                    }
                }

                if (initializationAttempts < maxInitializationAttempts) {
                    Log.d("SpeechToText", "Retrying initialization in 2 seconds...")
                    // 增加重试间隔
                    android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                        initializeSpeechRecognizer()
                    }, 2000)
                    return
                } else {
                    _errorMessage.value = errorMsg
                    _state.value = SpeechToTextState.ERROR
                    return
                }
            }

            // 验证识别器是否真正可用
            try {
                speechRecognizer?.setRecognitionListener(recognitionListener)
                Log.d("SpeechToText", "Recognition listener set successfully")
            } catch (listenerException: Exception) {
                Log.e("SpeechToText", "Failed to set recognition listener", listenerException)
                speechRecognizer?.destroy()
                speechRecognizer = null

                if (initializationAttempts < maxInitializationAttempts) {
                    android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                        initializeSpeechRecognizer()
                    }, 2000)
                    return
                } else {
                    _errorMessage.value = "语音识别器配置失败\n错误: ${listenerException.message}"
                    _state.value = SpeechToTextState.ERROR
                    return
                }
            }

            Log.d("SpeechToText", "SpeechRecognizer initialized successfully on attempt $initializationAttempts")

            // 重置错误状态
            if (_state.value == SpeechToTextState.ERROR) {
                _state.value = SpeechToTextState.IDLE
                _errorMessage.value = ""
            }

            // 重置重试计数器
            initializationAttempts = 0

        } catch (e: Exception) {
            Log.e("SpeechToText", "Exception during SpeechRecognizer initialization - attempt $initializationAttempts", e)

            val errorMsg = buildString {
                append("语音识别器初始化异常\n")
                append("错误: ${e.message}\n")
                append("尝试次数: $initializationAttempts/$maxInitializationAttempts")
                if (initializationAttempts >= maxInitializationAttempts) {
                    append("\n建议:\n")
                    append("• 重启应用\n")
                    append("• 检查设备设置\n")
                    append("• 联系技术支持")
                }
            }

            if (initializationAttempts < maxInitializationAttempts) {
                Log.d("SpeechToText", "Retrying initialization after exception...")
                android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                    initializeSpeechRecognizer()
                }, 2000)
            } else {
                _errorMessage.value = errorMsg
                _state.value = SpeechToTextState.ERROR
            }
        }
    }
    
    /**
     * 开始实时语音识别
     */
    fun startListening() {
        Log.d("SpeechToText", "startListening called")

        if (isListening) {
            Log.d("SpeechToText", "Already listening, ignoring start request")
            return
        }

        // 如果启用多引擎模式，优先使用多引擎
        if (useMultiEngine) {
            startListeningWithMultiEngine()
            return
        }

        // 执行启动前诊断
        val diagnosticInfo = performDetailedDiagnostic()
        Log.d("SpeechToText", "Pre-start diagnostic:\n$diagnosticInfo")

        // 检查权限（OPPO设备特殊处理）
        val permissionStatus = OppoPermissionHelper.checkRecordAudioPermission(context)
        when (permissionStatus) {
            OppoPermissionHelper.PermissionStatus.DENIED -> {
                Log.e("SpeechToText", "Missing RECORD_AUDIO permission")
                _errorMessage.value = "需要录音权限进行语音识别\n请在应用设置中授予录音权限"
                _state.value = SpeechToTextState.ERROR
                return
            }
            OppoPermissionHelper.PermissionStatus.OPPO_SPECIAL_HANDLING_NEEDED -> {
                Log.w("SpeechToText", "OPPO device detected, special permission handling needed")
                val oppoGuide = OppoPermissionHelper.getPermissionGuideMessage()
                val diagnostic = OppoPermissionHelper.getOppoPermissionDiagnostic(context)
                _errorMessage.value = "$oppoGuide\n\n诊断信息:\n$diagnostic"
                _state.value = SpeechToTextState.ERROR
                return
            }
            OppoPermissionHelper.PermissionStatus.GRANTED -> {
                Log.d("SpeechToText", "RECORD_AUDIO permission granted")
            }
        }

        // 检查设备支持
        if (!SpeechRecognizer.isRecognitionAvailable(context)) {
            Log.e("SpeechToText", "Speech recognition not available")
            val googleServiceInfo = checkGoogleSpeechService()
            _errorMessage.value = "设备不支持语音识别\n$googleServiceInfo"
            _state.value = SpeechToTextState.ERROR
            return
        }

        // 检查网络连接
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE)
            as? android.net.ConnectivityManager
        val activeNetwork = connectivityManager?.activeNetworkInfo
        if (activeNetwork?.isConnected != true) {
            Log.w("SpeechToText", "No network connection")
            _errorMessage.value = "网络连接不可用\n语音识别需要网络支持"
            _state.value = SpeechToTextState.ERROR
            return
        }

        // 如果识别器为空，尝试重新初始化
        if (speechRecognizer == null) {
            Log.d("SpeechToText", "SpeechRecognizer is null, attempting to reinitialize")

            // 如果之前初始化失败次数过多，使用强制重新初始化
            if (initializationAttempts >= maxInitializationAttempts) {
                Log.w("SpeechToText", "Previous initialization failed multiple times, using force reinitialize")
                forceReinitialize()
                _errorMessage.value = "正在重新初始化语音识别器\n请稍后重试"
                _state.value = SpeechToTextState.ERROR
                return
            } else {
                reinitialize()

                // 等待一小段时间让初始化完成
                Thread.sleep(500)

                // 如果重新初始化后仍然为空，返回错误
                if (speechRecognizer == null) {
                    Log.e("SpeechToText", "Reinitialize failed, SpeechRecognizer still null")
                    _errorMessage.value = "语音识别器初始化失败\n请重启应用或检查设备设置"
                    _state.value = SpeechToTextState.ERROR
                    return
                }
            }
        }

        speechRecognizer?.let { recognizer ->
            Log.d("SpeechToText", "Preparing recognition intent")

            val intent = Intent(RecognizerIntent.ACTION_RECOGNIZE_SPEECH).apply {
                putExtra(RecognizerIntent.EXTRA_LANGUAGE_MODEL, RecognizerIntent.LANGUAGE_MODEL_FREE_FORM)
                putExtra(RecognizerIntent.EXTRA_LANGUAGE, Locale.getDefault())
                putExtra(RecognizerIntent.EXTRA_PARTIAL_RESULTS, true)
                putExtra(RecognizerIntent.EXTRA_MAX_RESULTS, 5)
                putExtra(RecognizerIntent.EXTRA_CALLING_PACKAGE, context.packageName)
                // 添加更多配置以提高识别准确性
                putExtra(RecognizerIntent.EXTRA_SPEECH_INPUT_COMPLETE_SILENCE_LENGTH_MILLIS, 1500)
                putExtra(RecognizerIntent.EXTRA_SPEECH_INPUT_POSSIBLY_COMPLETE_SILENCE_LENGTH_MILLIS, 1500)
                // 添加更多稳定性配置
                putExtra(RecognizerIntent.EXTRA_PREFER_OFFLINE, false) // 优先使用在线识别
                putExtra("android.speech.extra.EXTRA_ADDITIONAL_LANGUAGES", arrayOf("zh-CN", "en-US"))
            }

            try {
                Log.d("SpeechToText", "Starting speech recognition with enhanced configuration")

                // 验证识别器状态
                if (recognizer == speechRecognizer) {
                    recognizer.startListening(intent)
                    isListening = true
                    _state.value = SpeechToTextState.LISTENING
                    _result.value = SpeechToTextResult()
                    _errorMessage.value = ""

                    Log.d("SpeechToText", "Speech recognition started successfully")

                    // 设置超时保护
                    android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                        if (isListening && _state.value == SpeechToTextState.LISTENING) {
                            Log.w("SpeechToText", "Speech recognition timeout, stopping")
                            stopListening()
                            _errorMessage.value = "语音识别超时\n请重试"
                            _state.value = SpeechToTextState.ERROR
                        }
                    }, 30000) // 30秒超时

                } else {
                    Log.e("SpeechToText", "SpeechRecognizer instance mismatch")
                    _errorMessage.value = "语音识别器状态异常\n请重试"
                    _state.value = SpeechToTextState.ERROR
                }

            } catch (e: Exception) {
                Log.e("SpeechToText", "Failed to start listening", e)
                isListening = false

                val errorMsg = buildString {
                    append("启动语音识别失败\n")
                    append("错误: ${e.message}\n")
                    append("建议:\n")
                    append("• 检查网络连接\n")
                    append("• 重启应用\n")
                    append("• 检查Google语音服务")
                }

                _errorMessage.value = errorMsg
                _state.value = SpeechToTextState.ERROR

                // 启动失败后尝试重新初始化
                android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                    Log.d("SpeechToText", "Attempting reinitialize after start failure")
                    forceReinitialize()
                }, 1000)
            }
        } ?: run {
            Log.e("SpeechToText", "SpeechRecognizer is still null after reinitialize attempt")
            val googleServiceInfo = checkGoogleSpeechService()
            _errorMessage.value = "语音识别器不可用\n$googleServiceInfo\n建议重启应用"
            _state.value = SpeechToTextState.ERROR
        }
    }
    
    /**
     * 停止语音识别
     */
    fun stopListening() {
        if (!isListening) return

        try {
            if (useMultiEngine) {
                speechEngineManager.getCurrentEngine()?.stopListening()
            } else {
                speechRecognizer?.stopListening()
            }
            isListening = false
            _state.value = SpeechToTextState.PROCESSING
        } catch (e: Exception) {
            Log.e("SpeechToText", "Error stopping speech recognition", e)
            _errorMessage.value = "停止语音识别时出错: ${e.message}"
            _state.value = SpeechToTextState.ERROR
            isListening = false
        }
    }
    
    /**
     * 取消语音识别
     */
    fun cancelListening() {
        if (!isListening) return

        try {
            if (useMultiEngine) {
                speechEngineManager.getCurrentEngine()?.cancelListening()
            } else {
                speechRecognizer?.cancel()
            }
            isListening = false
            _state.value = SpeechToTextState.IDLE
            _result.value = SpeechToTextResult()
        } catch (e: Exception) {
            Log.e("SpeechToText", "Error cancelling speech recognition", e)
            isListening = false
            _state.value = SpeechToTextState.IDLE
        }
    }
    
    /**
     * 转换音频文件为文字
     * 使用实时语音识别来处理录制的音频
     */
    fun convertAudioFileToText(audioFile: File) {
        if (!audioFile.exists()) {
            _errorMessage.value = "音频文件不存在"
            _state.value = SpeechToTextState.ERROR
            return
        }

        // 直接使用实时语音识别
        // 这是一个简化的实现，实际上我们会在录音时直接进行语音识别
        startListening()
    }

    /**
     * 开始语音转文字（从录音开始）
     */
    fun startSpeechToText() {
        startListening()
    }
    
    /**
     * 语音识别监听器
     */
    private val recognitionListener = object : RecognitionListener {
        override fun onReadyForSpeech(params: Bundle?) {
            Log.d("SpeechToText", "Ready for speech")
            _state.value = SpeechToTextState.LISTENING
        }
        
        override fun onBeginningOfSpeech() {
            Log.d("SpeechToText", "Beginning of speech")
        }
        
        override fun onRmsChanged(rmsdB: Float) {
            // 可以用于显示音量级别
        }
        
        override fun onBufferReceived(buffer: ByteArray?) {
            // 接收到音频数据
        }
        
        override fun onEndOfSpeech() {
            Log.d("SpeechToText", "End of speech")
            _state.value = SpeechToTextState.PROCESSING
        }
        
        override fun onError(error: Int) {
            Log.e("SpeechToText", "Recognition error: $error")
            isListening = false
            
            val errorMsg = when (error) {
                SpeechRecognizer.ERROR_AUDIO -> "音频录制错误"
                SpeechRecognizer.ERROR_CLIENT -> "客户端错误"
                SpeechRecognizer.ERROR_INSUFFICIENT_PERMISSIONS -> "权限不足"
                SpeechRecognizer.ERROR_NETWORK -> "网络错误"
                SpeechRecognizer.ERROR_NETWORK_TIMEOUT -> "网络超时"
                SpeechRecognizer.ERROR_NO_MATCH -> "未识别到语音"
                SpeechRecognizer.ERROR_RECOGNIZER_BUSY -> "识别器忙碌"
                SpeechRecognizer.ERROR_SERVER -> "服务器错误"
                SpeechRecognizer.ERROR_SPEECH_TIMEOUT -> "语音超时"
                else -> "未知错误"
            }
            
            _errorMessage.value = errorMsg
            _state.value = SpeechToTextState.ERROR
        }
        
        override fun onResults(results: Bundle?) {
            Log.d("SpeechToText", "Recognition results received")
            isListening = false
            
            results?.getStringArrayList(SpeechRecognizer.RESULTS_RECOGNITION)?.let { matches ->
                if (matches.isNotEmpty()) {
                    val confidence = results.getFloatArray(SpeechRecognizer.CONFIDENCE_SCORES)
                    _result.value = SpeechToTextResult(
                        text = matches[0],
                        confidence = confidence?.get(0) ?: 0f,
                        isPartial = false
                    )
                    _state.value = SpeechToTextState.SUCCESS
                } else {
                    _errorMessage.value = "未识别到有效语音"
                    _state.value = SpeechToTextState.ERROR
                }
            }
        }
        
        override fun onPartialResults(partialResults: Bundle?) {
            Log.d("SpeechToText", "Partial results received")
            
            partialResults?.getStringArrayList(SpeechRecognizer.RESULTS_RECOGNITION)?.let { matches ->
                if (matches.isNotEmpty()) {
                    _result.value = SpeechToTextResult(
                        text = matches[0],
                        confidence = 0f,
                        isPartial = true
                    )
                }
            }
        }
        
        override fun onEvent(eventType: Int, params: Bundle?) {
            // 其他事件
        }
    }
    
    /**
     * 使用多引擎模式开始语音识别
     */
    private fun startListeningWithMultiEngine() {
        Log.d("SpeechToText", "Starting listening with multi-engine mode")

        val currentEngine = speechEngineManager.getCurrentEngine()
        if (currentEngine == null) {
            _errorMessage.value = "没有可用的语音识别引擎\n" +
                    "请检查以下选项:\n" +
                    "• Google语音服务\n" +
                    "• 讯飞语音SDK\n" +
                    "• Vosk离线引擎"
            _state.value = SpeechToTextState.ERROR
            return
        }

        // 检查权限
        if (ContextCompat.checkSelfPermission(context, Manifest.permission.RECORD_AUDIO)
            != PackageManager.PERMISSION_GRANTED) {
            Log.e("SpeechToText", "Missing RECORD_AUDIO permission")
            _errorMessage.value = "需要录音权限进行语音识别\n请在应用设置中授予录音权限"
            _state.value = SpeechToTextState.ERROR
            return
        }

        try {
            // 监听引擎状态变化
            CoroutineScope(Dispatchers.Main).launch {
                currentEngine.state.collect { state ->
                    _state.value = state
                }
            }

            // 监听识别结果
            CoroutineScope(Dispatchers.Main).launch {
                currentEngine.result.collect { result ->
                    _result.value = result
                }
            }

            // 监听错误信息
            CoroutineScope(Dispatchers.Main).launch {
                currentEngine.errorMessage.collect { error ->
                    if (error.isNotEmpty()) {
                        _errorMessage.value = error
                    }
                }
            }

            // 开始识别
            currentEngine.startListening()
            isListening = true

        } catch (e: Exception) {
            Log.e("SpeechToText", "Failed to start multi-engine listening", e)
            _errorMessage.value = "启动语音识别失败: ${e.message}"
            _state.value = SpeechToTextState.ERROR
        }
    }

    /**
     * 释放资源
     */
    fun release() {
        speechRecognizer?.destroy()
        speechRecognizer = null
        speechEngineManager.release()
        isListening = false
        _state.value = SpeechToTextState.IDLE
    }
    
    /**
     * 检查是否正在监听
     */
    fun isListening(): Boolean = isListening
    
    /**
     * 重置状态
     */
    fun reset() {
        cancelListening()
        _result.value = SpeechToTextResult()
        _errorMessage.value = ""
        _state.value = SpeechToTextState.IDLE
    }
}
