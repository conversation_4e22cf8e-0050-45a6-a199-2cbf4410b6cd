package com.example.aihealthbutler.dify

/**
 * Dify API 配置
 */
object DifyConfig {
    
    /**
     * Dify API 基础URL (HTTPS)
     */
    const val BASE_URL = "https://qcx.yuneyang.top/v1"
    
    /**
     * Dify API Key
     * 注意：在生产环境中，应该将API Key存储在更安全的地方，
     * 比如加密的SharedPreferences或者服务器端
     */
    const val API_KEY = "app-AMAy2YskOmHWtrTi3wZazUVm"
    
    /**
     * 默认用户标识前缀
     */
    const val USER_ID_PREFIX = "android-user-"
    
    /**
     * 文件上传最大大小（MB）
     */
    const val MAX_FILE_SIZE_MB = 50
    
    /**
     * 请求超时时间（秒）
     */
    const val CONNECT_TIMEOUT_SECONDS = 30L
    const val READ_TIMEOUT_SECONDS = 60L
    const val WRITE_TIMEOUT_SECONDS = 60L
    
    /**
     * 支持的响应模式
     */
    object ResponseModes {
        const val STREAMING = "streaming"
        const val BLOCKING = "blocking"
    }
    
    /**
     * 默认输入参数
     */
    val DEFAULT_INPUTS = emptyMap<String, Any>()
    
    /**
     * 是否自动生成对话标题
     */
    const val AUTO_GENERATE_NAME = true
    
    /**
     * 获取完整的API URL
     */
    fun getApiUrl(endpoint: String): String {
        return "$BASE_URL/$endpoint"
    }
    
    /**
     * 获取聊天消息API URL
     */
    fun getChatMessagesUrl(): String {
        return getApiUrl("chat-messages")
    }
    
    /**
     * 获取文件上传API URL
     */
    fun getFileUploadUrl(): String {
        return getApiUrl("files/upload")
    }
    
    /**
     * 获取停止响应API URL
     */
    fun getStopResponseUrl(taskId: String): String {
        return getApiUrl("chat-messages/$taskId/stop")
    }
    
    /**
     * 获取消息反馈API URL
     */
    fun getMessageFeedbackUrl(messageId: String): String {
        return getApiUrl("messages/$messageId/feedbacks")
    }

    /**
     * 获取语音转文字API URL
     */
    fun getAudioToTextUrl(): String {
        return getApiUrl("audio-to-text")
    }
}
