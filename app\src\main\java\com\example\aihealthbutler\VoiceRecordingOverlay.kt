package com.example.aihealthbutler

import androidx.compose.animation.core.*
//import androidx.compose.animation.animateFloatAsState
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.drawIntoCanvas

import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.aihealthbutler.speech.SpeechToTextState
import com.example.aihealthbutler.speech.SpeechToTextResult
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.draw.shadow
import androidx.compose.foundation.border
import androidx.compose.animation.core.*
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.graphics.nativeCanvas
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.TileMode
import androidx.compose.foundation.border
import androidx.compose.animation.core.*
import androidx.compose.foundation.shape.CircleShape
import kotlin.math.sin
import kotlin.math.cos
import kotlin.math.PI
import kotlin.math.abs
import kotlin.random.Random


@Composable
fun VoiceRecordingOverlay(
    isRecording: Boolean,
    recordingDuration: Long,
    volumeLevel: Float,
    isCancelMode: Boolean = false,
    isConvertToTextMode: Boolean = false,
    speechToTextState: SpeechToTextState = SpeechToTextState.IDLE,
    speechToTextResult: SpeechToTextResult = SpeechToTextResult(),
    speechToTextError: String = "",
    onCancel: () -> Unit = {},
    onConvertToText: () -> Unit = {},
    onComplete: () -> Unit = {},
    onDismissSpeechResult: () -> Unit = {},
    onSendSpeechMessage: (String) -> Unit = {},
    speechToTextManager: SpeechToTextManager? = null
) {
    if (isRecording || speechToTextState != SpeechToTextState.IDLE) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(Color.Black.copy(alpha = 0.75f))
        ) {
            // 中间的语音波形气泡（录音时显示）
            if (isRecording) {
                Box(
                    modifier = Modifier
                        .align(Alignment.Center)
                        .offset(y = (-120).dp)
                ) {
                    VoiceRecordingBubble(
                        recordingDuration = recordingDuration,
                        volumeLevel = volumeLevel
                    )
                }

                // 底部的操作区域
                VoiceRecordingBottomActions(
                    isCancelMode = isCancelMode,
                    isConvertToTextMode = isConvertToTextMode,
                    onCancel = onCancel,
                    onConvertToText = onConvertToText,
                    onComplete = onComplete,
                    modifier = Modifier.align(Alignment.BottomCenter)
                )
            }

            // 语音转文字状态指示器（转换时显示）
            if (speechToTextState != SpeechToTextState.IDLE) {
                SpeechToTextOverlayIndicator(
                    state = speechToTextState,
                    result = speechToTextResult,
                    modifier = Modifier
                        .align(Alignment.Center)
                        .offset(y = if (isRecording) 60.dp else 0.dp)
                )
            }

            // 语音转文字结果卡片（完成或错误时显示）
            if (speechToTextState == SpeechToTextState.SUCCESS ||
                speechToTextState == SpeechToTextState.ERROR) {
                SpeechToTextResultCard(
                    result = speechToTextResult,
                    state = speechToTextState,
                    errorMessage = speechToTextError,
                    onDismiss = onDismissSpeechResult,
                    onSendMessage = onSendSpeechMessage,
                    modifier = Modifier.align(Alignment.Center),
                    speechToTextManager = speechToTextManager
                )
            }
        }
    }
}

@Composable
private fun VoiceRecordingBubble(
    recordingDuration: Long,
    volumeLevel: Float
) {
    // 现代极简风格语音卡片 - 固定尺寸
    Card(
        modifier = Modifier
            .width(192.dp) // 恢复固定宽度
            .wrapContentHeight(),
        shape = RoundedCornerShape(14.dp), // 24 * 0.6 = 14.4 ≈ 14
        colors = CardDefaults.cardColors(
            containerColor = Color(0xFF74B5AF) // 语音录制气泡颜色
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = 10.dp // 16 * 0.6 = 9.6 ≈ 10
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(19.dp), // 32 * 0.6 = 19.2 ≈ 19
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            // 顶部图标 - 缩小
            Box(
                modifier = Modifier
                    .size(29.dp) // 48 * 0.6 = 28.8 ≈ 29
                    .clip(CircleShape)
                    .background(Color.White.copy(alpha = 0.2f)),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "🎤",
                    fontSize = 14.sp // 24 * 0.6 = 14.4 ≈ 14
                )
            }

            Spacer(modifier = Modifier.height(8.dp)) // 20 * 0.6 = 12

            // 现代波形动画 - 固定宽度
            ElegantVoiceWaveAnimation(volumeLevel = volumeLevel)

            Spacer(modifier = Modifier.height(6.dp)) // 20 * 0.6 = 12

            // 录音时长 - 更优雅的显示，缩小
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Box(
                    modifier = Modifier
                        .size(5.dp) // 8 * 0.6 = 4.8 ≈ 5
                        .clip(CircleShape)
                        .background(Color.Red)
                )

                Spacer(modifier = Modifier.width(5.dp)) // 8 * 0.6 = 4.8 ≈ 5

                Text(
                    text = formatDuration(recordingDuration),
                    color = Color.White,
                    fontSize = 11.sp, // 18 * 0.6 = 10.8 ≈ 11
                    fontWeight = FontWeight.Medium
                )
            }
        }
    }
}

@Composable
private fun ElegantVoiceWaveAnimation(volumeLevel: Float) {
    val animatedProgress by rememberInfiniteTransition(label = "wave_animation").animateFloat(
        initialValue = 0f,
        targetValue = 1f,
        animationSpec = infiniteRepeatable(
            animation = tween(1500, easing = LinearEasing),
            repeatMode = RepeatMode.Restart
        ),
        label = "wave_progress"
    )

    Canvas(
        modifier = Modifier
            .width(120.dp) // 恢复固定宽度
            .height(24.dp) // 40 * 0.6 = 24
    ) {
        val barCount = 15 // 保持条形数量不变
        val totalSpacing = size.width * 0.3f
        val barWidth = (size.width - totalSpacing) / barCount
        val spacing = totalSpacing / (barCount - 1)

        val maxHeight = size.height * 0.8f
        val minHeight = size.height * 0.2f

        for (i in 0 until barCount) {
            val x = i * (barWidth + spacing)

            // 创建优雅的波形效果 - 中心高，两边低
            val centerDistance = abs(i - barCount / 2f) / (barCount / 2f)
            val centerEffect = 1f - centerDistance * 0.3f

            val waveOffset = sin((animatedProgress * 2 * PI + i * 0.4).toFloat()) * 0.5f + 0.5f
            val volumeEffect = (volumeLevel * 2f).coerceIn(0.3f, 1f)
            val barHeight = minHeight + (maxHeight - minHeight) * waveOffset * volumeEffect * centerEffect

            // 绘制圆润的条形 - 使用渐变效果
            drawRoundRect(
                color = Color.White.copy(alpha = 0.9f),
                topLeft = Offset(x, (size.height - barHeight) / 2),
                size = Size(barWidth, barHeight),
                cornerRadius = CornerRadius(barWidth / 2, barWidth / 2)
            )
        }
    }
}

@Composable
private fun VoiceRecordingBottomActions(
    isCancelMode: Boolean,
    isConvertToTextMode: Boolean,
    onCancel: () -> Unit,
    onConvertToText: () -> Unit,
    onComplete: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(bottom = 40.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // 现代化的三个操作按钮
        Row(
            horizontalArrangement = Arrangement.spacedBy(60.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 取消按钮 - 现代化设计
            ModernActionButton(
                icon = "✕",
                isSelected = isCancelMode,
                selectedColor = Color(0xFFC75D5D), // 取消颜色
                onClick = onCancel
            )

            // 中央录音按钮 - 只在发送状态时变色
            Box(
                modifier = Modifier
                    .size(
                        // 只在发送状态时变大，否则和左右按钮一致
                        if (!isCancelMode && !isConvertToTextMode) 72.dp else 64.dp
                    )
                    .clip(CircleShape)
                    .background(
                        // 只在发送状态时变成青绿色，否则保持白色半透明
                        if (!isCancelMode && !isConvertToTextMode) {
                            Brush.radialGradient(
                                colors = listOf(
                                    Color(0xFF74B5AF),
                                    Color(0xFF5A9A94)
                                )
                            )
                        } else {
                            Brush.radialGradient(
                                colors = listOf(
                                    Color.White.copy(alpha = 0.2f),
                                    Color.White.copy(alpha = 0.1f)
                                )
                            )
                        }
                    )
                    .clickable { onComplete() },
                contentAlignment = Alignment.Center
            ) {
                Box(
                    modifier = Modifier
                        .size(24.dp)
                        .clip(CircleShape)
                        .background(Color.White)
                )
            }

            // 转文字按钮 - 现代化设计
            ModernActionButton(
                icon = "文",
                isSelected = isConvertToTextMode,
                selectedColor = Color(0xFF5C93D1), // 转文字颜色
                onClick = onConvertToText
            )
        }

        Spacer(modifier = Modifier.height(40.dp))

        // 底部优雅的指示器
        ElegantBottomIndicator()
    }
}

@Composable
private fun ModernActionButton(
    icon: String,
    isSelected: Boolean,
    selectedColor: Color,
    onClick: () -> Unit
) {
    Box(
        modifier = Modifier
            .size(if (isSelected) 72.dp else 64.dp)
            .clip(CircleShape)
            .background(
                if (isSelected) selectedColor
                else Color.White.copy(alpha = 0.2f)
            )
            .clickable { onClick() },
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = icon,
            color = Color.White,
            fontSize = if (isSelected) 28.sp else 24.sp,
            fontWeight = FontWeight.Bold
        )
    }
}

@Composable
private fun ElegantBottomIndicator() {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .height(60.dp)
    ) {
        Canvas(
            modifier = Modifier.fillMaxSize()
        ) {
            val centerX = size.width / 2
            val centerY = size.height + 20.dp.toPx()
            val radius = size.width * 0.35f

            // 绘制优雅的弧形
            drawArc(
                color = Color.White.copy(alpha = 0.3f),
                startAngle = 45f,
                sweepAngle = 90f,
                useCenter = false,
                topLeft = Offset(centerX - radius, centerY - radius),
                size = Size(radius * 2, radius * 2),
                style = Stroke(width = 3.dp.toPx(), cap = StrokeCap.Round)
            )
        }

        // 中央麦克风指示器
        Box(
            modifier = Modifier
                .align(Alignment.Center)
                .offset(y = (-15).dp)
                .size(32.dp)
                .clip(CircleShape)
                .background(Color.White.copy(alpha = 0.15f)),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = "🎤",
                fontSize = 16.sp
            )
        }
    }
}



@Composable
private fun VoiceWaveAnimation(volumeLevel: Float) {
    val density = LocalDensity.current
    val animationSpec = infiniteRepeatable<Float>(
        animation = tween(800, easing = LinearEasing),
        repeatMode = RepeatMode.Restart
    )

    val animatedProgress by rememberInfiniteTransition(label = "wave").animateFloat(
        initialValue = 0f,
        targetValue = 1f,
        animationSpec = animationSpec,
        label = "wave_progress"
    )

    Canvas(
        modifier = Modifier.size(width = 120.dp, height = 32.dp)
    ) {
        drawVoiceWave(
            progress = animatedProgress,
            volumeLevel = volumeLevel,
            size = size
        )
    }
}

@Composable
private fun SimpleVoiceWaveAnimation(
    volumeLevel: Float,
    modifier: Modifier = Modifier
) {
    val infiniteTransition = rememberInfiniteTransition(label = "waveAnimation")

    // 创建简洁的波形条
    val waveCount = 12
    val waveHeights = (0 until waveCount).map { index ->
        infiniteTransition.animateFloat(
            initialValue = 0.3f,
            targetValue = 1f,
            animationSpec = infiniteRepeatable(
                animation = tween(
                    durationMillis = 600 + (index * 40),
                    easing = EaseInOutSine
                ),
                repeatMode = RepeatMode.Reverse
            ),
            label = "waveHeight$index"
        )
    }

    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.spacedBy(2.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        waveHeights.forEachIndexed { index, animatedHeight ->
            val baseHeight = 3.dp
            val maxHeight = 20.dp
            val currentHeight = baseHeight + (maxHeight - baseHeight) *
                (animatedHeight.value * (0.4f + volumeLevel * 0.6f))

            // 简洁的白色波形条
            Box(
                modifier = Modifier
                    .width(2.dp)
                    .height(currentHeight)
                    .background(
                        color = Color.White,
                        shape = RoundedCornerShape(1.dp)
                    )
            )
        }
    }
}

private fun DrawScope.drawVoiceWave(
    progress: Float,
    volumeLevel: Float,
    size: androidx.compose.ui.geometry.Size
) {
    val centerX = size.width / 2
    val centerY = size.height / 2
    val baseHeight = 4.dp.toPx()
    val maxHeight = size.height * 0.8f

    // 绘制多个波形条，类似微信的样式
    val barCount = 12
    val barWidth = 2.dp.toPx()
    val barSpacing = 2.5.dp.toPx()
    val totalWidth = barCount * barWidth + (barCount - 1) * barSpacing
    val startX = centerX - totalWidth / 2

    for (i in 0 until barCount) {
        val x = startX + i * (barWidth + barSpacing) + barWidth / 2

        // 计算每个条的高度，基于音量级别和动画进度
        val heightMultiplier = 0.2f + 0.8f * volumeLevel *
                (0.4f + 0.6f * sin(progress * 2 * Math.PI + i * 0.6).toFloat())
        val animatedHeight = baseHeight + (maxHeight - baseHeight) * heightMultiplier

        val top = centerY - animatedHeight / 2
        val bottom = centerY + animatedHeight / 2

        drawLine(
            color = Color.White,
            start = Offset(x, top),
            end = Offset(x, bottom),
            strokeWidth = barWidth,
            cap = StrokeCap.Round
        )
    }
}



/**
 * 增强版语音录制覆盖层，支持拖拽手势
 */
@Composable
fun EnhancedVoiceRecordingOverlay(
    isRecording: Boolean,
    recordingDuration: Long,
    volumeLevel: Float,
    speechToTextState: SpeechToTextState = SpeechToTextState.IDLE,
    speechToTextResult: SpeechToTextResult = SpeechToTextResult(),
    speechToTextError: String = "",
    onCancel: () -> Unit = {},
    onConvertToText: () -> Unit = {},
    onComplete: () -> Unit = {},
    onDismissSpeechResult: () -> Unit = {},
    onSendSpeechMessage: (String) -> Unit = {},
    speechToTextManager: SpeechToTextManager? = null
) {
    var dragOffset by remember { mutableStateOf(Offset.Zero) }
    var isCancelMode by remember { mutableStateOf(false) }
    var isConvertToTextMode by remember { mutableStateOf(false) }

    if (isRecording || speechToTextState != SpeechToTextState.IDLE) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(Color.Black.copy(alpha = 0.75f))
                .pointerInput(Unit) {
                    if (isRecording) {
                        detectDragGestures(
                            onDragEnd = {
                                when {
                                    isCancelMode -> onCancel()
                                    isConvertToTextMode -> onConvertToText()
                                    else -> onComplete()
                                }
                                // 重置状态
                                dragOffset = Offset.Zero
                                isCancelMode = false
                                isConvertToTextMode = false
                            }
                        ) { change, _ ->
                            dragOffset = change.position

                            // 检测是否在取消区域
                            isCancelMode = dragOffset.x < size.width * 0.3f && dragOffset.y > size.height * 0.7f

                            // 检测是否在转文字区域
                            isConvertToTextMode = dragOffset.x > size.width * 0.7f && dragOffset.y > size.height * 0.7f
                        }
                    }
                }
        ) {
            // 中间的语音波形气泡（录音时显示）
            if (isRecording) {
                Box(
                    modifier = Modifier
                        .align(Alignment.Center)
                        .offset(y = (-120).dp)
                ) {
                    VoiceRecordingBubble(
                        recordingDuration = recordingDuration,
                        volumeLevel = volumeLevel
                    )
                }

                // 底部的操作区域 - 微信风格半圆弧设计
                WeChatStyleBottomActions(
                    isCancelMode = isCancelMode,
                    isConvertToTextMode = isConvertToTextMode,
                    onCancel = onCancel,
                    onConvertToText = onConvertToText,
                    onComplete = onComplete,
                    modifier = Modifier.align(Alignment.BottomCenter)
                )

                // 拖拽提示
                if (dragOffset != Offset.Zero) {
                    DragIndicator(
                        dragOffset = dragOffset,
                        isCancelMode = isCancelMode,
                        isConvertToTextMode = isConvertToTextMode
                    )
                }
            }

            // 语音转文字状态指示器（转换时显示）
            if (speechToTextState != SpeechToTextState.IDLE) {
                SpeechToTextOverlayIndicator(
                    state = speechToTextState,
                    result = speechToTextResult,
                    modifier = Modifier
                        .align(Alignment.Center)
                        .offset(y = if (isRecording) 60.dp else 0.dp)
                )
            }

            // 语音转文字结果卡片（完成或错误时显示）
            if (speechToTextState == SpeechToTextState.SUCCESS ||
                speechToTextState == SpeechToTextState.ERROR) {
                SpeechToTextResultCard(
                    result = speechToTextResult,
                    state = speechToTextState,
                    errorMessage = speechToTextError,
                    onDismiss = onDismissSpeechResult,
                    onSendMessage = onSendSpeechMessage,
                    modifier = Modifier.align(Alignment.Center),
                    speechToTextManager = speechToTextManager
                )
            }
        }
    }
}

/**
 * 拖拽指示器
 */
@Composable
private fun DragIndicator(
    dragOffset: Offset,
    isCancelMode: Boolean,
    isConvertToTextMode: Boolean
) {
    Box(
        modifier = Modifier
            .offset(
                x = (dragOffset.x / LocalDensity.current.density).dp,
                y = (dragOffset.y / LocalDensity.current.density).dp
            )
            .size(60.dp)
            .clip(RoundedCornerShape(30.dp))
            .background(
                when {
                    isCancelMode -> Color(0xFFC75D5D).copy(alpha = 0.8f)
                    isConvertToTextMode -> Color(0xFF5C93D1).copy(alpha = 0.8f)
                    else -> Color(0xFF74B5AF).copy(alpha = 0.6f)
                }
            ),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = when {
                isCancelMode -> "取消"
                isConvertToTextMode -> "转文字"
                else -> "录音"
            },
            color = Color.White,
            fontSize = 14.sp,
            fontWeight = FontWeight.Medium
        )
    }
}

/**
 * 微信风格底部操作区域 - 135度圆弧设计
 */
@Composable
private fun WeChatStyleBottomActions(
    isCancelMode: Boolean,
    isConvertToTextMode: Boolean,
    onCancel: () -> Unit,
    onConvertToText: () -> Unit,
    onComplete: () -> Unit,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .fillMaxWidth()
            .height(200.dp)
    ) {
        // 上方两个按钮：取消 和 转文字 - 避免与圆弧重叠
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 50.dp)
                .align(Alignment.TopCenter)
                .padding(top = 20.dp),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            // 取消按钮
            Box(
                modifier = Modifier
                    .clip(RoundedCornerShape(25.dp))
                    .background(
                        if (isCancelMode) Color(0xFF666666)
                        else Color(0xFF999999)
                    )
                    .clickable { onCancel() }
                    .padding(horizontal = 28.dp, vertical = 14.dp),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "取消",
                    color = Color.White,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium
                )
            }

            // 转文字按钮
            Box(
                modifier = Modifier
                    .clip(RoundedCornerShape(25.dp))
                    .background(
                        if (isConvertToTextMode) Color(0xFF666666)
                        else Color(0xFF999999)
                    )
                    .clickable { onConvertToText() }
                    .padding(horizontal = 20.dp, vertical = 14.dp),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "滑到这里 转文字",
                    color = Color.White,
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium
                )
            }
        }

        // 下方的135度圆弧发送区域
        Box(
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .fillMaxWidth()
                .height(140.dp)
        ) {
            // 135度圆弧背景
            Canvas(
                modifier = Modifier
                    .fillMaxSize()
                    .clickable { onComplete() }
            ) {
                val centerX = size.width / 2
                val centerY = size.height + 80.dp.toPx() // 圆心在底部下方
                val radius = size.width * 0.8f

                // 135度圆弧：以底部为中心，向两侧各扩展67.5度
                // 90度是底部中心，向左右各扩展67.5度
                val startAngle = 90f - 67.5f  // 22.5度（右下）
                val sweepAngle = 135f          // 扫描135度到157.5度（左下）

                // 绘制填充的圆弧扇形
                drawArc(
                    color = Color(0xFFE5E5E5),
                    startAngle = startAngle,
                    sweepAngle = sweepAngle,
                    useCenter = true,
                    topLeft = Offset(centerX - radius, centerY - radius),
                    size = Size(radius * 2, radius * 2)
                )

                // 添加边框让圆弧更明显
                drawArc(
                    color = Color(0xFFBBBBBB),
                    startAngle = startAngle,
                    sweepAngle = sweepAngle,
                    useCenter = false,
                    topLeft = Offset(centerX - radius, centerY - radius),
                    size = Size(radius * 2, radius * 2),
                    style = Stroke(width = 3.dp.toPx())
                )
            }

            // 发送文字
            Text(
                text = "松开 发送",
                color = Color.Black,
                fontSize = 18.sp,
                fontWeight = FontWeight.Medium,
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .offset(y = (-30).dp) // 向上偏移到圆弧可见区域
            )
        }
    }
}

/**
 * 绘制半圆弧背景
 */
private fun DrawScope.drawSemiCircleBackground(
    size: Size
) {
    val centerX = size.width / 2
    val centerY = size.height
    val radius = size.width * 0.35f

    // 绘制半圆弧背景
    val rect = Rect(
        left = centerX - radius,
        top = centerY - radius,
        right = centerX + radius,
        bottom = centerY + radius
    )

    drawArc(
        color = Color.White.copy(alpha = 0.9f),
        startAngle = 180f,
        sweepAngle = 180f,
        useCenter = true,
        topLeft = rect.topLeft,
        size = Size(rect.width, rect.height)
    )

    // 绘制半圆弧边框
    drawArc(
        color = Color.Gray.copy(alpha = 0.3f),
        startAngle = 180f,
        sweepAngle = 180f,
        useCenter = false,
        topLeft = rect.topLeft,
        size = Size(rect.width, rect.height),
        style = Stroke(width = 2.dp.toPx())
    )
}

/**
 * 格式化录音时长显示
 */
private fun formatDuration(seconds: Long): String {
    val minutes = seconds / 60
    val remainingSeconds = seconds % 60
    return String.format("%02d:%02d", minutes, remainingSeconds)
}

/**
 * 智能格式化语音消息时长显示
 * 针对不同时长范围使用不同的显示格式
 */
private fun formatVoiceDuration(durationMs: Long): String {
    val totalSeconds = durationMs / 1000.0

    return when {
        // 小于1秒：显示为 "1"" (最少显示1秒)
        totalSeconds < 1.0 -> "1\""

        // 1-59秒：显示为 "X""
        totalSeconds < 60.0 -> "${totalSeconds.toInt()}\""

        // 1-9分钟：显示为 "X'XX""
        totalSeconds < 600.0 -> {
            val minutes = (totalSeconds / 60).toInt()
            val seconds = (totalSeconds % 60).toInt()
            "${minutes}'${seconds.toString().padStart(2, '0')}\""
        }

        // 10分钟以上：显示为 "XX'XX""
        else -> {
            val minutes = (totalSeconds / 60).toInt()
            val seconds = (totalSeconds % 60).toInt()
            "${minutes}'${seconds.toString().padStart(2, '0')}\""
        }
    }
}

// 根据语音时长计算气泡宽度的函数 - 更新时长范围
private fun calculateVoiceMessageWidth(duration: Long): androidx.compose.ui.unit.Dp {
    val durationInSeconds = duration / 1000f

    // 按照新需求精确计算宽度
    return when {
        // 0-3秒：100dp固定宽度
        durationInSeconds <= 3f -> 100.dp

        // 3-15秒：100dp → 145dp
        durationInSeconds <= 15f -> {
            val progress = ((durationInSeconds - 3f) / 12f).coerceIn(0f, 1f)
            100.dp + (45.dp * progress)
        }

        // 15-45秒：145dp → 210dp
        durationInSeconds <= 45f -> {
            val progress = ((durationInSeconds - 15f) / 30f).coerceIn(0f, 1f)
            145.dp + (65.dp * progress)
        }

        // 45秒-1.5分钟：210dp → 240dp
        durationInSeconds <= 90f -> {
            val progress = ((durationInSeconds - 45f) / 45f).coerceIn(0f, 1f)
            210.dp + (30.dp * progress)
        }

        // 1.5-2分钟：240dp → 280dp
        durationInSeconds <= 120f -> {
            val progress = ((durationInSeconds - 90f) / 30f).coerceIn(0f, 1f)
            240.dp + (40.dp * progress)
        }

        // 2分钟以上：280dp固定最大宽度
        else -> 280.dp
    }
}

/**
 * 语音消息气泡组件
 */
@Composable
fun VoiceMessageBubble(
    duration: Long,
    isPlaying: Boolean,
    isFromUser: Boolean,
    onPlayClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    // 根据时长计算动态宽度
    val bubbleWidth = calculateVoiceMessageWidth(duration)

    Row(
        modifier = modifier
                    //.height(40.dp) // 恢复固定高度
            .wrapContentHeight() // 改为自适应高度，与普通气泡一致
            .width(bubbleWidth) // 应用动态宽度
            .clip(RoundedCornerShape(12.dp))
            .background(
                if (isFromUser) Color(0xFFBADED9) else Color(0xFFE4E7EB)
            )
            .padding(horizontal = 0.dp, vertical = 2.dp), // 减小水平内边距，调整垂直内边距
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 播放按钮 - 减小左边距
        IconButton(
            onClick = onPlayClick,
            modifier = Modifier.size(24.dp) //减小按钮尺寸
        ) {
            Icon(
                painter = androidx.compose.ui.res.painterResource(
                    id = if (isPlaying) R.drawable.ic_pause else R.drawable.ic_play
                ),
                contentDescription = if (isPlaying) "暂停" else "播放",
                tint = Color(0xFF333333),
                modifier = Modifier.size(14.dp) //图标尺寸
            )
        }

        Spacer(modifier = Modifier.width(0.dp)) // 减小间距

        // 语音波形显示（播放时显示动画）- 精确计算动态宽度
        // 计算时长文本的精确宽度需求 - 减少空白
        val durationText = formatVoiceDuration(duration)
        val estimatedTextWidth = when {
            durationText.length <= 2 -> 16.dp  // "1"" - 减少空白
            durationText.length <= 3 -> 22.dp  // "45"" - 减少空白
            durationText.length <= 5 -> 28.dp  // "1'30"" - 减少空白
            else -> 34.dp  // "12'00"" - 减少空白
        }

        // 波形宽度 = 总宽度 - 播放按钮(24dp) - 时长文本宽度 - 最小边距
        val waveWidth = (bubbleWidth - 24.dp - estimatedTextWidth - 4.dp).coerceAtLeast(30.dp)

        if (isPlaying) {
            VoicePlayingWave(width = waveWidth)
        } else {
            VoiceStaticWave(width = waveWidth)
        }

        // 时长显示 - 精确宽度，减少空白
        Text(
            text = durationText,
            color = Color(0xFF666666),
            fontSize = 11.sp, // 保持紧凑字体
            fontWeight = FontWeight.Medium,
            modifier = Modifier
                .width(estimatedTextWidth)
                .padding(horizontal = 1.dp), // 最小内边距确保文字不贴边
            textAlign = TextAlign.Center // 居中对齐，减少左右空白
        )
    }
}

@Composable
private fun VoicePlayingWave(width: androidx.compose.ui.unit.Dp) {
    val animatedProgress by rememberInfiniteTransition(label = "playing_wave").animateFloat(
        initialValue = 0f,
        targetValue = 1f,
        animationSpec = infiniteRepeatable(
            animation = tween(800, easing = LinearEasing),
            repeatMode = RepeatMode.Restart
        ),
        label = "playing_progress"
    )

    Canvas(modifier = Modifier.size(width = width, height = 14.dp)) {
        drawPlayingWave(animatedProgress, size)
    }
}

@Composable
private fun VoiceStaticWave(width: androidx.compose.ui.unit.Dp) {
    Canvas(modifier = Modifier.size(width = width, height = 14.dp)) {
        drawStaticWave(size)
    }
}



private fun DrawScope.drawStaticWave(size: androidx.compose.ui.geometry.Size) {
    val barWidth = 1.5.dp.toPx()
    val barSpacing = 2.5.dp.toPx()
    val totalBarWidth = barWidth + barSpacing

    // 根据宽度动态计算条形数量
    val barCount = ((size.width - barSpacing) / totalBarWidth).toInt().coerceAtLeast(3)

    val centerY = size.height / 2
    val maxHeight = size.height * 0.7f

    // 生成足够的高度模式
    val baseHeights = listOf(0.4f, 0.8f, 0.6f, 0.9f, 0.5f, 0.7f, 0.3f, 0.85f, 0.65f, 0.75f)

    for (i in 0 until barCount) {
        val x = i * totalBarWidth + barWidth / 2
        val height = maxHeight * baseHeights[i % baseHeights.size]

        val top = centerY - height / 2
        val bottom = centerY + height / 2

        drawLine(
            color = Color(0xFF999999),
            start = Offset(x, top),
            end = Offset(x, bottom),
            strokeWidth = barWidth,
            cap = StrokeCap.Round
        )
    }
}

private fun DrawScope.drawPlayingWave(
    progress: Float,
    size: androidx.compose.ui.geometry.Size
) {
    val barWidth = 1.5.dp.toPx()
    val barSpacing = 2.5.dp.toPx()
    val totalBarWidth = barWidth + barSpacing

    // 根据宽度动态计算条形数量
    val barCount = ((size.width - barSpacing) / totalBarWidth).toInt().coerceAtLeast(3)

    val centerY = size.height / 2
    val maxHeight = size.height * 0.7f

    for (i in 0 until barCount) {
        val x = i * totalBarWidth + barWidth / 2
        val animatedHeight = maxHeight * (0.3f + 0.7f * sin(progress * 2 * Math.PI + i * 0.5).toFloat())

        val top = centerY - animatedHeight / 2
        val bottom = centerY + animatedHeight / 2

        drawLine(
            color = Color(0xFF74B5AF),
            start = Offset(x, top),
            end = Offset(x, bottom),
            strokeWidth = barWidth,
            cap = StrokeCap.Round
        )
    }
}
